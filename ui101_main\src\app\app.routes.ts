import { Routes, CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { Auth, authState } from '@angular/fire/auth';
import { map, take } from 'rxjs/operators';

export const authGuard: CanActivateFn = () => {
  const auth = inject(Auth), router = inject(Router);
  return authState(auth).pipe(
    take(1),
    map(u => {
      console.log('AuthGuard: User state:', u);
      return u ? true : router.createUrlTree(['/login']);
    })
  );
};

export const redirectLoggedInGuard: CanActivateFn = () => {
  const auth = inject(Auth), router = inject(Router);
  return authState(auth).pipe(
    take(1),
    map(u => {
      console.log('RedirectLoggedInGuard: User state:', u);
      return u ? router.createUrlTree(['/projects']) : true;
    })
  );
};

export const routes: Routes = [
  { path: 'login', canActivate: [redirectLoggedInGuard], loadComponent: () => import('./login/login.component').then(m => m.LoginComponent) },
  { path: 'projects', canActivate: [authGuard], loadComponent: () => import('./projects/projects.page').then(m => m.ProjectsPage) },
  { path: '**', redirectTo: 'projects' }
];
