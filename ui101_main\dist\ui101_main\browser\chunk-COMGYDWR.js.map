{"version": 3, "sources": ["src/app/services/projects.service.ts", "node_modules/carbon-components-angular/fesm2020/carbon-components-angular-link.mjs", "node_modules/carbon-components-angular/fesm2020/carbon-components-angular-tiles.mjs", "src/app/components/project-card.component.ts", "src/app/projects/projects.page.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\nimport { Firestore, collection, query, orderBy, limit, collectionData, doc, setDoc, serverTimestamp } from '@angular/fire/firestore';\nimport { Auth } from '@angular/fire/auth';\nimport { authState } from '@angular/fire/auth';\nimport { switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\n\nexport type ProjectType = 'CONCRETE' | 'FINISHES' | 'DRYWALL';\n\nexport interface Project {\n  id: string;\n  title: string;\n  type: ProjectType;\n  ownerUid: string;\n  createdAt: any;   // Firestore Timestamp\n  updatedAt: any;   // Firestore Timestamp\n}\n\n@Injectable({ providedIn: 'root' })\nexport class ProjectsService {\n  private db = inject(Firestore);\n  private auth = inject(Auth);\n\n  /** Realtime stream of the current user's projects (ordered by updatedAt desc, limited to 5) */\n  projects$: Observable<Project[]> = authState(this.auth).pipe(\n    switchMap(user => {\n      if (!user) return of([]); // not signed in\n      const col = collection(this.db, `users/${user.uid}/projects`);\n      const q = query(col, orderBy('updatedAt', 'desc'), limit(5));\n      // idField echoes doc.id into 'id' so it matches our rules (id == projectId)\n      return collectionData(q, { idField: 'id' }).pipe(map(r => r as Project[]));\n    })\n  );\n\n  /** Create a project with auto-ID and echo id into the document */\n  async createProject(title: string, type: ProjectType) {\n    const user = this.auth.currentUser;\n    if (!user) throw new Error('Not signed in');\n\n    const col = collection(this.db, `users/${user.uid}/projects`);\n    const ref = doc(col); // auto-ID\n    const data: Project = {\n      id: ref.id,\n      title: title.trim(),\n      type,\n      ownerUid: user.uid,\n      createdAt: serverTimestamp() as any,\n      updatedAt: serverTimestamp() as any\n    };\n    await setDoc(ref, data);\n    return ref.id;\n  }\n}\n", "import * as i0 from '@angular/core';\nimport { Directive, HostBinding, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n/**\n * A convenience directive for applying styling to a link. Get started with importing the module:\n *\n * ```typescript\n * import { LinkModule } from 'carbon-components-angular';\n * ```\n *\n * ```html\n * <a href=\"#\" cdsLink>A link</a>\n * ```\n *\n * See the [vanilla carbon docs](http://www.carbondesignsystem.com/components/link/code) for more detail.\n *\n * [See demo](../../?path=/story/components-link--basic)\n */\nclass Link {\n  constructor() {\n    this.baseClass = true;\n    /**\n     * Set to true to show links inline in a sentence or paragraph.\n     */\n    this.inline = false;\n  }\n  /**\n   * Set to true to disable link.\n   */\n  set disabled(disabled) {\n    this._disabled = disabled;\n    this.tabindex = this.disabled ? -1 : null;\n  }\n  get disabled() {\n    return this._disabled;\n  }\n}\nLink.ɵfac = function Link_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || Link)();\n};\nLink.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Link,\n  selectors: [[\"\", \"cdsLink\", \"\"], [\"\", \"ibmLink\", \"\"]],\n  hostVars: 8,\n  hostBindings: function Link_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.tabindex)(\"aria-disabled\", ctx.disabled);\n      i0.ɵɵclassProp(\"cds--link\", ctx.baseClass)(\"cds--link--inline\", ctx.inline)(\"cds--link--disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    inline: \"inline\",\n    disabled: \"disabled\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Link, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsLink], [ibmLink]\"\n    }]\n  }], null, {\n    baseClass: [{\n      type: HostBinding,\n      args: [\"class.cds--link\"]\n    }],\n    tabindex: [{\n      type: HostBinding,\n      args: [\"attr.tabindex\"]\n    }],\n    inline: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"class.cds--link--inline\"]\n    }],\n    disabled: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"attr.aria-disabled\"]\n    }, {\n      type: HostBinding,\n      args: [\"class.cds--link--disabled\"]\n    }]\n  });\n})();\nclass LinkIconDirective {\n  constructor() {\n    this.iconClass = true;\n  }\n}\nLinkIconDirective.ɵfac = function LinkIconDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || LinkIconDirective)();\n};\nLinkIconDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: LinkIconDirective,\n  selectors: [[\"\", \"ibmLinkIcon\", \"\"], [\"\", \"cdsLinkIcon\", \"\"]],\n  hostVars: 2,\n  hostBindings: function LinkIconDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--link__icon\", ctx.iconClass);\n    }\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LinkIconDirective, [{\n    type: Directive,\n    args: [{\n      selector: \"[ibmLinkIcon], [cdsLinkIcon]\"\n    }]\n  }], null, {\n    iconClass: [{\n      type: HostBinding,\n      args: [\"class.cds--link__icon\"]\n    }]\n  });\n})();\nclass LinkModule {}\nLinkModule.ɵfac = function LinkModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || LinkModule)();\n};\nLinkModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LinkModule,\n  declarations: [Link, LinkIconDirective],\n  imports: [CommonModule],\n  exports: [Link, LinkIconDirective]\n});\nLinkModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LinkModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [Link, LinkIconDirective],\n      exports: [Link, LinkIconDirective],\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Link, LinkIconDirective, LinkModule };\n", "import * as i0 from '@angular/core';\nimport { Directive, HostBinding, EventEmitter, Component, Optional, Input, Output, ViewChild, HostListener, TemplateRef, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'carbon-components-angular/link';\nimport { LinkModule } from 'carbon-components-angular/link';\nimport { merge } from 'carbon-components-angular/utils';\nimport * as i1$1 from 'carbon-components-angular/i18n';\nimport { I18nModule } from 'carbon-components-angular/i18n';\nimport * as i3$1 from 'carbon-components-angular/icon';\nimport { IconModule } from 'carbon-components-angular/icon';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nconst _c1 = (a0, a1) => ({\n  \"cds--tile--light\": a0,\n  \"cds--tile--disabled cds--link--disabled\": a1\n});\nconst _c2 = [\"container\"];\nconst _c3 = [[[\"\", \"cdsAboveFold\", \"\"], [\"\", \"ibmAboveFold\", \"\"], [\"\", 8, \"cds--tile-content__above-the-fold\"]], [[\"\", \"cdsBelowFold\", \"\"], [\"\", \"ibmBelowFold\", \"\"], [\"\", 8, \"cds--tile-content__below-the-fold\"]]];\nconst _c4 = [\"[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold\", \"[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold\"];\nconst _c5 = (a0, a1) => ({\n  \"cds--tile--is-expanded\": a0,\n  \"cds--tile--light\": a1\n});\nconst _c6 = a0 => ({\n  \"max-height\": a0\n});\nfunction ExpandableTile_button_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExpandableTile_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"click\", function ExpandableTile_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick());\n    });\n    i0.ɵɵtemplate(2, ExpandableTile_button_0_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const expandableTileContent_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c5, ctx_r1.expanded, ctx_r1.theme === \"light\"))(\"ngStyle\", i0.ɵɵpureFunction1(10, _c6, ctx_r1.expandedHeight + \"px\"));\n    i0.ɵɵattribute(\"aria-expanded\", ctx_r1.expanded)(\"title\", i0.ɵɵpipeBind1(1, 5, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", expandableTileContent_r3);\n  }\n}\nfunction ExpandableTile_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExpandableTile_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtemplate(2, ExpandableTile_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const expandableTileContent_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c5, ctx_r1.expanded, ctx_r1.theme === \"light\"))(\"ngStyle\", i0.ɵɵpureFunction1(9, _c6, ctx_r1.expandedHeight + \"px\"));\n    i0.ɵɵattribute(\"title\", i0.ɵɵpipeBind1(1, 4, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", expandableTileContent_r3);\n  }\n}\nfunction ExpandableTile_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"svg\", 8);\n  }\n}\nfunction ExpandableTile_ng_template_4_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExpandableTile_ng_template_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, ExpandableTile_ng_template_4_div_4_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const chevronIcon_r4 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chevronIcon_r4);\n  }\n}\nfunction ExpandableTile_ng_template_4_button_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ExpandableTile_ng_template_4_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"click\", function ExpandableTile_ng_template_4_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClick());\n    });\n    i0.ɵɵtemplate(2, ExpandableTile_ng_template_4_button_5_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const chevronIcon_r4 = i0.ɵɵreference(3);\n    i0.ɵɵattribute(\"aria-expanded\", ctx_r1.expanded)(\"aria-label\", i0.ɵɵpipeBind1(1, 3, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chevronIcon_r4);\n  }\n}\nfunction ExpandableTile_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", null, 2)(2, \"div\", 9);\n    i0.ɵɵprojection(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ExpandableTile_ng_template_4_div_4_Template, 2, 1, \"div\", 10)(5, ExpandableTile_ng_template_4_button_5_Template, 3, 5, \"button\", 11);\n    i0.ɵɵelementStart(6, \"div\", 9);\n    i0.ɵɵprojection(7, 1);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.interactive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.interactive);\n  }\n}\nconst _c7 = [\"input\"];\nconst _c8 = (a0, a1, a2) => ({\n  \"cds--tile--is-selected\": a0,\n  \"cds--tile--light\": a1,\n  \"cds--tile--disabled\": a2\n});\nfunction SelectionTile__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"svg\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdsIcon\", ctx_r1.multiple ? \"checkbox\" : \"checkmark\");\n  }\n}\nfunction SelectionTile_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"svg\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdsIcon\", ctx_r1.multiple ? \"checkbox--checked--filled\" : \"checkmark--filled\");\n  }\n}\nconst _c9 = [[[\"ibm-selection-tile\"], [\"cds-selection-tile\"]]];\nconst _c10 = [\"ibm-selection-tile,cds-selection-tile\"];\nfunction TileGroup_legend_1_1_ng_template_0_Template(rf, ctx) {}\nfunction TileGroup_legend_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TileGroup_legend_1_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.legend);\n  }\n}\nfunction TileGroup_legend_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate(ctx_r0.legend);\n  }\n}\nfunction TileGroup_legend_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"legend\", 2);\n    i0.ɵɵtemplate(1, TileGroup_legend_1_1_Template, 1, 1, null, 3)(2, TileGroup_legend_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const legendLabel_r2 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTemplate(ctx_r0.legend))(\"ngIfElse\", legendLabel_r2);\n  }\n}\nclass ClickableTileIconDirective {\n  constructor() {\n    this.icon = true;\n  }\n}\nClickableTileIconDirective.ɵfac = function ClickableTileIconDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ClickableTileIconDirective)();\n};\nClickableTileIconDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ClickableTileIconDirective,\n  selectors: [[\"\", \"cdsClickableTileIcon\", \"\"], [\"\", \"ibmClickableTileIcon\", \"\"]],\n  hostVars: 2,\n  hostBindings: function ClickableTileIconDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tile--icon\", ctx.icon);\n    }\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickableTileIconDirective, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsClickableTileIcon], [ibmClickableTileIcon]\"\n    }]\n  }], null, {\n    icon: [{\n      type: HostBinding,\n      args: [\"class.cds--tile--icon\"]\n    }]\n  });\n})();\n\n/**\n * Build application's clickable tiles using this component. Get started with importing the module:\n *\n * ```typescript\n * import { TilesModule } from 'carbon-components-angular';\n * ```\n *\n * ```html\n * <cds-clickable-tile>\n * \t\ttile content\n * </cds-clickable-tile>\n * ```\n */\nclass ClickableTile {\n  constructor(router) {\n    this.router = router;\n    /**\n     * @deprecated since v5 - Use `cdsLayer` directive instead\n     * Set to `\"light\"` to apply the light style\n     */\n    this.theme = \"dark\";\n    /**\n     * Sets the `href` attribute on the `cds-clickable-tile` element.\n     */\n    this.href = \"#\";\n    /**\n     * Set to `true` to disable the clickable tile.\n     */\n    this.disabled = false;\n    /**\n     * Emits the navigation status promise when the link is activated\n     */\n    this.navigation = new EventEmitter();\n  }\n  navigate(event) {\n    if (this.router && this.route && !this.disabled) {\n      event.preventDefault();\n      const status = this.router.navigate(this.route, this.routeExtras);\n      this.navigation.emit(status);\n    }\n  }\n}\nClickableTile.ɵfac = function ClickableTile_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ClickableTile)(i0.ɵɵdirectiveInject(i1.Router, 8));\n};\nClickableTile.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ClickableTile,\n  selectors: [[\"cds-clickable-tile\"], [\"ibm-clickable-tile\"]],\n  inputs: {\n    theme: \"theme\",\n    href: \"href\",\n    target: \"target\",\n    rel: \"rel\",\n    disabled: \"disabled\",\n    route: \"route\",\n    routeExtras: \"routeExtras\"\n  },\n  outputs: {\n    navigation: \"navigation\"\n  },\n  standalone: false,\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 8,\n  consts: [[\"cdsLink\", \"\", \"tabindex\", \"0\", 1, \"cds--tile\", \"cds--tile--clickable\", 3, \"click\", \"ngClass\"]],\n  template: function ClickableTile_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"a\", 0);\n      i0.ɵɵlistener(\"click\", function ClickableTile_Template_a_click_0_listener($event) {\n        return ctx.navigate($event);\n      });\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c1, ctx.theme === \"light\", ctx.disabled));\n      i0.ɵɵattribute(\"href\", ctx.disabled ? null : ctx.href, i0.ɵɵsanitizeUrl)(\"target\", ctx.target)(\"rel\", ctx.rel ? ctx.rel : null)(\"aria-disabled\", ctx.disabled);\n    }\n  },\n  dependencies: [i2.NgClass, i3.Link],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickableTile, [{\n    type: Component,\n    args: [{\n      selector: \"cds-clickable-tile, ibm-clickable-tile\",\n      template: `\n\t<a\n\t\tcdsLink\n\t\tclass=\"cds--tile cds--tile--clickable\"\n\t\t[ngClass]=\"{\n\t\t\t'cds--tile--light': theme === 'light',\n\t\t\t'cds--tile--disabled cds--link--disabled' : disabled\n\t\t}\"\n\t\ttabindex=\"0\"\n\t\t(click)=\"navigate($event)\"\n\t\t[attr.href]=\"disabled ? null : href\"\n\t\t[attr.target]=\"target\"\n\t\t[attr.rel]=\"rel ? rel : null\"\n\t\t[attr.aria-disabled]=\"disabled\">\n\t\t<ng-content></ng-content>\n\t</a>`\n    }]\n  }], function () {\n    return [{\n      type: i1.Router,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    theme: [{\n      type: Input\n    }],\n    href: [{\n      type: Input\n    }],\n    target: [{\n      type: Input\n    }],\n    rel: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    route: [{\n      type: Input\n    }],\n    routeExtras: [{\n      type: Input\n    }],\n    navigation: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { TilesModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-tiles-expandable--basic)\n */\nclass ExpandableTile {\n  constructor(i18n, element) {\n    this.i18n = i18n;\n    this.element = element;\n    /**\n     * @deprecated since v5 - Use `cdsLayer` directive instead\n     * Set to `\"light\"` to apply the light style\n     */\n    this.theme = \"dark\";\n    /**\n     * Controls the expanded state\n     */\n    this.expanded = false;\n    /**\n     * Controls the interactive state\n     */\n    this.interactive = false;\n    this.tileMaxHeight = 0;\n    this.currentExpandedHeight = 0;\n    this.expand = this.i18n.getOverridable(\"TILES.EXPAND\");\n    this.collapse = this.i18n.getOverridable(\"TILES.COLLAPSE\");\n  }\n  /**\n   * Expects an object that contains some or all of:\n   * ```\n   * {\n   *\t\t\"EXPAND\": \"Expand\",\n   *\t\t\"COLLAPSE\": \"Collapse\",\n   * }\n   * ```\n   */\n  set translations(value) {\n    const valueWithDefaults = merge(this.i18n.getMultiple(\"TILES\"), value);\n    this.expand.override(valueWithDefaults.EXPAND);\n    this.collapse.override(valueWithDefaults.COLLAPSE);\n  }\n  ngAfterViewInit() {\n    this.updateMaxHeight();\n  }\n  get expandedHeight() {\n    const tile = this.element.nativeElement.querySelector(\".cds--tile\");\n    const tilePadding = parseInt(getComputedStyle(tile).paddingBottom, 10) + parseInt(getComputedStyle(tile).paddingTop, 10);\n    const expandedHeight = this.tileMaxHeight + tilePadding;\n    if (!isNaN(expandedHeight)) {\n      this.currentExpandedHeight = expandedHeight;\n    }\n    return this.currentExpandedHeight;\n  }\n  updateMaxHeight() {\n    if (this.expanded) {\n      this.tileMaxHeight = this.tileContainer.nativeElement.getBoundingClientRect().height;\n    } else {\n      this.tileMaxHeight = this.element.nativeElement.querySelector(\".cds--tile-content__above-the-fold\").getBoundingClientRect().height;\n    }\n  }\n  onClick() {\n    this.expanded = !this.expanded;\n    this.updateMaxHeight();\n  }\n}\nExpandableTile.ɵfac = function ExpandableTile_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ExpandableTile)(i0.ɵɵdirectiveInject(i1$1.I18n), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nExpandableTile.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ExpandableTile,\n  selectors: [[\"cds-expandable-tile\"], [\"ibm-expandable-tile\"]],\n  viewQuery: function ExpandableTile_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c2, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tileContainer = _t.first);\n    }\n  },\n  inputs: {\n    theme: \"theme\",\n    expanded: \"expanded\",\n    interactive: \"interactive\",\n    translations: \"translations\"\n  },\n  standalone: false,\n  ngContentSelectors: _c4,\n  decls: 6,\n  vars: 2,\n  consts: [[\"chevronIcon\", \"\"], [\"expandableTileContent\", \"\"], [\"container\", \"\"], [\"class\", \"cds--tile cds--tile--expandable\", \"type\", \"button\", 3, \"ngClass\", \"ngStyle\", \"click\", 4, \"ngIf\"], [\"class\", \"cds--tile cds--tile--expandable cds--tile--expandable--interactive\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"cds--tile\", \"cds--tile--expandable\", 3, \"click\", \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\"], [1, \"cds--tile\", \"cds--tile--expandable\", \"cds--tile--expandable--interactive\", 3, \"ngClass\", \"ngStyle\"], [\"cdsIcon\", \"chevron--down\", \"size\", \"16\"], [1, \"cds--tile-content\"], [\"class\", \"cds--tile__chevron\", 4, \"ngIf\"], [\"class\", \"cds--tile__chevron cds--tile__chevron--interactive\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"cds--tile__chevron\"], [\"type\", \"button\", 1, \"cds--tile__chevron\", \"cds--tile__chevron--interactive\", 3, \"click\"]],\n  template: function ExpandableTile_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵtemplate(0, ExpandableTile_button_0_Template, 3, 12, \"button\", 3)(1, ExpandableTile_div_1_Template, 3, 11, \"div\", 4)(2, ExpandableTile_ng_template_2_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, ExpandableTile_ng_template_4_Template, 8, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.interactive);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.interactive);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3$1.IconDirective, i2.AsyncPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExpandableTile, [{\n    type: Component,\n    args: [{\n      selector: \"cds-expandable-tile, ibm-expandable-tile\",\n      template: `\n\t\t<button\n\t\t\t*ngIf=\"!interactive\"\n\t\t\tclass=\"cds--tile cds--tile--expandable\"\n\t\t\t[ngClass]=\"{\n\t\t\t\t'cds--tile--is-expanded' : expanded,\n\t\t\t\t'cds--tile--light': theme === 'light'\n\t\t\t}\"\n\t\t\t[ngStyle]=\"{'max-height': expandedHeight + 'px'}\"\n\t\t\ttype=\"button\"\n\t\t\t(click)=\"onClick()\"\n\t\t\t[attr.aria-expanded]=\"expanded\"\n\t\t\t[attr.title]=\"(expanded ? collapse.subject : expand.subject) | async\">\n\t\t\t\t<ng-container *ngTemplateOutlet=\"expandableTileContent\"></ng-container>\n\t\t</button>\n\n\t\t<div\n\t\t\t*ngIf=\"interactive\"\n\t\t\tclass=\"cds--tile cds--tile--expandable cds--tile--expandable--interactive\"\n\t\t\t[ngClass]=\"{\n\t\t\t\t'cds--tile--is-expanded' : expanded,\n\t\t\t\t'cds--tile--light': theme === 'light'\n\t\t\t}\"\n\t\t\t[ngStyle]=\"{'max-height': expandedHeight + 'px'}\"\n\t\t\t[attr.title]=\"(expanded ? collapse.subject : expand.subject) | async\">\n\t\t\t<ng-container *ngTemplateOutlet=\"expandableTileContent\"></ng-container>\n\t\t</div>\n\n\t\t<ng-template #chevronIcon>\n\t\t\t<svg cdsIcon=\"chevron--down\" size=\"16\"></svg>\n\t\t</ng-template>\n\n\t\t<ng-template #expandableTileContent>\n\t\t\t<div #container>\n\t\t\t\t<div class=\"cds--tile-content\">\n\t\t\t\t\t<ng-content select=\"[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold\"></ng-content>\n\t\t\t\t</div>\n\t\t\t\t<div *ngIf=\"!interactive\" class=\"cds--tile__chevron\">\n\t\t\t\t\t<ng-container *ngTemplateOutlet=\"chevronIcon\"></ng-container>\n\t\t\t\t</div>\n\t\t\t\t<button\n\t\t\t\t\t*ngIf=\"interactive\"\n\t\t\t\t\tclass=\"cds--tile__chevron cds--tile__chevron--interactive\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t(click)=\"onClick()\"\n\t\t\t\t\t[attr.aria-expanded]=\"expanded\"\n\t\t\t\t\t[attr.aria-label]=\"(expanded ? collapse.subject : expand.subject) | async\">\n\t\t\t\t\t<ng-container *ngTemplateOutlet=\"chevronIcon\"></ng-container>\n\t\t\t\t</button>\n\t\t\t\t<div class=\"cds--tile-content\">\n\t\t\t\t\t<ng-content select=\"[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold\"></ng-content>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</ng-template>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i1$1.I18n\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    theme: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    interactive: [{\n      type: Input\n    }],\n    translations: [{\n      type: Input\n    }],\n    tileContainer: [{\n      type: ViewChild,\n      args: [\"container\"]\n    }]\n  });\n})();\nclass ExpandableTileAboveFoldDirective {\n  constructor() {\n    this.aboveFold = true;\n  }\n}\nExpandableTileAboveFoldDirective.ɵfac = function ExpandableTileAboveFoldDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ExpandableTileAboveFoldDirective)();\n};\nExpandableTileAboveFoldDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ExpandableTileAboveFoldDirective,\n  selectors: [[\"\", \"cdsAboveFold\", \"\"], [\"\", \"ibmAboveFold\", \"\"]],\n  hostVars: 2,\n  hostBindings: function ExpandableTileAboveFoldDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tile-content__above-the-fold\", ctx.aboveFold);\n    }\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExpandableTileAboveFoldDirective, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsAboveFold], [ibmAboveFold]\"\n    }]\n  }], null, {\n    aboveFold: [{\n      type: HostBinding,\n      args: [\"class.cds--tile-content__above-the-fold\"]\n    }]\n  });\n})();\nclass ExpandableTileBelowFoldDirective {\n  constructor() {\n    this.belowFold = true;\n  }\n}\nExpandableTileBelowFoldDirective.ɵfac = function ExpandableTileBelowFoldDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ExpandableTileBelowFoldDirective)();\n};\nExpandableTileBelowFoldDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ExpandableTileBelowFoldDirective,\n  selectors: [[\"\", \"cdsBelowFold\", \"\"], [\"\", \"ibmBelowFold\", \"\"]],\n  hostVars: 2,\n  hostBindings: function ExpandableTileBelowFoldDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tile-content__below-the-fold\", ctx.belowFold);\n    }\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExpandableTileBelowFoldDirective, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsBelowFold], [ibmBelowFold]\"\n    }]\n  }], null, {\n    belowFold: [{\n      type: HostBinding,\n      args: [\"class.cds--tile-content__below-the-fold\"]\n    }]\n  });\n})();\nclass SelectionTile {\n  constructor(i18n) {\n    this.i18n = i18n;\n    /**\n     * @deprecated since v5 - Use `cdsLayer` directive instead\n     * Set to `\"light\"` to apply the light style\n     */\n    this.theme = \"dark\";\n    /**\n     * The unique id for the input.\n     */\n    this.id = `tile-${SelectionTile.tileCount}`;\n    /**\n     * Internal event used to notify the containing `TileGroup` of changes.\n     */\n    this.change = new EventEmitter();\n    /**\n     * Set to `true` to disable the selection tile.\n     */\n    this.disabled = false;\n    /**\n     * Set by the containing `TileGroup`. Used for the `name` property on the input.\n     */\n    this.name = \"tile-group-unbound\";\n    /**\n     * Defines whether or not the `SelectionTile` supports selecting multiple tiles as opposed to single\n     * tile selection.\n     */\n    this.multiple = true; // Set to true because of the way tile group sets it up.\n    // If an initial selected value is set before input exists, we save\n    // the value and check again when input exists in `AfterViewInit`.\n    this._selected = null;\n    SelectionTile.tileCount++;\n  }\n  /**\n   * Updating the state of the input to match the state of the parameter passed in.\n   * Set to `true` if this tile should be selected.\n   */\n  set selected(value) {\n    // If an initial selected value is set before input exists, we save\n    // the value and check again when input exists in `AfterViewInit`.\n    this._selected = value ? true : null;\n    if (this.input) {\n      this.input.nativeElement.checked = this._selected;\n    }\n  }\n  get selected() {\n    return this.input ? this.input.nativeElement.checked : false;\n  }\n  ngAfterViewInit() {\n    if (this.input) {\n      setTimeout(() => {\n        this.input.nativeElement.checked = this._selected;\n      });\n    }\n  }\n  keyboardInput(event) {\n    if (event.key === \"Enter\" || event.key === \"Spacebar\" || event.key === \" \") {\n      this.selected = !this.selected;\n      this.change.emit(event);\n    }\n  }\n  onChange(event) {\n    this.change.emit(event);\n  }\n}\nSelectionTile.tileCount = 0;\nSelectionTile.ɵfac = function SelectionTile_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || SelectionTile)(i0.ɵɵdirectiveInject(i1$1.I18n));\n};\nSelectionTile.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: SelectionTile,\n  selectors: [[\"cds-selection-tile\"], [\"ibm-selection-tile\"]],\n  viewQuery: function SelectionTile_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c7, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n    }\n  },\n  hostBindings: function SelectionTile_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function SelectionTile_keydown_HostBindingHandler($event) {\n        return ctx.keyboardInput($event);\n      });\n    }\n  },\n  inputs: {\n    theme: \"theme\",\n    id: \"id\",\n    selected: \"selected\",\n    value: \"value\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    change: \"change\"\n  },\n  standalone: false,\n  ngContentSelectors: _c0,\n  decls: 10,\n  vars: 19,\n  consts: [[\"input\", \"\"], [\"selectedIcon\", \"\"], [1, \"cds--tile-input\", 3, \"change\", \"id\", \"disabled\", \"type\", \"value\", \"name\"], [1, \"cds--tile\", \"cds--tile--selectable\", 3, \"for\", \"ngClass\"], [1, \"cds--tile__checkmark\"], [\"size\", \"16\", 3, \"cdsIcon\", 4, \"ngIf\", \"ngIfElse\"], [1, \"cds--tile-content\"], [\"size\", \"16\", 3, \"cdsIcon\"]],\n  template: function SelectionTile_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"input\", 2, 0);\n      i0.ɵɵlistener(\"change\", function SelectionTile_Template_input_change_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onChange($event));\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(2, \"label\", 3);\n      i0.ɵɵpipe(3, \"async\");\n      i0.ɵɵelementStart(4, \"div\", 4);\n      i0.ɵɵtemplate(5, SelectionTile__svg_svg_5_Template, 1, 1, \"svg\", 5)(6, SelectionTile_ng_template_6_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const selectedIcon_r3 = i0.ɵɵreference(7);\n      i0.ɵɵproperty(\"id\", ctx.id)(\"disabled\", ctx.disabled)(\"type\", ctx.multiple ? \"checkbox\" : \"radio\")(\"value\", ctx.value)(\"name\", ctx.name);\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"for\", ctx.id)(\"ngClass\", i0.ɵɵpureFunction3(15, _c8, ctx.selected, ctx.theme === \"light\", ctx.disabled));\n      i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind1(3, 13, ctx.i18n.get(\"TILES.TILE\")));\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"cds--tile__checkmark--persistent\", ctx.multiple);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.selected)(\"ngIfElse\", selectedIcon_r3);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i3$1.IconDirective, i2.AsyncPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SelectionTile, [{\n    type: Component,\n    args: [{\n      selector: \"cds-selection-tile, ibm-selection-tile\",\n      template: `\n\t\t<input\n\t\t\t#input\n\t\t\t[attr.tabindex]=\"disabled ? null : 0\"\n\t\t\tclass=\"cds--tile-input\"\n\t\t\t[id]=\"id\"\n\t\t\t[disabled]=\"disabled\"\n\t\t\t[type]=\"(multiple ? 'checkbox': 'radio')\"\n\t\t\t[value]=\"value\"\n\t\t\t[name]=\"name\"\n\t\t\t(change)=\"onChange($event)\"/>\n\t\t<label\n\t\t\tclass=\"cds--tile cds--tile--selectable\"\n\t\t\t[for]=\"id\"\n\t\t\t[ngClass]=\"{\n\t\t\t\t'cds--tile--is-selected' : selected,\n\t\t\t\t'cds--tile--light': theme === 'light',\n\t\t\t\t'cds--tile--disabled' : disabled\n\t\t\t}\"\n\t\t\t[attr.aria-label]=\"i18n.get('TILES.TILE') | async\">\n\t\t\t<div class=\"cds--tile__checkmark\"\n\t\t\t\t[class.cds--tile__checkmark--persistent]=\"multiple\">\n\t\t\t\t<svg *ngIf=\"!selected; else selectedIcon\"\n\t\t\t\t\t[cdsIcon]=\"multiple ? 'checkbox' : 'checkmark'\"\n\t\t\t\t\tsize=\"16\">\n\t\t\t\t</svg>\n\t\t\t\t<ng-template #selectedIcon>\n\t\t\t\t\t<svg [cdsIcon]=\"multiple ? 'checkbox--checked--filled' : 'checkmark--filled'\" size=\"16\"></svg>\n\t\t\t\t</ng-template>\n\t\t\t</div>\n\t\t\t<div class=\"cds--tile-content\">\n\t\t\t\t<ng-content></ng-content>\n\t\t\t</div>\n\t\t</label>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i1$1.I18n\n    }];\n  }, {\n    theme: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    disabled: [{\n      type: Input\n    }],\n    input: [{\n      type: ViewChild,\n      args: [\"input\", {\n        static: true\n      }]\n    }],\n    keyboardInput: [{\n      type: HostListener,\n      args: [\"keydown\", [\"$event\"]]\n    }]\n  });\n})();\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { TilesModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-tiles-grouped--selectable)\n */\nclass TileGroup {\n  constructor() {\n    /**\n     * The tile group `name`\n     */\n    this.name = `tile-group-${TileGroup.tileGroupCount}`;\n    /**\n     * Set to `true` to support multiple tile selection\n     */\n    this.multiple = false;\n    /**\n     * Emits an event when the tile selection changes.\n     *\n     * Emits an object that looks like:\n     * ```javascript\n     * {\n     * \tvalue: \"something\",\n     * \tselected: true,\n     * \tname: \"tile-group-1\"\n     * }\n     * ```\n     */\n    this.selected = new EventEmitter();\n    this.tileGroupClass = true;\n    this.unsubscribe$ = new Subject();\n    this.unsubscribeTiles$ = new Subject();\n    this.onChange = _ => {};\n    this.onTouched = () => {};\n    TileGroup.tileGroupCount++;\n  }\n  ngAfterContentInit() {\n    const updateTiles = () => {\n      // remove old subscriptions\n      this.unsubscribeTiles$.next();\n      // react to changes\n      // setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        this.selectionTiles.forEach(tile => {\n          tile.name = this.name;\n          tile.change.pipe(takeUntil(this.unsubscribeTiles$)).subscribe(() => {\n            this.selected.emit({\n              value: tile.value,\n              selected: tile.selected,\n              name: this.name\n            });\n            this.onChange(tile.value);\n          });\n          tile.multiple = this.multiple;\n        });\n      });\n    };\n    updateTiles();\n    this.selectionTiles.changes.pipe(takeUntil(this.unsubscribe$)).subscribe(_ => updateTiles());\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n    // takes care of tile subscriptions when tile-group dies\n    this.unsubscribeTiles$.next();\n    this.unsubscribeTiles$.complete();\n  }\n  writeValue(value) {\n    if (!this.selectionTiles) {\n      return;\n    }\n    this.selectionTiles.forEach(tile => {\n      if (tile.value === value) {\n        tile.selected = true;\n      } else {\n        tile.selected = false;\n      }\n    });\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  isTemplate(value) {\n    return value instanceof TemplateRef;\n  }\n}\nTileGroup.tileGroupCount = 0;\nTileGroup.ɵfac = function TileGroup_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TileGroup)();\n};\nTileGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TileGroup,\n  selectors: [[\"cds-tile-group\"], [\"ibm-tile-group\"]],\n  contentQueries: function TileGroup_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, SelectionTile, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.selectionTiles = _t);\n    }\n  },\n  hostVars: 2,\n  hostBindings: function TileGroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tile-group\", ctx.tileGroupClass);\n    }\n  },\n  inputs: {\n    name: \"name\",\n    multiple: \"multiple\",\n    legend: \"legend\"\n  },\n  outputs: {\n    selected: \"selected\"\n  },\n  standalone: false,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: TileGroup,\n    multi: true\n  }])],\n  ngContentSelectors: _c10,\n  decls: 3,\n  vars: 1,\n  consts: [[\"legendLabel\", \"\"], [\"class\", \"cds--label\", 4, \"ngIf\"], [1, \"cds--label\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngTemplateOutlet\"]],\n  template: function TileGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c9);\n      i0.ɵɵelementStart(0, \"fieldset\");\n      i0.ɵɵtemplate(1, TileGroup_legend_1_Template, 4, 2, \"legend\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.legend);\n    }\n  },\n  dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TileGroup, [{\n    type: Component,\n    args: [{\n      selector: \"cds-tile-group, ibm-tile-group\",\n      template: `\n\t\t<fieldset>\n\t\t\t<legend *ngIf=\"legend\" class=\"cds--label\">\n\t\t\t\t<ng-template *ngIf=\"isTemplate(legend); else legendLabel;\" [ngTemplateOutlet]=\"legend\"></ng-template>\n\t\t\t\t<ng-template #legendLabel>{{legend}}</ng-template>\n\t\t\t</legend>\n\t\t\t<ng-content select=\"ibm-selection-tile,cds-selection-tile\"></ng-content>\n\t\t</fieldset>`,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: TileGroup,\n        multi: true\n      }]\n    }]\n  }], function () {\n    return [];\n  }, {\n    name: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    legend: [{\n      type: Input\n    }],\n    selected: [{\n      type: Output\n    }],\n    tileGroupClass: [{\n      type: HostBinding,\n      args: [\"class.cds--tile-group\"]\n    }],\n    selectionTiles: [{\n      type: ContentChildren,\n      args: [SelectionTile]\n    }]\n  });\n})();\n\n/**\n * Build application's tiles using this component. Get started with importing the module:\n *\n * ```typescript\n * import { TilesModule } from 'carbon-components-angular';\n * ```\n *\n * ```html\n * <cds-tile>\n * \t\ttile content\n * </cds-tile>\n * ```\n *\n * [See demo](../../?path=/story/components-tiles--basic)\n */\nclass Tile {\n  constructor() {\n    this.tileClass = true;\n    /**\n     * @deprecated since v5 - Use `cdsLayer` directive instead\n     * Set to `\"light\"` to apply the light style\n     */\n    this.theme = \"dark\";\n  }\n  get lightThemeEnabled() {\n    return this.theme === \"light\";\n  }\n}\nTile.ɵfac = function Tile_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || Tile)();\n};\nTile.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Tile,\n  selectors: [[\"cds-tile\"], [\"ibm-tile\"]],\n  hostVars: 4,\n  hostBindings: function Tile_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tile\", ctx.tileClass)(\"cds--tile--light\", ctx.lightThemeEnabled);\n    }\n  },\n  inputs: {\n    theme: \"theme\"\n  },\n  standalone: false,\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function Tile_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tile, [{\n    type: Component,\n    args: [{\n      selector: \"cds-tile, ibm-tile\",\n      template: `<ng-content></ng-content>`\n    }]\n  }], null, {\n    tileClass: [{\n      type: HostBinding,\n      args: [\"class.cds--tile\"]\n    }],\n    lightThemeEnabled: [{\n      type: HostBinding,\n      args: [\"class.cds--tile--light\"]\n    }],\n    theme: [{\n      type: Input\n    }]\n  });\n})();\nclass TilesModule {}\nTilesModule.ɵfac = function TilesModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TilesModule)();\n};\nTilesModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TilesModule,\n  declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],\n  imports: [CommonModule, I18nModule, IconModule, LinkModule],\n  exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup]\n});\nTilesModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, I18nModule, IconModule, LinkModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TilesModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],\n      exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],\n      imports: [CommonModule, I18nModule, IconModule, LinkModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ClickableTile, ClickableTileIconDirective, ExpandableTile, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, SelectionTile, Tile, TileGroup, TilesModule };\n", "import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Project } from '../services/projects.service';\n\n// Carbon Design System imports\nimport { ButtonModule } from 'carbon-components-angular/button';\nimport { TilesModule } from 'carbon-components-angular/tiles';\n\n@Component({\n  selector: 'app-project-card',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ButtonModule,\n    TilesModule\n  ],\n  template: `\n    <cds-tile class=\"project-card\">\n      <div class=\"project-card-content\">\n        <div class=\"project-info\">\n          <h3 class=\"project-title\">{{ project.title }}</h3>\n          <span class=\"project-type\">{{ project.type }}</span>\n        </div>\n        <div class=\"project-actions\">\n          <button\n            cdsButton=\"primary\"\n            size=\"sm\"\n            (click)=\"onEnter()\">\n            Enter\n          </button>\n        </div>\n      </div>\n    </cds-tile>\n  `,\n  styles: [`\n    .project-card {\n      margin-bottom: 1rem;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n    \n    .project-card:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n    }\n    \n    .project-card-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 0.5rem;\n    }\n    \n    .project-info {\n      flex: 1;\n    }\n    \n    .project-title {\n      margin: 0 0 0.25rem 0;\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #161616;\n    }\n    \n    .project-type {\n      font-size: 0.875rem;\n      color: #6f6f6f;\n      text-transform: uppercase;\n      font-weight: 500;\n    }\n    \n    .project-actions {\n      margin-left: 1rem;\n    }\n  `]\n})\nexport class ProjectCardComponent {\n  @Input({ required: true }) project!: Project;\n  @Output() enter = new EventEmitter<Project>();\n\n  onEnter() {\n    this.enter.emit(this.project);\n  }\n}\n", "import { Component, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { ProjectsService, Project } from '../services/projects.service';\nimport { AuthService } from '../services/auth.service';\nimport { ProjectCardComponent } from '../components/project-card.component';\n\n// Carbon Design System imports\nimport { ButtonModule } from 'carbon-components-angular/button';\nimport { LoadingModule } from 'carbon-components-angular/loading';\n\n@Component({\n  selector: 'app-projects-page',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ButtonModule,\n    LoadingModule,\n    ProjectCardComponent\n  ],\n  template: `\n    <div class=\"projects-container\">\n      <header class=\"projects-header\">\n        <h1>Your Projects</h1>\n        <div class=\"header-actions\">\n          <button\n            cdsButton=\"primary\"\n            size=\"md\"\n            (click)=\"newProject()\">\n            + New Project\n          </button>\n          <button\n            cdsButton=\"secondary\"\n            size=\"md\"\n            (click)=\"signOut()\">\n            Sign Out\n          </button>\n        </div>\n      </header>\n\n      <main class=\"projects-content\">\n        <!-- Loading while first emission pending -->\n        @if (projects$ | async; as projects) {\n          <!-- Empty state -->\n          @if (projects.length === 0) {\n            <div class=\"empty-state\">\n              <div class=\"empty-state-content\">\n                <h2>No projects yet</h2>\n                <p>Create your first project to get started.</p>\n                <button\n                  cdsButton=\"primary\"\n                  size=\"lg\"\n                  (click)=\"newProject()\">\n                  Create First Project\n                </button>\n              </div>\n            </div>\n          } @else {\n            <!-- List -->\n            <div class=\"projects-list\">\n              @for (project of projects; track project.id) {\n                <app-project-card\n                  [project]=\"project\"\n                  (enter)=\"enterProject($event)\">\n                </app-project-card>\n              }\n            </div>\n          }\n        } @else {\n          <!-- Loading state -->\n          <div class=\"loading-state\">\n            <cds-loading></cds-loading>\n            <p>Loading projects...</p>\n          </div>\n        }\n      </main>\n    </div>\n  `,\n  styles: [`\n    .projects-container {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n      min-height: 100vh;\n    }\n    \n    .projects-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n      padding-bottom: 1rem;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    \n    .projects-header h1 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: 400;\n      color: #161616;\n    }\n    \n    .header-actions {\n      display: flex;\n      gap: 1rem;\n    }\n    \n    .projects-content {\n      padding: 1rem 0;\n    }\n    \n    .loading-state {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 4rem 2rem;\n      text-align: center;\n    }\n    \n    .loading-state p {\n      margin-top: 1rem;\n      color: #6f6f6f;\n      font-size: 1.125rem;\n    }\n    \n    .empty-state {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      min-height: 400px;\n      padding: 2rem;\n    }\n    \n    .empty-state-content {\n      text-align: center;\n      max-width: 400px;\n    }\n    \n    .empty-state-content h2 {\n      margin: 0 0 1rem 0;\n      font-size: 1.5rem;\n      font-weight: 400;\n      color: #161616;\n    }\n    \n    .empty-state-content p {\n      margin: 0 0 2rem 0;\n      color: #6f6f6f;\n      font-size: 1rem;\n      line-height: 1.5;\n    }\n    \n    .projects-list {\n      display: grid;\n      gap: 1rem;\n      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n    }\n    \n    @media (max-width: 768px) {\n      .projects-container {\n        padding: 1rem;\n      }\n      \n      .projects-header {\n        flex-direction: column;\n        gap: 1rem;\n        align-items: stretch;\n      }\n      \n      .header-actions {\n        justify-content: center;\n      }\n      \n      .projects-list {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class ProjectsPage {\n  private projectsService = inject(ProjectsService);\n  private auth = inject(AuthService);\n  private router = inject(Router);\n\n  projects$ = this.projectsService.projects$;\n\n  async newProject() {\n    try {\n      await this.projectsService.createProject('New Project', 'DRYWALL');\n    } catch (error) {\n      console.error('Error creating project:', error);\n    }\n  }\n\n  enterProject(project: Project) {\n    console.log('Entering project:', project);\n    // TODO: Navigate to project detail page\n    // this.router.navigate(['/projects', project.id]);\n  }\n\n  async signOut() {\n    try {\n      console.log('Signing out...');\n      await this.auth.signOut();\n      console.log('Sign out successful, navigating to login...');\n\n      // Small delay to ensure auth state is updated\n      setTimeout(async () => {\n        await this.router.navigate(['/login']);\n      }, 100);\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBM,IAAO,kBAAP,MAAO,iBAAe;EAClB,KAAK,OAAO,SAAS;EACrB,OAAO,OAAO,IAAI;;EAG1B,YAAmC,UAAU,KAAK,IAAI,EAAE,KACtD,UAAU,UAAO;AACf,QAAI,CAAC;AAAM,aAAO,GAAG,CAAA,CAAE;AACvB,UAAM,MAAM,WAAW,KAAK,IAAI,SAAS,KAAK,GAAG,WAAW;AAC5D,UAAM,IAAI,MAAM,KAAK,QAAQ,aAAa,MAAM,GAAG,MAAM,CAAC,CAAC;AAE3D,WAAO,eAAe,GAAG,EAAE,SAAS,KAAI,CAAE,EAAE,KAAK,IAAI,OAAK,CAAc,CAAC;EAC3E,CAAC,CAAC;;EAIJ,MAAM,cAAc,OAAe,MAAiB;AAClD,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,CAAC;AAAM,YAAM,IAAI,MAAM,eAAe;AAE1C,UAAM,MAAM,WAAW,KAAK,IAAI,SAAS,KAAK,GAAG,WAAW;AAC5D,UAAM,MAAM,IAAI,GAAG;AACnB,UAAM,OAAgB;MACpB,IAAI,IAAI;MACR,OAAO,MAAM,KAAI;MACjB;MACA,UAAU,KAAK;MACf,WAAW,gBAAe;MAC1B,WAAW,gBAAe;;AAE5B,UAAM,OAAO,KAAK,IAAI;AACtB,WAAO,IAAI;EACb;;qCAhCW,kBAAe;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YADF,OAAM,CAAA;;;sEACnB,iBAAe,CAAA;UAD3B;WAAW,EAAE,YAAY,OAAM,CAAE;;;;;ACClC,IAAM,OAAN,MAAW;AAAA,EACT,cAAc;AACZ,SAAK,YAAY;AAIjB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AACjB,SAAK,WAAW,KAAK,WAAW,KAAK;AAAA,EACvC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AACF;AACA,KAAK,YAAO,SAAS,aAAa,mBAAmB;AACnD,SAAO,KAAK,qBAAqB,MAAM;AACzC;AACA,KAAK,YAAsB,gBAAG,4BAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,EACpD,UAAU;AAAA,EACV,cAAc,SAAS,kBAAkB,IAAI,KAAK;AAChD,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,YAAY,IAAI,QAAQ,EAAE,iBAAiB,IAAI,QAAQ;AACtE,MAAG,sBAAY,aAAa,IAAI,SAAS,EAAE,qBAAqB,IAAI,MAAM,EAAE,uBAAuB,IAAI,QAAQ;AAAA,IACjH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AACF;AACA,kBAAkB,YAAO,SAAS,0BAA0B,mBAAmB;AAC7E,SAAO,KAAK,qBAAqB,mBAAmB;AACtD;AACA,kBAAkB,YAAsB,gBAAG,4BAAkB;AAAA,EAC3D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,EAC5D,UAAU;AAAA,EACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,mBAAmB,IAAI,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAiB;AAAC;AAClB,WAAW,YAAO,SAAS,mBAAmB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,YAAY;AAC/C;AACA,WAAW,YAAsB,gBAAG,2BAAiB;AAAA,EACnD,MAAM;AAAA,EACN,cAAc,CAAC,MAAM,iBAAiB;AAAA,EACtC,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,MAAM,iBAAiB;AACnC,CAAC;AACD,WAAW,YAAsB,gBAAG,2BAAiB;AAAA,EACnD,SAAS,CAAC,YAAY;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,MAAM,iBAAiB;AAAA,MACtC,SAAS,CAAC,MAAM,iBAAiB;AAAA,MACjC,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AChIH,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,oBAAoB;AAAA,EACpB,2CAA2C;AAC7C;AACA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,GAAG,mCAAmC,CAAC,GAAG,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,GAAG,mCAAmC,CAAC,CAAC;AACnN,IAAM,MAAM,CAAC,oEAAoE,kEAAkE;AACnJ,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,0BAA0B;AAAA,EAC1B,oBAAoB;AACtB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,cAAc;AAChB;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,6BAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,2BAAiB;AAChC,IAAG,yBAAe,GAAG,UAAU,CAAC;AAChC,IAAG,iBAAO,GAAG,OAAO;AACpB,IAAG,qBAAW,SAAS,SAAS,2DAA2D;AACzF,MAAG,wBAAc,GAAG;AACpB,YAAM,SAAY,wBAAc;AAChC,aAAU,sBAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,qBAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AACzF,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,UAAM,2BAA8B,sBAAY,CAAC;AACjD,IAAG,qBAAW,WAAc,0BAAgB,GAAG,KAAK,OAAO,UAAU,OAAO,UAAU,OAAO,CAAC,EAAE,WAAc,0BAAgB,IAAI,KAAK,OAAO,iBAAiB,IAAI,CAAC;AACpK,IAAG,sBAAY,iBAAiB,OAAO,QAAQ,EAAE,SAAY,sBAAY,GAAG,GAAG,OAAO,WAAW,OAAO,SAAS,UAAU,OAAO,OAAO,OAAO,CAAC;AACjJ,IAAG,oBAAU,CAAC;AACd,IAAG,qBAAW,oBAAoB,wBAAwB;AAAA,EAC5D;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,6BAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAO,GAAG,OAAO;AACpB,IAAG,qBAAW,GAAG,8CAA8C,GAAG,GAAG,gBAAgB,CAAC;AACtF,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,UAAM,2BAA8B,sBAAY,CAAC;AACjD,IAAG,qBAAW,WAAc,0BAAgB,GAAG,KAAK,OAAO,UAAU,OAAO,UAAU,OAAO,CAAC,EAAE,WAAc,0BAAgB,GAAG,KAAK,OAAO,iBAAiB,IAAI,CAAC;AACnK,IAAG,sBAAY,SAAY,sBAAY,GAAG,GAAG,OAAO,WAAW,OAAO,SAAS,UAAU,OAAO,OAAO,OAAO,CAAC;AAC/G,IAAG,oBAAU,CAAC;AACd,IAAG,qBAAW,oBAAoB,wBAAwB;AAAA,EAC5D;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe;AAClB,IAAG,oBAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,6BAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,qBAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,wBAAc,CAAC;AAClB,UAAM,iBAAoB,sBAAY,CAAC;AACvC,IAAG,oBAAU;AACb,IAAG,qBAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,6BAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,2BAAiB;AAChC,IAAG,yBAAe,GAAG,UAAU,EAAE;AACjC,IAAG,iBAAO,GAAG,OAAO;AACpB,IAAG,qBAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,wBAAc,GAAG;AACpB,YAAM,SAAY,wBAAc,CAAC;AACjC,aAAU,sBAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,qBAAW,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,CAAC;AACvG,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc,CAAC;AACjC,UAAM,iBAAoB,sBAAY,CAAC;AACvC,IAAG,sBAAY,iBAAiB,OAAO,QAAQ,EAAE,cAAiB,sBAAY,GAAG,GAAG,OAAO,WAAW,OAAO,SAAS,UAAU,OAAO,OAAO,OAAO,CAAC;AACtJ,IAAG,oBAAU,CAAC;AACd,IAAG,qBAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe,GAAG,OAAO,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC;AAChD,IAAG,uBAAa,CAAC;AACjB,IAAG,uBAAa;AAChB,IAAG,qBAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,gDAAgD,GAAG,GAAG,UAAU,EAAE;AACpJ,IAAG,yBAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,uBAAa,GAAG,CAAC;AACpB,IAAG,uBAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,IAAG,oBAAU,CAAC;AACd,IAAG,qBAAW,QAAQ,CAAC,OAAO,WAAW;AACzC,IAAG,oBAAU;AACb,IAAG,qBAAW,QAAQ,OAAO,WAAW;AAAA,EAC1C;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA,EACpB,uBAAuB;AACzB;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe;AAClB,IAAG,oBAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,IAAG,qBAAW,WAAW,OAAO,WAAW,aAAa,WAAW;AAAA,EACrE;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe;AAClB,IAAG,oBAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,IAAG,qBAAW,WAAW,OAAO,WAAW,8BAA8B,mBAAmB;AAAA,EAC9F;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,oBAAoB,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC7D,IAAM,OAAO,CAAC,uCAAuC;AACrD,SAAS,4CAA4C,IAAI,KAAK;AAAC;AAC/D,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,qBAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,CAAC;AAAA,EACtF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc,CAAC;AACjC,IAAG,qBAAW,oBAAoB,OAAO,MAAM;AAAA,EACjD;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc,CAAC;AACjC,IAAG,4BAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe,GAAG,UAAU,CAAC;AAChC,IAAG,qBAAW,GAAG,+BAA+B,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,gCAAsB;AACpK,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAoB,sBAAY,CAAC;AACvC,UAAM,SAAY,wBAAc;AAChC,IAAG,oBAAU;AACb,IAAG,qBAAW,QAAQ,OAAO,WAAW,OAAO,MAAM,CAAC,EAAE,YAAY,cAAc;AAAA,EACpF;AACF;AACA,IAAM,6BAAN,MAAiC;AAAA,EAC/B,cAAc;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,2BAA2B,YAAO,SAAS,mCAAmC,mBAAmB;AAC/F,SAAO,KAAK,qBAAqB,4BAA4B;AAC/D;AACA,2BAA2B,YAAsB,gBAAG,4BAAkB;AAAA,EACpE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,wBAAwB,EAAE,GAAG,CAAC,IAAI,wBAAwB,EAAE,CAAC;AAAA,EAC9E,UAAU;AAAA,EACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,mBAAmB,IAAI,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAeH,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAKd,SAAK,QAAQ;AAIb,SAAK,OAAO;AAIZ,SAAK,WAAW;AAIhB,SAAK,aAAa,IAAI,aAAa;AAAA,EACrC;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,UAAU,KAAK,SAAS,CAAC,KAAK,UAAU;AAC/C,YAAM,eAAe;AACrB,YAAM,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK,WAAW;AAChE,WAAK,WAAW,KAAK,MAAM;AAAA,IAC7B;AAAA,EACF;AACF;AACA,cAAc,YAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAkB,4BAAqB,QAAQ,CAAC,CAAC;AACpF;AACA,cAAc,YAAsB,gBAAG,4BAAkB;AAAA,EACvD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,GAAG,CAAC,oBAAoB,CAAC;AAAA,EAC1D,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,WAAW,IAAI,YAAY,KAAK,GAAG,aAAa,wBAAwB,GAAG,SAAS,SAAS,CAAC;AAAA,EACxG,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,QAAI,KAAK,GAAG;AACV,MAAG,0BAAgB;AACnB,MAAG,yBAAe,GAAG,KAAK,CAAC;AAC3B,MAAG,qBAAW,SAAS,SAAS,0CAA0C,QAAQ;AAChF,eAAO,IAAI,SAAS,MAAM;AAAA,MAC5B,CAAC;AACD,MAAG,uBAAa,CAAC;AACjB,MAAG,uBAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,qBAAW,WAAc,0BAAgB,GAAG,KAAK,IAAI,UAAU,SAAS,IAAI,QAAQ,CAAC;AACxF,MAAG,sBAAY,QAAQ,IAAI,WAAW,OAAO,IAAI,MAAS,uBAAa,EAAE,UAAU,IAAI,MAAM,EAAE,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,iBAAiB,IAAI,QAAQ;AAAA,IAC/J;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,IAAI;AAAA,EAClC,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,MACT,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,MAAM,SAAS;AACzB,SAAK,OAAO;AACZ,SAAK,UAAU;AAKf,SAAK,QAAQ;AAIb,SAAK,WAAW;AAIhB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,SAAS,KAAK,KAAK,eAAe,cAAc;AACrD,SAAK,WAAW,KAAK,KAAK,eAAe,gBAAgB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,aAAa,OAAO;AACtB,UAAM,oBAAoB,MAAM,KAAK,KAAK,YAAY,OAAO,GAAG,KAAK;AACrE,SAAK,OAAO,SAAS,kBAAkB,MAAM;AAC7C,SAAK,SAAS,SAAS,kBAAkB,QAAQ;AAAA,EACnD;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,IAAI,iBAAiB;AACnB,UAAM,OAAO,KAAK,QAAQ,cAAc,cAAc,YAAY;AAClE,UAAM,cAAc,SAAS,iBAAiB,IAAI,EAAE,eAAe,EAAE,IAAI,SAAS,iBAAiB,IAAI,EAAE,YAAY,EAAE;AACvH,UAAM,iBAAiB,KAAK,gBAAgB;AAC5C,QAAI,CAAC,MAAM,cAAc,GAAG;AAC1B,WAAK,wBAAwB;AAAA,IAC/B;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,gBAAgB,KAAK,cAAc,cAAc,sBAAsB,EAAE;AAAA,IAChF,OAAO;AACL,WAAK,gBAAgB,KAAK,QAAQ,cAAc,cAAc,oCAAoC,EAAE,sBAAsB,EAAE;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,WAAW,CAAC,KAAK;AACtB,SAAK,gBAAgB;AAAA,EACvB;AACF;AACA,eAAe,YAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAmB,4BAAuB,IAAI,GAAM,4BAAqB,UAAU,CAAC;AACvH;AACA,eAAe,YAAsB,gBAAG,4BAAkB;AAAA,EACxD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,GAAG,CAAC,qBAAqB,CAAC;AAAA,EAC5D,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,yBAAe,KAAQ,sBAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,IACtE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,mCAAmC,QAAQ,UAAU,GAAG,WAAW,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,sEAAsE,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,GAAG,aAAa,yBAAyB,GAAG,SAAS,WAAW,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,aAAa,yBAAyB,sCAAsC,GAAG,WAAW,SAAS,GAAG,CAAC,WAAW,iBAAiB,QAAQ,IAAI,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,SAAS,sDAAsD,QAAQ,UAAU,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,QAAQ,UAAU,GAAG,sBAAsB,mCAAmC,GAAG,OAAO,CAAC;AAAA,EACr2B,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,QAAI,KAAK,GAAG;AACV,MAAG,0BAAgB,GAAG;AACtB,MAAG,qBAAW,GAAG,kCAAkC,GAAG,IAAI,UAAU,CAAC,EAAE,GAAG,+BAA+B,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG,uCAAuC,GAAG,GAAG,eAAe,MAAM,GAAM,gCAAsB,EAAE,GAAG,uCAAuC,GAAG,GAAG,eAAe,MAAM,GAAM,gCAAsB;AAAA,IAChU;AACA,QAAI,KAAK,GAAG;AACV,MAAG,qBAAW,QAAQ,CAAC,IAAI,WAAW;AACtC,MAAG,oBAAU;AACb,MAAG,qBAAW,QAAQ,IAAI,WAAW;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,MAAS,kBAAqB,SAAc,eAAkB,SAAS;AAAA,EACrG,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuDZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAW;AAAA,IACb,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAuC;AAAA,EACrC,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AACF;AACA,iCAAiC,YAAO,SAAS,yCAAyC,mBAAmB;AAC3G,SAAO,KAAK,qBAAqB,kCAAkC;AACrE;AACA,iCAAiC,YAAsB,gBAAG,4BAAkB;AAAA,EAC1E,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EAC9D,UAAU;AAAA,EACV,cAAc,SAAS,8CAA8C,IAAI,KAAK;AAC5E,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,qCAAqC,IAAI,SAAS;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,yCAAyC;AAAA,IAClD,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAuC;AAAA,EACrC,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AACF;AACA,iCAAiC,YAAO,SAAS,yCAAyC,mBAAmB;AAC3G,SAAO,KAAK,qBAAqB,kCAAkC;AACrE;AACA,iCAAiC,YAAsB,gBAAG,4BAAkB;AAAA,EAC1E,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EAC9D,UAAU;AAAA,EACV,cAAc,SAAS,8CAA8C,IAAI,KAAK;AAC5E,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,qCAAqC,IAAI,SAAS;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,yCAAyC;AAAA,IAClD,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,MAAM;AAChB,SAAK,OAAO;AAKZ,SAAK,QAAQ;AAIb,SAAK,KAAK,QAAQ,eAAc,SAAS;AAIzC,SAAK,SAAS,IAAI,aAAa;AAI/B,SAAK,WAAW;AAIhB,SAAK,OAAO;AAKZ,SAAK,WAAW;AAGhB,SAAK,YAAY;AACjB,mBAAc;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS,OAAO;AAGlB,SAAK,YAAY,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,cAAc,UAAU,KAAK;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,QAAQ,KAAK,MAAM,cAAc,UAAU;AAAA,EACzD;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,OAAO;AACd,iBAAW,MAAM;AACf,aAAK,MAAM,cAAc,UAAU,KAAK;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ,cAAc,MAAM,QAAQ,KAAK;AAC1E,WAAK,WAAW,CAAC,KAAK;AACtB,WAAK,OAAO,KAAK,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AACF;AACA,cAAc,YAAY;AAC1B,cAAc,YAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAkB,4BAAuB,IAAI,CAAC;AACjF;AACA,cAAc,YAAsB,gBAAG,4BAAkB;AAAA,EACvD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,GAAG,CAAC,oBAAoB,CAAC;AAAA,EAC1D,WAAW,SAAS,oBAAoB,IAAI,KAAK;AAC/C,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,yBAAe,KAAQ,sBAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,QAAI,KAAK,GAAG;AACV,MAAG,qBAAW,WAAW,SAAS,yCAAyC,QAAQ;AACjF,eAAO,IAAI,cAAc,MAAM;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,mBAAmB,GAAG,UAAU,MAAM,YAAY,QAAQ,SAAS,MAAM,GAAG,CAAC,GAAG,aAAa,yBAAyB,GAAG,OAAO,SAAS,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,QAAQ,MAAM,GAAG,WAAW,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,QAAQ,MAAM,GAAG,SAAS,CAAC;AAAA,EACtU,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,QAAI,KAAK,GAAG;AACV,YAAM,MAAS,2BAAiB;AAChC,MAAG,0BAAgB;AACnB,MAAG,yBAAe,GAAG,SAAS,GAAG,CAAC;AAClC,MAAG,qBAAW,UAAU,SAAS,+CAA+C,QAAQ;AACtF,QAAG,wBAAc,GAAG;AACpB,eAAU,sBAAY,IAAI,SAAS,MAAM,CAAC;AAAA,MAC5C,CAAC;AACD,MAAG,uBAAa;AAChB,MAAG,yBAAe,GAAG,SAAS,CAAC;AAC/B,MAAG,iBAAO,GAAG,OAAO;AACpB,MAAG,yBAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,qBAAW,GAAG,mCAAmC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,gCAAsB;AACpK,MAAG,uBAAa;AAChB,MAAG,yBAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,uBAAa,CAAC;AACjB,MAAG,uBAAa,EAAE;AAAA,IACpB;AACA,QAAI,KAAK,GAAG;AACV,YAAM,kBAAqB,sBAAY,CAAC;AACxC,MAAG,qBAAW,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,QAAQ,EAAE,QAAQ,IAAI,WAAW,aAAa,OAAO,EAAE,SAAS,IAAI,KAAK,EAAE,QAAQ,IAAI,IAAI;AACvI,MAAG,sBAAY,YAAY,IAAI,WAAW,OAAO,CAAC;AAClD,MAAG,oBAAU,CAAC;AACd,MAAG,qBAAW,OAAO,IAAI,EAAE,EAAE,WAAc,0BAAgB,IAAI,KAAK,IAAI,UAAU,IAAI,UAAU,SAAS,IAAI,QAAQ,CAAC;AACtH,MAAG,sBAAY,cAAiB,sBAAY,GAAG,IAAI,IAAI,KAAK,IAAI,YAAY,CAAC,CAAC;AAC9E,MAAG,oBAAU,CAAC;AACd,MAAG,sBAAY,oCAAoC,IAAI,QAAQ;AAC/D,MAAG,oBAAU;AACb,MAAG,qBAAW,QAAQ,CAAC,IAAI,QAAQ,EAAE,YAAY,eAAe;AAAA,IAClE;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,MAAW,eAAkB,SAAS;AAAA,EACpE,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmCZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAW;AAAA,IACb,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc;AAIZ,SAAK,OAAO,cAAc,WAAU,cAAc;AAIlD,SAAK,WAAW;AAahB,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,iBAAiB;AACtB,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,oBAAoB,IAAI,QAAQ;AACrC,SAAK,WAAW,OAAK;AAAA,IAAC;AACtB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,eAAU;AAAA,EACZ;AAAA,EACA,qBAAqB;AACnB,UAAM,cAAc,MAAM;AAExB,WAAK,kBAAkB,KAAK;AAG5B,iBAAW,MAAM;AACf,aAAK,eAAe,QAAQ,UAAQ;AAClC,eAAK,OAAO,KAAK;AACjB,eAAK,OAAO,KAAK,UAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,MAAM;AAClE,iBAAK,SAAS,KAAK;AAAA,cACjB,OAAO,KAAK;AAAA,cACZ,UAAU,KAAK;AAAA,cACf,MAAM,KAAK;AAAA,YACb,CAAC;AACD,iBAAK,SAAS,KAAK,KAAK;AAAA,UAC1B,CAAC;AACD,eAAK,WAAW,KAAK;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,gBAAY;AACZ,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,YAAY,CAAC,EAAE,UAAU,OAAK,YAAY,CAAC;AAAA,EAC7F;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAE3B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,SAAK,eAAe,QAAQ,UAAQ;AAClC,UAAI,KAAK,UAAU,OAAO;AACxB,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,iBAAiB;AAAA,EAC1B;AACF;AACA,UAAU,iBAAiB;AAC3B,UAAU,YAAO,SAAS,kBAAkB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,WAAW;AAC9C;AACA,UAAU,YAAsB,gBAAG,4BAAkB;AAAA,EACnD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,gBAAgB,CAAC;AAAA,EAClD,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,QAAI,KAAK,GAAG;AACV,MAAG,yBAAe,UAAU,eAAe,CAAC;AAAA,IAC9C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,yBAAe,KAAQ,sBAAY,CAAC,MAAM,IAAI,iBAAiB;AAAA,IACpE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,mBAAmB,IAAI,cAAc;AAAA,IACtD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,UAAU;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,6BAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,EACT,CAAC,CAAC,CAAC;AAAA,EACH,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,SAAS,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,EACrI,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,QAAI,KAAK,GAAG;AACV,MAAG,0BAAgB,GAAG;AACtB,MAAG,yBAAe,GAAG,UAAU;AAC/B,MAAG,qBAAW,GAAG,6BAA6B,GAAG,GAAG,UAAU,CAAC;AAC/D,MAAG,uBAAa,CAAC;AACjB,MAAG,uBAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,oBAAU;AACb,MAAG,qBAAW,QAAQ,IAAI,MAAM;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,gBAAgB;AAAA,EAC3C,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,EACV,GAAG;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAiBH,IAAM,OAAN,MAAW;AAAA,EACT,cAAc;AACZ,SAAK,YAAY;AAKjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,UAAU;AAAA,EACxB;AACF;AACA,KAAK,YAAO,SAAS,aAAa,mBAAmB;AACnD,SAAO,KAAK,qBAAqB,MAAM;AACzC;AACA,KAAK,YAAsB,gBAAG,4BAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,UAAU,CAAC;AAAA,EACtC,UAAU;AAAA,EACV,cAAc,SAAS,kBAAkB,IAAI,KAAK;AAChD,QAAI,KAAK,GAAG;AACV,MAAG,sBAAY,aAAa,IAAI,SAAS,EAAE,oBAAoB,IAAI,iBAAiB;AAAA,IACtF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,cAAc,IAAI,KAAK;AACxC,QAAI,KAAK,GAAG;AACV,MAAG,0BAAgB;AACnB,MAAG,uBAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAkB;AAAC;AACnB,YAAY,YAAO,SAAS,oBAAoB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,aAAa;AAChD;AACA,YAAY,YAAsB,gBAAG,2BAAiB;AAAA,EACpD,MAAM;AAAA,EACN,cAAc,CAAC,MAAM,eAAe,4BAA4B,kCAAkC,kCAAkC,gBAAgB,eAAe,SAAS;AAAA,EAC5K,SAAS,CAAC,cAAc,YAAY,YAAY,UAAU;AAAA,EAC1D,SAAS,CAAC,MAAM,eAAe,4BAA4B,kCAAkC,kCAAkC,gBAAgB,eAAe,SAAS;AACzK,CAAC;AACD,YAAY,YAAsB,gBAAG,2BAAiB;AAAA,EACpD,SAAS,CAAC,cAAc,YAAY,YAAY,UAAU;AAC5D,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,MAAM,eAAe,4BAA4B,kCAAkC,kCAAkC,gBAAgB,eAAe,SAAS;AAAA,MAC5K,SAAS,CAAC,MAAM,eAAe,4BAA4B,kCAAkC,kCAAkC,gBAAgB,eAAe,SAAS;AAAA,MACvK,SAAS,CAAC,cAAc,YAAY,YAAY,UAAU;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7iCG,IAAO,uBAAP,MAAO,sBAAoB;EACJ;EACjB,QAAQ,IAAI,aAAY;EAElC,UAAO;AACL,SAAK,MAAM,KAAK,KAAK,OAAO;EAC9B;;qCANW,uBAAoB;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,QAAA,EAAA,SAAA,UAAA,GAAA,SAAA,EAAA,OAAA,QAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,aAAA,WAAA,QAAA,MAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA3D7B,MAAA,yBAAA,GAAA,YAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EACK,GAAA,OAAA,CAAA,EACN,GAAA,MAAA,CAAA;AACE,MAAA,iBAAA,CAAA;AAAmB,MAAA,uBAAA;AAC7C,MAAA,yBAAA,GAAA,QAAA,CAAA;AAA2B,MAAA,iBAAA,CAAA;AAAkB,MAAA,uBAAA,EAAO;AAEtD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,UAAA,CAAA;AAIzB,MAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClB,MAAA,iBAAA,GAAA,SAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;;;AAXwB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,QAAA,KAAA;AACC,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,QAAA,IAAA;;;IATjC;IACA;IAAY;IACZ;IAAW;EAAA,GAAA,QAAA,CAAA,izBAAA,EAAA,CAAA;;;sEA8DF,sBAAoB,CAAA;UApEhC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;KAiBT,QAAA,CAAA,s3BAAA,EAAA,CAAA;cA4C0B,SAAO,CAAA;UAAjC;WAAM,EAAE,UAAU,KAAI,CAAE;MACf,OAAK,CAAA;UAAd;;;;6EAFU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,gDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;;;;;AC/BrB,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAyB,GAAA,OAAA,CAAA,EACU,GAAA,IAAA;AAC3B,IAAA,iBAAA,GAAA,iBAAA;AAAe,IAAA,uBAAA;AACnB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,2CAAA;AAAyC,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AACrB,IAAA,iBAAA,GAAA,wBAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;;;AAMJ,IAAA,yBAAA,GAAA,oBAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,2FAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA;AAC/B,IAAA,uBAAA;;;;AAFE,IAAA,qBAAA,WAAA,UAAA;;;;;AAHN,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,2BAAA,GAAA,0DAAA,GAAA,GAAA,oBAAA,IAAA,UAAA;AAMF,IAAA,uBAAA;;;;AANE,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA;;;;;AAhBJ,IAAA,8BAAA,GAAA,oDAAA,GAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,oDAAA,GAAA,GAAA,OAAA,CAAA;;;AAA7B,IAAA,wBAAA,IAAA,WAAA,IAAA,IAAA,CAAA;;;;;AA0BA,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA,EAAI;;;AA4GhC,IAAO,eAAP,MAAO,cAAY;EACf,kBAAkB,OAAO,eAAe;EACxC,OAAO,OAAO,WAAW;EACzB,SAAS,OAAO,MAAM;EAE9B,YAAY,KAAK,gBAAgB;EAEjC,MAAM,aAAU;AACd,QAAI;AACF,YAAM,KAAK,gBAAgB,cAAc,eAAe,SAAS;IACnE,SAAS,OAAO;AACd,cAAQ,MAAM,2BAA2B,KAAK;IAChD;EACF;EAEA,aAAa,SAAgB;AAC3B,YAAQ,IAAI,qBAAqB,OAAO;EAG1C;EAEA,MAAM,UAAO;AACX,QAAI;AACF,cAAQ,IAAI,gBAAgB;AAC5B,YAAM,KAAK,KAAK,QAAO;AACvB,cAAQ,IAAI,6CAA6C;AAGzD,iBAAW,YAAW;AACpB,cAAM,KAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACvC,GAAG,GAAG;IACR,SAAS,OAAO;AACd,cAAQ,MAAM,mBAAmB,KAAK;IACxC;EACF;;qCAlCW,eAAY;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,aAAA,WAAA,QAAA,MAAA,GAAA,OAAA,GAAA,CAAA,aAAA,aAAA,QAAA,MAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,aAAA,WAAA,QAAA,MAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,SAAA,CAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA/JrB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,UAAA,CAAA,EACE,GAAA,IAAA;AAC1B,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AAIxB,MAAA,qBAAA,SAAA,SAAA,gDAAA;AAAA,eAAS,IAAA,WAAA;MAAY,CAAA;AACrB,MAAA,iBAAA,GAAA,iBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,UAAA,CAAA;AAGE,MAAA,qBAAA,SAAA,SAAA,gDAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClB,MAAA,iBAAA,GAAA,YAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,yBAAA,GAAA,QAAA,CAAA;AAEE,MAAA,8BAAA,IAAA,sCAAA,GAAA,CAAA;;AA0BE,MAAA,oCAAA,IAAA,sCAAA,GAAA,GAAA,OAAA,CAAA;AAOJ,MAAA,uBAAA,EAAO;;;;AAjCL,MAAA,oBAAA,EAAA;AAAA,MAAA,yBAAA,UAAA,sBAAA,IAAA,GAAA,IAAA,SAAA,KAAA,KAAA,IAAA,OAAA;;;IA3BJ;IACA;IAAY;IACZ;IAAa;IACb;IAAoB;EAAA,GAAA,QAAA,CAAA,q+DAAA,EAAA,CAAA;;;sEAkKX,cAAY,CAAA;UAzKxB;uBACW,qBAAmB,YACjB,MAAI,SACP;MACP;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyDT,QAAA,CAAA,gyDAAA,EAAA,CAAA;;;;6EAuGU,cAAY,EAAA,WAAA,gBAAA,UAAA,qCAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": [], "x_google_ignoreList": [1, 2]}