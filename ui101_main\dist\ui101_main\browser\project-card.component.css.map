{"version": 3, "sources": ["src/app/components/project-card.component.ts"], "sourcesContent": ["\n    .project-card {\n      margin-bottom: 1rem;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n    \n    .project-card:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n    }\n    \n    .project-card-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 0.5rem;\n    }\n    \n    .project-info {\n      flex: 1;\n    }\n    \n    .project-title {\n      margin: 0 0 0.25rem 0;\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #161616;\n    }\n    \n    .project-type {\n      font-size: 0.875rem;\n      color: #6f6f6f;\n      text-transform: uppercase;\n      font-weight: 500;\n    }\n    \n    .project-actions {\n      margin-left: 1rem;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,iBAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;;AAGF,CANA,YAMA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,QAAA;;AAGF,CAAA;AACE,UAAA,EAAA,EAAA,QAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CAAA;AACE,eAAA;;", "names": []}