import {
  Loading,
  LoadingModule,
  getFirestore,
  provideFirestore
} from "./chunk-5UGBLMKE.js";
import {
  AsyncPipe,
  Auth,
  CommonModule,
  Component,
  PLATFORM_ID,
  Router,
  RouterOutlet,
  authState,
  bootstrapApplication,
  browserLocalPersistence,
  getAuth,
  initializeApp,
  inject,
  isPlatformBrowser,
  map,
  provideAuth,
  provideBrowserGlobalErrorListeners,
  provideClientHydration,
  provideFirebaseApp,
  provideRouter,
  provideZonelessChangeDetection,
  setClassMetadata,
  setPersistence,
  signal,
  startWith,
  take,
  withEventReplay,
  ɵsetClassDebugInfo,
  ɵɵconditional,
  ɵɵconditionalBranchCreate,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵtext
} from "./chunk-V3EQCA3M.js";

// src/app/app.routes.ts
var authGuard = () => {
  const auth = inject(Auth), router = inject(Router);
  return authState(auth).pipe(take(1), map((u) => {
    console.log("AuthGuard: User state:", u);
    return u ? true : router.createUrlTree(["/login"]);
  }));
};
var redirectLoggedInGuard = () => {
  const auth = inject(Auth), router = inject(Router);
  return authState(auth).pipe(take(1), map((u) => {
    console.log("RedirectLoggedInGuard: User state:", u);
    return u ? router.createUrlTree(["/projects"]) : true;
  }));
};
var routes = [
  { path: "login", canActivate: [redirectLoggedInGuard], loadComponent: () => import("./chunk-AU74Z7LT.js").then((m) => m.LoginComponent) },
  { path: "projects", canActivate: [authGuard], loadComponent: () => import("./chunk-COMGYDWR.js").then((m) => m.ProjectsPage) },
  { path: "**", redirectTo: "projects" }
];

// src/environments/environments.dev.ts
var environment = {
  production: false,
  firebaseConfig: {
    apiKey: "AIzaSyDts9OKt9xyFy5SemqQ_ZF26hulhdvck7Q",
    authDomain: "aa103-poc.firebaseapp.com",
    projectId: "aa103-poc",
    storageBucket: "aa103-poc.firebasestorage.app",
    messagingSenderId: "50286062087",
    appId: "1:50286062087:web:35e0b95924835cc7f518a2",
    measurementId: "G-2SZKT467MZ"
  }
};

// src/app/app.config.ts
var appConfig = {
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideZonelessChangeDetection(),
    provideRouter(routes),
    provideClientHydration(withEventReplay()),
    provideFirebaseApp(() => initializeApp(environment.firebaseConfig)),
    provideAuth(() => {
      const auth = getAuth();
      const platformId = inject(PLATFORM_ID);
      if (isPlatformBrowser(platformId)) {
        setPersistence(auth, browserLocalPersistence);
      }
      return auth;
    }),
    provideFirestore(() => getFirestore())
  ]
};

// src/app/components/loading.component.ts
var LoadingComponent = class _LoadingComponent {
  static \u0275fac = function LoadingComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoadingComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoadingComponent, selectors: [["app-loading"]], decls: 5, vars: 0, consts: [[1, "loading-container"], [1, "loading-content"], ["size", "normal"], [1, "loading-text"]], template: function LoadingComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
      \u0275\u0275element(2, "ibm-loading", 2);
      \u0275\u0275elementStart(3, "p", 3);
      \u0275\u0275text(4, "Loading...");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [
    CommonModule,
    LoadingModule,
    Loading
  ], styles: ["\n\n.loading-container[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(255, 255, 255, 0.9);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n.loading-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n.loading-text[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1rem;\n  color: #525252;\n}\n/*# sourceMappingURL=loading.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoadingComponent, [{
    type: Component,
    args: [{ selector: "app-loading", standalone: true, imports: [
      CommonModule,
      LoadingModule
    ], template: `
    <div class="loading-container">
      <div class="loading-content">
        <ibm-loading size="normal"></ibm-loading>
        <p class="loading-text">Loading...</p>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;6f26d0adba53154f03b8d17a881d2f58e2370f14cac158d40188c1fc78d09577;C:/Users/<USER>/ANACE/Dev/UI101_main_dashboard/ui101_main/src/app/components/loading.component.ts */\n.loading-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(255, 255, 255, 0.9);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n.loading-text {\n  margin: 0;\n  font-size: 1rem;\n  color: #525252;\n}\n/*# sourceMappingURL=loading.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoadingComponent, { className: "LoadingComponent", filePath: "src/app/components/loading.component.ts", lineNumber: 50 });
})();

// src/app/app.ts
function App_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "app-loading");
  }
}
function App_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "router-outlet");
  }
}
var App = class _App {
  title = signal("ui101_main", ...ngDevMode ? [{ debugName: "title" }] : []);
  auth = inject(Auth);
  // Track if auth state is still loading (null at startup until Firebase restores session)
  isAuthLoading$ = authState(this.auth).pipe(
    startWith(void 0),
    // Start with undefined to show loading initially
    map((user) => user === void 0)
    // Show loading only when user is undefined (initial state)
  );
  ngOnInit() {
  }
  static \u0275fac = function App_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _App)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _App, selectors: [["app-root"]], decls: 3, vars: 3, template: function App_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275conditionalCreate(0, App_Conditional_0_Template, 1, 0, "app-loading");
      \u0275\u0275pipe(1, "async");
      \u0275\u0275conditionalBranchCreate(2, App_Conditional_2_Template, 1, 0, "router-outlet");
    }
    if (rf & 2) {
      \u0275\u0275conditional(\u0275\u0275pipeBind1(1, 1, ctx.isAuthLoading$) ? 0 : 2);
    }
  }, dependencies: [RouterOutlet, CommonModule, LoadingComponent, AsyncPipe], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(App, [{
    type: Component,
    args: [{ selector: "app-root", imports: [RouterOutlet, CommonModule, LoadingComponent], template: `
    @if (isAuthLoading$ | async) {
      <app-loading></app-loading>
    } @else {
      <router-outlet></router-outlet>
    }
  ` }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(App, { className: "App", filePath: "src/app/app.ts", lineNumber: 20 });
})();

// src/main.ts
bootstrapApplication(App, appConfig).catch((err) => console.error(err));
//# sourceMappingURL=main.js.map
