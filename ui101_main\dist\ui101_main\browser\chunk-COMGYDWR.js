import {
  Firestore,
  I18n,
  I18nModule,
  Loading,
  LoadingModule,
  collection,
  collectionData,
  doc,
  limit,
  merge,
  orderBy,
  query,
  serverTimestamp,
  setDoc
} from "./chunk-5UGBLMKE.js";
import {
  AuthService,
  Button,
  ButtonModule,
  IconDirective,
  IconModule,
  NG_VALUE_ACCESSOR
} from "./chunk-VEYUXI7N.js";
import {
  AsyncPipe,
  Auth,
  CommonModule,
  Component,
  ContentChildren,
  Directive,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Injectable,
  Input,
  NgClass,
  NgIf,
  NgModule,
  NgStyle,
  NgTemplateOutlet,
  Optional,
  Output,
  Router,
  Subject,
  TemplateRef,
  ViewChild,
  authState,
  inject,
  map,
  of,
  setClassMetadata,
  switchMap,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalBranchCreate,
  ɵɵconditionalCreate,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵpureFunction3,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-V3EQCA3M.js";

// src/app/services/projects.service.ts
var ProjectsService = class _ProjectsService {
  db = inject(Firestore);
  auth = inject(Auth);
  /** Realtime stream of the current user's projects (ordered by updatedAt desc, limited to 5) */
  projects$ = authState(this.auth).pipe(switchMap((user) => {
    if (!user)
      return of([]);
    const col = collection(this.db, `users/${user.uid}/projects`);
    const q = query(col, orderBy("updatedAt", "desc"), limit(5));
    return collectionData(q, { idField: "id" }).pipe(map((r) => r));
  }));
  /** Create a project with auto-ID and echo id into the document */
  async createProject(title, type) {
    const user = this.auth.currentUser;
    if (!user)
      throw new Error("Not signed in");
    const col = collection(this.db, `users/${user.uid}/projects`);
    const ref = doc(col);
    const data = {
      id: ref.id,
      title: title.trim(),
      type,
      ownerUid: user.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    await setDoc(ref, data);
    return ref.id;
  }
  static \u0275fac = function ProjectsService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProjectsService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ProjectsService, factory: _ProjectsService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProjectsService, [{
    type: Injectable,
    args: [{ providedIn: "root" }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-link.mjs
var Link = class {
  constructor() {
    this.baseClass = true;
    this.inline = false;
  }
  /**
   * Set to true to disable link.
   */
  set disabled(disabled) {
    this._disabled = disabled;
    this.tabindex = this.disabled ? -1 : null;
  }
  get disabled() {
    return this._disabled;
  }
};
Link.\u0275fac = function Link_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Link)();
};
Link.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: Link,
  selectors: [["", "cdsLink", ""], ["", "ibmLink", ""]],
  hostVars: 8,
  hostBindings: function Link_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275attribute("tabindex", ctx.tabindex)("aria-disabled", ctx.disabled);
      \u0275\u0275classProp("cds--link", ctx.baseClass)("cds--link--inline", ctx.inline)("cds--link--disabled", ctx.disabled);
    }
  },
  inputs: {
    inline: "inline",
    disabled: "disabled"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Link, [{
    type: Directive,
    args: [{
      selector: "[cdsLink], [ibmLink]"
    }]
  }], null, {
    baseClass: [{
      type: HostBinding,
      args: ["class.cds--link"]
    }],
    tabindex: [{
      type: HostBinding,
      args: ["attr.tabindex"]
    }],
    inline: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["class.cds--link--inline"]
    }],
    disabled: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["attr.aria-disabled"]
    }, {
      type: HostBinding,
      args: ["class.cds--link--disabled"]
    }]
  });
})();
var LinkIconDirective = class {
  constructor() {
    this.iconClass = true;
  }
};
LinkIconDirective.\u0275fac = function LinkIconDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LinkIconDirective)();
};
LinkIconDirective.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: LinkIconDirective,
  selectors: [["", "ibmLinkIcon", ""], ["", "cdsLinkIcon", ""]],
  hostVars: 2,
  hostBindings: function LinkIconDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--link__icon", ctx.iconClass);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LinkIconDirective, [{
    type: Directive,
    args: [{
      selector: "[ibmLinkIcon], [cdsLinkIcon]"
    }]
  }], null, {
    iconClass: [{
      type: HostBinding,
      args: ["class.cds--link__icon"]
    }]
  });
})();
var LinkModule = class {
};
LinkModule.\u0275fac = function LinkModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LinkModule)();
};
LinkModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: LinkModule,
  declarations: [Link, LinkIconDirective],
  imports: [CommonModule],
  exports: [Link, LinkIconDirective]
});
LinkModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LinkModule, [{
    type: NgModule,
    args: [{
      declarations: [Link, LinkIconDirective],
      exports: [Link, LinkIconDirective],
      imports: [CommonModule]
    }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-tiles.mjs
var _c0 = ["*"];
var _c1 = (a0, a1) => ({
  "cds--tile--light": a0,
  "cds--tile--disabled cds--link--disabled": a1
});
var _c2 = ["container"];
var _c3 = [[["", "cdsAboveFold", ""], ["", "ibmAboveFold", ""], ["", 8, "cds--tile-content__above-the-fold"]], [["", "cdsBelowFold", ""], ["", "ibmBelowFold", ""], ["", 8, "cds--tile-content__below-the-fold"]]];
var _c4 = ["[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold", "[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold"];
var _c5 = (a0, a1) => ({
  "cds--tile--is-expanded": a0,
  "cds--tile--light": a1
});
var _c6 = (a0) => ({
  "max-height": a0
});
function ExpandableTile_button_0_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainer(0);
  }
}
function ExpandableTile_button_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 5);
    \u0275\u0275pipe(1, "async");
    \u0275\u0275listener("click", function ExpandableTile_button_0_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onClick());
    });
    \u0275\u0275template(2, ExpandableTile_button_0_ng_container_2_Template, 1, 0, "ng-container", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    const expandableTileContent_r3 = \u0275\u0275reference(5);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction2(7, _c5, ctx_r1.expanded, ctx_r1.theme === "light"))("ngStyle", \u0275\u0275pureFunction1(10, _c6, ctx_r1.expandedHeight + "px"));
    \u0275\u0275attribute("aria-expanded", ctx_r1.expanded)("title", \u0275\u0275pipeBind1(1, 5, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngTemplateOutlet", expandableTileContent_r3);
  }
}
function ExpandableTile_div_1_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainer(0);
  }
}
function ExpandableTile_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7);
    \u0275\u0275pipe(1, "async");
    \u0275\u0275template(2, ExpandableTile_div_1_ng_container_2_Template, 1, 0, "ng-container", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    const expandableTileContent_r3 = \u0275\u0275reference(5);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction2(6, _c5, ctx_r1.expanded, ctx_r1.theme === "light"))("ngStyle", \u0275\u0275pureFunction1(9, _c6, ctx_r1.expandedHeight + "px"));
    \u0275\u0275attribute("title", \u0275\u0275pipeBind1(1, 4, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngTemplateOutlet", expandableTileContent_r3);
  }
}
function ExpandableTile_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 8);
  }
}
function ExpandableTile_ng_template_4_div_4_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainer(0);
  }
}
function ExpandableTile_ng_template_4_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275template(1, ExpandableTile_ng_template_4_div_4_ng_container_1_Template, 1, 0, "ng-container", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    \u0275\u0275nextContext(2);
    const chevronIcon_r4 = \u0275\u0275reference(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngTemplateOutlet", chevronIcon_r4);
  }
}
function ExpandableTile_ng_template_4_button_5_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainer(0);
  }
}
function ExpandableTile_ng_template_4_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 13);
    \u0275\u0275pipe(1, "async");
    \u0275\u0275listener("click", function ExpandableTile_ng_template_4_button_5_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onClick());
    });
    \u0275\u0275template(2, ExpandableTile_ng_template_4_button_5_ng_container_2_Template, 1, 0, "ng-container", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    const chevronIcon_r4 = \u0275\u0275reference(3);
    \u0275\u0275attribute("aria-expanded", ctx_r1.expanded)("aria-label", \u0275\u0275pipeBind1(1, 3, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngTemplateOutlet", chevronIcon_r4);
  }
}
function ExpandableTile_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", null, 2)(2, "div", 9);
    \u0275\u0275projection(3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ExpandableTile_ng_template_4_div_4_Template, 2, 1, "div", 10)(5, ExpandableTile_ng_template_4_button_5_Template, 3, 5, "button", 11);
    \u0275\u0275elementStart(6, "div", 9);
    \u0275\u0275projection(7, 1);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", !ctx_r1.interactive);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.interactive);
  }
}
var _c7 = ["input"];
var _c8 = (a0, a1, a2) => ({
  "cds--tile--is-selected": a0,
  "cds--tile--light": a1,
  "cds--tile--disabled": a2
});
function SelectionTile__svg_svg_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 7);
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("cdsIcon", ctx_r1.multiple ? "checkbox" : "checkmark");
  }
}
function SelectionTile_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 7);
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("cdsIcon", ctx_r1.multiple ? "checkbox--checked--filled" : "checkmark--filled");
  }
}
var _c9 = [[["ibm-selection-tile"], ["cds-selection-tile"]]];
var _c10 = ["ibm-selection-tile,cds-selection-tile"];
function TileGroup_legend_1_1_ng_template_0_Template(rf, ctx) {
}
function TileGroup_legend_1_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TileGroup_legend_1_1_ng_template_0_Template, 0, 0, "ng-template", 4);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.legend);
  }
}
function TileGroup_legend_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275text(0);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275textInterpolate(ctx_r0.legend);
  }
}
function TileGroup_legend_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "legend", 2);
    \u0275\u0275template(1, TileGroup_legend_1_1_Template, 1, 1, null, 3)(2, TileGroup_legend_1_ng_template_2_Template, 1, 1, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const legendLabel_r2 = \u0275\u0275reference(3);
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.legend))("ngIfElse", legendLabel_r2);
  }
}
var ClickableTileIconDirective = class {
  constructor() {
    this.icon = true;
  }
};
ClickableTileIconDirective.\u0275fac = function ClickableTileIconDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ClickableTileIconDirective)();
};
ClickableTileIconDirective.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: ClickableTileIconDirective,
  selectors: [["", "cdsClickableTileIcon", ""], ["", "ibmClickableTileIcon", ""]],
  hostVars: 2,
  hostBindings: function ClickableTileIconDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--tile--icon", ctx.icon);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClickableTileIconDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsClickableTileIcon], [ibmClickableTileIcon]"
    }]
  }], null, {
    icon: [{
      type: HostBinding,
      args: ["class.cds--tile--icon"]
    }]
  });
})();
var ClickableTile = class {
  constructor(router) {
    this.router = router;
    this.theme = "dark";
    this.href = "#";
    this.disabled = false;
    this.navigation = new EventEmitter();
  }
  navigate(event) {
    if (this.router && this.route && !this.disabled) {
      event.preventDefault();
      const status = this.router.navigate(this.route, this.routeExtras);
      this.navigation.emit(status);
    }
  }
};
ClickableTile.\u0275fac = function ClickableTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ClickableTile)(\u0275\u0275directiveInject(Router, 8));
};
ClickableTile.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: ClickableTile,
  selectors: [["cds-clickable-tile"], ["ibm-clickable-tile"]],
  inputs: {
    theme: "theme",
    href: "href",
    target: "target",
    rel: "rel",
    disabled: "disabled",
    route: "route",
    routeExtras: "routeExtras"
  },
  outputs: {
    navigation: "navigation"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 2,
  vars: 8,
  consts: [["cdsLink", "", "tabindex", "0", 1, "cds--tile", "cds--tile--clickable", 3, "click", "ngClass"]],
  template: function ClickableTile_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "a", 0);
      \u0275\u0275listener("click", function ClickableTile_Template_a_click_0_listener($event) {
        return ctx.navigate($event);
      });
      \u0275\u0275projection(1);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction2(5, _c1, ctx.theme === "light", ctx.disabled));
      \u0275\u0275attribute("href", ctx.disabled ? null : ctx.href, \u0275\u0275sanitizeUrl)("target", ctx.target)("rel", ctx.rel ? ctx.rel : null)("aria-disabled", ctx.disabled);
    }
  },
  dependencies: [NgClass, Link],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClickableTile, [{
    type: Component,
    args: [{
      selector: "cds-clickable-tile, ibm-clickable-tile",
      template: `
	<a
		cdsLink
		class="cds--tile cds--tile--clickable"
		[ngClass]="{
			'cds--tile--light': theme === 'light',
			'cds--tile--disabled cds--link--disabled' : disabled
		}"
		tabindex="0"
		(click)="navigate($event)"
		[attr.href]="disabled ? null : href"
		[attr.target]="target"
		[attr.rel]="rel ? rel : null"
		[attr.aria-disabled]="disabled">
		<ng-content></ng-content>
	</a>`
    }]
  }], function() {
    return [{
      type: Router,
      decorators: [{
        type: Optional
      }]
    }];
  }, {
    theme: [{
      type: Input
    }],
    href: [{
      type: Input
    }],
    target: [{
      type: Input
    }],
    rel: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    route: [{
      type: Input
    }],
    routeExtras: [{
      type: Input
    }],
    navigation: [{
      type: Output
    }]
  });
})();
var ExpandableTile = class {
  constructor(i18n, element) {
    this.i18n = i18n;
    this.element = element;
    this.theme = "dark";
    this.expanded = false;
    this.interactive = false;
    this.tileMaxHeight = 0;
    this.currentExpandedHeight = 0;
    this.expand = this.i18n.getOverridable("TILES.EXPAND");
    this.collapse = this.i18n.getOverridable("TILES.COLLAPSE");
  }
  /**
   * Expects an object that contains some or all of:
   * ```
   * {
   *		"EXPAND": "Expand",
   *		"COLLAPSE": "Collapse",
   * }
   * ```
   */
  set translations(value) {
    const valueWithDefaults = merge(this.i18n.getMultiple("TILES"), value);
    this.expand.override(valueWithDefaults.EXPAND);
    this.collapse.override(valueWithDefaults.COLLAPSE);
  }
  ngAfterViewInit() {
    this.updateMaxHeight();
  }
  get expandedHeight() {
    const tile = this.element.nativeElement.querySelector(".cds--tile");
    const tilePadding = parseInt(getComputedStyle(tile).paddingBottom, 10) + parseInt(getComputedStyle(tile).paddingTop, 10);
    const expandedHeight = this.tileMaxHeight + tilePadding;
    if (!isNaN(expandedHeight)) {
      this.currentExpandedHeight = expandedHeight;
    }
    return this.currentExpandedHeight;
  }
  updateMaxHeight() {
    if (this.expanded) {
      this.tileMaxHeight = this.tileContainer.nativeElement.getBoundingClientRect().height;
    } else {
      this.tileMaxHeight = this.element.nativeElement.querySelector(".cds--tile-content__above-the-fold").getBoundingClientRect().height;
    }
  }
  onClick() {
    this.expanded = !this.expanded;
    this.updateMaxHeight();
  }
};
ExpandableTile.\u0275fac = function ExpandableTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTile)(\u0275\u0275directiveInject(I18n), \u0275\u0275directiveInject(ElementRef));
};
ExpandableTile.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: ExpandableTile,
  selectors: [["cds-expandable-tile"], ["ibm-expandable-tile"]],
  viewQuery: function ExpandableTile_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c2, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.tileContainer = _t.first);
    }
  },
  inputs: {
    theme: "theme",
    expanded: "expanded",
    interactive: "interactive",
    translations: "translations"
  },
  standalone: false,
  ngContentSelectors: _c4,
  decls: 6,
  vars: 2,
  consts: [["chevronIcon", ""], ["expandableTileContent", ""], ["container", ""], ["class", "cds--tile cds--tile--expandable", "type", "button", 3, "ngClass", "ngStyle", "click", 4, "ngIf"], ["class", "cds--tile cds--tile--expandable cds--tile--expandable--interactive", 3, "ngClass", "ngStyle", 4, "ngIf"], ["type", "button", 1, "cds--tile", "cds--tile--expandable", 3, "click", "ngClass", "ngStyle"], [4, "ngTemplateOutlet"], [1, "cds--tile", "cds--tile--expandable", "cds--tile--expandable--interactive", 3, "ngClass", "ngStyle"], ["cdsIcon", "chevron--down", "size", "16"], [1, "cds--tile-content"], ["class", "cds--tile__chevron", 4, "ngIf"], ["class", "cds--tile__chevron cds--tile__chevron--interactive", "type", "button", 3, "click", 4, "ngIf"], [1, "cds--tile__chevron"], ["type", "button", 1, "cds--tile__chevron", "cds--tile__chevron--interactive", 3, "click"]],
  template: function ExpandableTile_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c3);
      \u0275\u0275template(0, ExpandableTile_button_0_Template, 3, 12, "button", 3)(1, ExpandableTile_div_1_Template, 3, 11, "div", 4)(2, ExpandableTile_ng_template_2_Template, 1, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor)(4, ExpandableTile_ng_template_4_Template, 8, 2, "ng-template", null, 1, \u0275\u0275templateRefExtractor);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", !ctx.interactive);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.interactive);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, NgStyle, IconDirective, AsyncPipe],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTile, [{
    type: Component,
    args: [{
      selector: "cds-expandable-tile, ibm-expandable-tile",
      template: `
		<button
			*ngIf="!interactive"
			class="cds--tile cds--tile--expandable"
			[ngClass]="{
				'cds--tile--is-expanded' : expanded,
				'cds--tile--light': theme === 'light'
			}"
			[ngStyle]="{'max-height': expandedHeight + 'px'}"
			type="button"
			(click)="onClick()"
			[attr.aria-expanded]="expanded"
			[attr.title]="(expanded ? collapse.subject : expand.subject) | async">
				<ng-container *ngTemplateOutlet="expandableTileContent"></ng-container>
		</button>

		<div
			*ngIf="interactive"
			class="cds--tile cds--tile--expandable cds--tile--expandable--interactive"
			[ngClass]="{
				'cds--tile--is-expanded' : expanded,
				'cds--tile--light': theme === 'light'
			}"
			[ngStyle]="{'max-height': expandedHeight + 'px'}"
			[attr.title]="(expanded ? collapse.subject : expand.subject) | async">
			<ng-container *ngTemplateOutlet="expandableTileContent"></ng-container>
		</div>

		<ng-template #chevronIcon>
			<svg cdsIcon="chevron--down" size="16"></svg>
		</ng-template>

		<ng-template #expandableTileContent>
			<div #container>
				<div class="cds--tile-content">
					<ng-content select="[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold"></ng-content>
				</div>
				<div *ngIf="!interactive" class="cds--tile__chevron">
					<ng-container *ngTemplateOutlet="chevronIcon"></ng-container>
				</div>
				<button
					*ngIf="interactive"
					class="cds--tile__chevron cds--tile__chevron--interactive"
					type="button"
					(click)="onClick()"
					[attr.aria-expanded]="expanded"
					[attr.aria-label]="(expanded ? collapse.subject : expand.subject) | async">
					<ng-container *ngTemplateOutlet="chevronIcon"></ng-container>
				</button>
				<div class="cds--tile-content">
					<ng-content select="[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold"></ng-content>
				</div>
			</div>
		</ng-template>
	`
    }]
  }], function() {
    return [{
      type: I18n
    }, {
      type: ElementRef
    }];
  }, {
    theme: [{
      type: Input
    }],
    expanded: [{
      type: Input
    }],
    interactive: [{
      type: Input
    }],
    translations: [{
      type: Input
    }],
    tileContainer: [{
      type: ViewChild,
      args: ["container"]
    }]
  });
})();
var ExpandableTileAboveFoldDirective = class {
  constructor() {
    this.aboveFold = true;
  }
};
ExpandableTileAboveFoldDirective.\u0275fac = function ExpandableTileAboveFoldDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTileAboveFoldDirective)();
};
ExpandableTileAboveFoldDirective.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: ExpandableTileAboveFoldDirective,
  selectors: [["", "cdsAboveFold", ""], ["", "ibmAboveFold", ""]],
  hostVars: 2,
  hostBindings: function ExpandableTileAboveFoldDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--tile-content__above-the-fold", ctx.aboveFold);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTileAboveFoldDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsAboveFold], [ibmAboveFold]"
    }]
  }], null, {
    aboveFold: [{
      type: HostBinding,
      args: ["class.cds--tile-content__above-the-fold"]
    }]
  });
})();
var ExpandableTileBelowFoldDirective = class {
  constructor() {
    this.belowFold = true;
  }
};
ExpandableTileBelowFoldDirective.\u0275fac = function ExpandableTileBelowFoldDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTileBelowFoldDirective)();
};
ExpandableTileBelowFoldDirective.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: ExpandableTileBelowFoldDirective,
  selectors: [["", "cdsBelowFold", ""], ["", "ibmBelowFold", ""]],
  hostVars: 2,
  hostBindings: function ExpandableTileBelowFoldDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--tile-content__below-the-fold", ctx.belowFold);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTileBelowFoldDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsBelowFold], [ibmBelowFold]"
    }]
  }], null, {
    belowFold: [{
      type: HostBinding,
      args: ["class.cds--tile-content__below-the-fold"]
    }]
  });
})();
var SelectionTile = class _SelectionTile {
  constructor(i18n) {
    this.i18n = i18n;
    this.theme = "dark";
    this.id = `tile-${_SelectionTile.tileCount}`;
    this.change = new EventEmitter();
    this.disabled = false;
    this.name = "tile-group-unbound";
    this.multiple = true;
    this._selected = null;
    _SelectionTile.tileCount++;
  }
  /**
   * Updating the state of the input to match the state of the parameter passed in.
   * Set to `true` if this tile should be selected.
   */
  set selected(value) {
    this._selected = value ? true : null;
    if (this.input) {
      this.input.nativeElement.checked = this._selected;
    }
  }
  get selected() {
    return this.input ? this.input.nativeElement.checked : false;
  }
  ngAfterViewInit() {
    if (this.input) {
      setTimeout(() => {
        this.input.nativeElement.checked = this._selected;
      });
    }
  }
  keyboardInput(event) {
    if (event.key === "Enter" || event.key === "Spacebar" || event.key === " ") {
      this.selected = !this.selected;
      this.change.emit(event);
    }
  }
  onChange(event) {
    this.change.emit(event);
  }
};
SelectionTile.tileCount = 0;
SelectionTile.\u0275fac = function SelectionTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || SelectionTile)(\u0275\u0275directiveInject(I18n));
};
SelectionTile.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: SelectionTile,
  selectors: [["cds-selection-tile"], ["ibm-selection-tile"]],
  viewQuery: function SelectionTile_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c7, 7);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.input = _t.first);
    }
  },
  hostBindings: function SelectionTile_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("keydown", function SelectionTile_keydown_HostBindingHandler($event) {
        return ctx.keyboardInput($event);
      });
    }
  },
  inputs: {
    theme: "theme",
    id: "id",
    selected: "selected",
    value: "value",
    disabled: "disabled"
  },
  outputs: {
    change: "change"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 10,
  vars: 19,
  consts: [["input", ""], ["selectedIcon", ""], [1, "cds--tile-input", 3, "change", "id", "disabled", "type", "value", "name"], [1, "cds--tile", "cds--tile--selectable", 3, "for", "ngClass"], [1, "cds--tile__checkmark"], ["size", "16", 3, "cdsIcon", 4, "ngIf", "ngIfElse"], [1, "cds--tile-content"], ["size", "16", 3, "cdsIcon"]],
  template: function SelectionTile_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "input", 2, 0);
      \u0275\u0275listener("change", function SelectionTile_Template_input_change_0_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.onChange($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(2, "label", 3);
      \u0275\u0275pipe(3, "async");
      \u0275\u0275elementStart(4, "div", 4);
      \u0275\u0275template(5, SelectionTile__svg_svg_5_Template, 1, 1, "svg", 5)(6, SelectionTile_ng_template_6_Template, 1, 1, "ng-template", null, 1, \u0275\u0275templateRefExtractor);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "div", 6);
      \u0275\u0275projection(9);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      const selectedIcon_r3 = \u0275\u0275reference(7);
      \u0275\u0275property("id", ctx.id)("disabled", ctx.disabled)("type", ctx.multiple ? "checkbox" : "radio")("value", ctx.value)("name", ctx.name);
      \u0275\u0275attribute("tabindex", ctx.disabled ? null : 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("for", ctx.id)("ngClass", \u0275\u0275pureFunction3(15, _c8, ctx.selected, ctx.theme === "light", ctx.disabled));
      \u0275\u0275attribute("aria-label", \u0275\u0275pipeBind1(3, 13, ctx.i18n.get("TILES.TILE")));
      \u0275\u0275advance(2);
      \u0275\u0275classProp("cds--tile__checkmark--persistent", ctx.multiple);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.selected)("ngIfElse", selectedIcon_r3);
    }
  },
  dependencies: [NgClass, NgIf, IconDirective, AsyncPipe],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SelectionTile, [{
    type: Component,
    args: [{
      selector: "cds-selection-tile, ibm-selection-tile",
      template: `
		<input
			#input
			[attr.tabindex]="disabled ? null : 0"
			class="cds--tile-input"
			[id]="id"
			[disabled]="disabled"
			[type]="(multiple ? 'checkbox': 'radio')"
			[value]="value"
			[name]="name"
			(change)="onChange($event)"/>
		<label
			class="cds--tile cds--tile--selectable"
			[for]="id"
			[ngClass]="{
				'cds--tile--is-selected' : selected,
				'cds--tile--light': theme === 'light',
				'cds--tile--disabled' : disabled
			}"
			[attr.aria-label]="i18n.get('TILES.TILE') | async">
			<div class="cds--tile__checkmark"
				[class.cds--tile__checkmark--persistent]="multiple">
				<svg *ngIf="!selected; else selectedIcon"
					[cdsIcon]="multiple ? 'checkbox' : 'checkmark'"
					size="16">
				</svg>
				<ng-template #selectedIcon>
					<svg [cdsIcon]="multiple ? 'checkbox--checked--filled' : 'checkmark--filled'" size="16"></svg>
				</ng-template>
			</div>
			<div class="cds--tile-content">
				<ng-content></ng-content>
			</div>
		</label>
	`
    }]
  }], function() {
    return [{
      type: I18n
    }];
  }, {
    theme: [{
      type: Input
    }],
    id: [{
      type: Input
    }],
    selected: [{
      type: Input
    }],
    value: [{
      type: Input
    }],
    change: [{
      type: Output
    }],
    disabled: [{
      type: Input
    }],
    input: [{
      type: ViewChild,
      args: ["input", {
        static: true
      }]
    }],
    keyboardInput: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }]
  });
})();
var TileGroup = class _TileGroup {
  constructor() {
    this.name = `tile-group-${_TileGroup.tileGroupCount}`;
    this.multiple = false;
    this.selected = new EventEmitter();
    this.tileGroupClass = true;
    this.unsubscribe$ = new Subject();
    this.unsubscribeTiles$ = new Subject();
    this.onChange = (_) => {
    };
    this.onTouched = () => {
    };
    _TileGroup.tileGroupCount++;
  }
  ngAfterContentInit() {
    const updateTiles = () => {
      this.unsubscribeTiles$.next();
      setTimeout(() => {
        this.selectionTiles.forEach((tile) => {
          tile.name = this.name;
          tile.change.pipe(takeUntil(this.unsubscribeTiles$)).subscribe(() => {
            this.selected.emit({
              value: tile.value,
              selected: tile.selected,
              name: this.name
            });
            this.onChange(tile.value);
          });
          tile.multiple = this.multiple;
        });
      });
    };
    updateTiles();
    this.selectionTiles.changes.pipe(takeUntil(this.unsubscribe$)).subscribe((_) => updateTiles());
  }
  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this.unsubscribeTiles$.next();
    this.unsubscribeTiles$.complete();
  }
  writeValue(value) {
    if (!this.selectionTiles) {
      return;
    }
    this.selectionTiles.forEach((tile) => {
      if (tile.value === value) {
        tile.selected = true;
      } else {
        tile.selected = false;
      }
    });
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TileGroup.tileGroupCount = 0;
TileGroup.\u0275fac = function TileGroup_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TileGroup)();
};
TileGroup.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: TileGroup,
  selectors: [["cds-tile-group"], ["ibm-tile-group"]],
  contentQueries: function TileGroup_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      \u0275\u0275contentQuery(dirIndex, SelectionTile, 4);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.selectionTiles = _t);
    }
  },
  hostVars: 2,
  hostBindings: function TileGroup_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--tile-group", ctx.tileGroupClass);
    }
  },
  inputs: {
    name: "name",
    multiple: "multiple",
    legend: "legend"
  },
  outputs: {
    selected: "selected"
  },
  standalone: false,
  features: [\u0275\u0275ProvidersFeature([{
    provide: NG_VALUE_ACCESSOR,
    useExisting: TileGroup,
    multi: true
  }])],
  ngContentSelectors: _c10,
  decls: 3,
  vars: 1,
  consts: [["legendLabel", ""], ["class", "cds--label", 4, "ngIf"], [1, "cds--label"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"]],
  template: function TileGroup_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c9);
      \u0275\u0275elementStart(0, "fieldset");
      \u0275\u0275template(1, TileGroup_legend_1_Template, 4, 2, "legend", 1);
      \u0275\u0275projection(2);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.legend);
    }
  },
  dependencies: [NgIf, NgTemplateOutlet],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TileGroup, [{
    type: Component,
    args: [{
      selector: "cds-tile-group, ibm-tile-group",
      template: `
		<fieldset>
			<legend *ngIf="legend" class="cds--label">
				<ng-template *ngIf="isTemplate(legend); else legendLabel;" [ngTemplateOutlet]="legend"></ng-template>
				<ng-template #legendLabel>{{legend}}</ng-template>
			</legend>
			<ng-content select="ibm-selection-tile,cds-selection-tile"></ng-content>
		</fieldset>`,
      providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: TileGroup,
        multi: true
      }]
    }]
  }], function() {
    return [];
  }, {
    name: [{
      type: Input
    }],
    multiple: [{
      type: Input
    }],
    legend: [{
      type: Input
    }],
    selected: [{
      type: Output
    }],
    tileGroupClass: [{
      type: HostBinding,
      args: ["class.cds--tile-group"]
    }],
    selectionTiles: [{
      type: ContentChildren,
      args: [SelectionTile]
    }]
  });
})();
var Tile = class {
  constructor() {
    this.tileClass = true;
    this.theme = "dark";
  }
  get lightThemeEnabled() {
    return this.theme === "light";
  }
};
Tile.\u0275fac = function Tile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Tile)();
};
Tile.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: Tile,
  selectors: [["cds-tile"], ["ibm-tile"]],
  hostVars: 4,
  hostBindings: function Tile_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--tile", ctx.tileClass)("cds--tile--light", ctx.lightThemeEnabled);
    }
  },
  inputs: {
    theme: "theme"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 1,
  vars: 0,
  template: function Tile_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275projection(0);
    }
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Tile, [{
    type: Component,
    args: [{
      selector: "cds-tile, ibm-tile",
      template: `<ng-content></ng-content>`
    }]
  }], null, {
    tileClass: [{
      type: HostBinding,
      args: ["class.cds--tile"]
    }],
    lightThemeEnabled: [{
      type: HostBinding,
      args: ["class.cds--tile--light"]
    }],
    theme: [{
      type: Input
    }]
  });
})();
var TilesModule = class {
};
TilesModule.\u0275fac = function TilesModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TilesModule)();
};
TilesModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: TilesModule,
  declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
  imports: [CommonModule, I18nModule, IconModule, LinkModule],
  exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup]
});
TilesModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule, I18nModule, IconModule, LinkModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TilesModule, [{
    type: NgModule,
    args: [{
      declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
      exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
      imports: [CommonModule, I18nModule, IconModule, LinkModule]
    }]
  }], null, null);
})();

// src/app/components/project-card.component.ts
var ProjectCardComponent = class _ProjectCardComponent {
  project;
  enter = new EventEmitter();
  onEnter() {
    this.enter.emit(this.project);
  }
  static \u0275fac = function ProjectCardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProjectCardComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProjectCardComponent, selectors: [["app-project-card"]], inputs: { project: "project" }, outputs: { enter: "enter" }, decls: 10, vars: 2, consts: [[1, "project-card"], [1, "project-card-content"], [1, "project-info"], [1, "project-title"], [1, "project-type"], [1, "project-actions"], ["cdsButton", "primary", "size", "sm", 3, "click"]], template: function ProjectCardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "cds-tile", 0)(1, "div", 1)(2, "div", 2)(3, "h3", 3);
      \u0275\u0275text(4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "span", 4);
      \u0275\u0275text(6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "div", 5)(8, "button", 6);
      \u0275\u0275listener("click", function ProjectCardComponent_Template_button_click_8_listener() {
        return ctx.onEnter();
      });
      \u0275\u0275text(9, " Enter ");
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.project.title);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.project.type);
    }
  }, dependencies: [
    CommonModule,
    ButtonModule,
    Button,
    TilesModule,
    Tile
  ], styles: ["\n\n.project-card[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.project-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n.project-card-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n}\n.project-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.project-title[_ngcontent-%COMP%] {\n  margin: 0 0 0.25rem 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #161616;\n}\n.project-type[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #6f6f6f;\n  text-transform: uppercase;\n  font-weight: 500;\n}\n.project-actions[_ngcontent-%COMP%] {\n  margin-left: 1rem;\n}\n/*# sourceMappingURL=project-card.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProjectCardComponent, [{
    type: Component,
    args: [{ selector: "app-project-card", standalone: true, imports: [
      CommonModule,
      ButtonModule,
      TilesModule
    ], template: `
    <cds-tile class="project-card">
      <div class="project-card-content">
        <div class="project-info">
          <h3 class="project-title">{{ project.title }}</h3>
          <span class="project-type">{{ project.type }}</span>
        </div>
        <div class="project-actions">
          <button
            cdsButton="primary"
            size="sm"
            (click)="onEnter()">
            Enter
          </button>
        </div>
      </div>
    </cds-tile>
  `, styles: ["/* angular:styles/component:scss;a5a69743b27b35acd069fa6101aa32417cfdd5a8f0b0dbfba5040103b79c549d;C:/Users/<USER>/ANACE/Dev/UI101_main_dashboard/ui101_main/src/app/components/project-card.component.ts */\n.project-card {\n  margin-bottom: 1rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.project-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n.project-card-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n}\n.project-info {\n  flex: 1;\n}\n.project-title {\n  margin: 0 0 0.25rem 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #161616;\n}\n.project-type {\n  font-size: 0.875rem;\n  color: #6f6f6f;\n  text-transform: uppercase;\n  font-weight: 500;\n}\n.project-actions {\n  margin-left: 1rem;\n}\n/*# sourceMappingURL=project-card.component.css.map */\n"] }]
  }], null, { project: [{
    type: Input,
    args: [{ required: true }]
  }], enter: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProjectCardComponent, { className: "ProjectCardComponent", filePath: "src/app/components/project-card.component.ts", lineNumber: 77 });
})();

// src/app/projects/projects.page.ts
var _forTrack0 = ($index, $item) => $item.id;
function ProjectsPage_Conditional_10_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 7)(1, "div", 9)(2, "h2");
    \u0275\u0275text(3, "No projects yet");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Create your first project to get started.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "button", 10);
    \u0275\u0275listener("click", function ProjectsPage_Conditional_10_Conditional_0_Template_button_click_6_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.newProject());
    });
    \u0275\u0275text(7, " Create First Project ");
    \u0275\u0275elementEnd()()();
  }
}
function ProjectsPage_Conditional_10_Conditional_1_For_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "app-project-card", 12);
    \u0275\u0275listener("enter", function ProjectsPage_Conditional_10_Conditional_1_For_2_Template_app_project_card_enter_0_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.enterProject($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const project_r4 = ctx.$implicit;
    \u0275\u0275property("project", project_r4);
  }
}
function ProjectsPage_Conditional_10_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275repeaterCreate(1, ProjectsPage_Conditional_10_Conditional_1_For_2_Template, 1, 1, "app-project-card", 11, _forTrack0);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const projects_r5 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275repeater(projects_r5);
  }
}
function ProjectsPage_Conditional_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275conditionalCreate(0, ProjectsPage_Conditional_10_Conditional_0_Template, 8, 0, "div", 7)(1, ProjectsPage_Conditional_10_Conditional_1_Template, 3, 0, "div", 8);
  }
  if (rf & 2) {
    \u0275\u0275conditional(ctx.length === 0 ? 0 : 1);
  }
}
function ProjectsPage_Conditional_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6);
    \u0275\u0275element(1, "cds-loading");
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading projects...");
    \u0275\u0275elementEnd()();
  }
}
var ProjectsPage = class _ProjectsPage {
  projectsService = inject(ProjectsService);
  auth = inject(AuthService);
  router = inject(Router);
  projects$ = this.projectsService.projects$;
  async newProject() {
    try {
      await this.projectsService.createProject("New Project", "DRYWALL");
    } catch (error) {
      console.error("Error creating project:", error);
    }
  }
  enterProject(project) {
    console.log("Entering project:", project);
  }
  async signOut() {
    try {
      console.log("Signing out...");
      await this.auth.signOut();
      console.log("Sign out successful, navigating to login...");
      setTimeout(async () => {
        await this.router.navigate(["/login"]);
      }, 100);
    } catch (error) {
      console.error("Sign out error:", error);
    }
  }
  static \u0275fac = function ProjectsPage_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProjectsPage)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProjectsPage, selectors: [["app-projects-page"]], decls: 13, vars: 3, consts: [[1, "projects-container"], [1, "projects-header"], [1, "header-actions"], ["cdsButton", "primary", "size", "md", 3, "click"], ["cdsButton", "secondary", "size", "md", 3, "click"], [1, "projects-content"], [1, "loading-state"], [1, "empty-state"], [1, "projects-list"], [1, "empty-state-content"], ["cdsButton", "primary", "size", "lg", 3, "click"], [3, "project"], [3, "enter", "project"]], template: function ProjectsPage_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "header", 1)(2, "h1");
      \u0275\u0275text(3, "Your Projects");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "div", 2)(5, "button", 3);
      \u0275\u0275listener("click", function ProjectsPage_Template_button_click_5_listener() {
        return ctx.newProject();
      });
      \u0275\u0275text(6, " + New Project ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "button", 4);
      \u0275\u0275listener("click", function ProjectsPage_Template_button_click_7_listener() {
        return ctx.signOut();
      });
      \u0275\u0275text(8, " Sign Out ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(9, "main", 5);
      \u0275\u0275conditionalCreate(10, ProjectsPage_Conditional_10_Template, 2, 1);
      \u0275\u0275pipe(11, "async");
      \u0275\u0275conditionalBranchCreate(12, ProjectsPage_Conditional_12_Template, 4, 0, "div", 6);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      let tmp_0_0;
      \u0275\u0275advance(10);
      \u0275\u0275conditional((tmp_0_0 = \u0275\u0275pipeBind1(11, 1, ctx.projects$)) ? 10 : 12, tmp_0_0);
    }
  }, dependencies: [
    CommonModule,
    ButtonModule,
    Button,
    LoadingModule,
    Loading,
    ProjectCardComponent,
    AsyncPipe
  ], styles: ["\n\n.projects-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n}\n.projects-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.projects-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 400;\n  color: #161616;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.projects-content[_ngcontent-%COMP%] {\n  padding: 1rem 0;\n}\n.loading-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: #6f6f6f;\n  font-size: 1.125rem;\n}\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  padding: 2rem;\n}\n.empty-state-content[_ngcontent-%COMP%] {\n  text-align: center;\n  max-width: 400px;\n}\n.empty-state-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n  color: #161616;\n}\n.empty-state-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0 0 2rem 0;\n  color: #6f6f6f;\n  font-size: 1rem;\n  line-height: 1.5;\n}\n.projects-list[_ngcontent-%COMP%] {\n  display: grid;\n  gap: 1rem;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n}\n@media (max-width: 768px) {\n  .projects-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .projects-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  .header-actions[_ngcontent-%COMP%] {\n    justify-content: center;\n  }\n  .projects-list[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=projects.page.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProjectsPage, [{
    type: Component,
    args: [{ selector: "app-projects-page", standalone: true, imports: [
      CommonModule,
      ButtonModule,
      LoadingModule,
      ProjectCardComponent
    ], template: `
    <div class="projects-container">
      <header class="projects-header">
        <h1>Your Projects</h1>
        <div class="header-actions">
          <button
            cdsButton="primary"
            size="md"
            (click)="newProject()">
            + New Project
          </button>
          <button
            cdsButton="secondary"
            size="md"
            (click)="signOut()">
            Sign Out
          </button>
        </div>
      </header>

      <main class="projects-content">
        <!-- Loading while first emission pending -->
        @if (projects$ | async; as projects) {
          <!-- Empty state -->
          @if (projects.length === 0) {
            <div class="empty-state">
              <div class="empty-state-content">
                <h2>No projects yet</h2>
                <p>Create your first project to get started.</p>
                <button
                  cdsButton="primary"
                  size="lg"
                  (click)="newProject()">
                  Create First Project
                </button>
              </div>
            </div>
          } @else {
            <!-- List -->
            <div class="projects-list">
              @for (project of projects; track project.id) {
                <app-project-card
                  [project]="project"
                  (enter)="enterProject($event)">
                </app-project-card>
              }
            </div>
          }
        } @else {
          <!-- Loading state -->
          <div class="loading-state">
            <cds-loading></cds-loading>
            <p>Loading projects...</p>
          </div>
        }
      </main>
    </div>
  `, styles: ["/* angular:styles/component:scss;246708008722e28a7273d58d0cc86a68fb0580de8ec2b84b5eb10cd3dbd5edd3;C:/Users/<USER>/ANACE/Dev/UI101_main_dashboard/ui101_main/src/app/projects/projects.page.ts */\n.projects-container {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n}\n.projects-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.projects-header h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 400;\n  color: #161616;\n}\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n.projects-content {\n  padding: 1rem 0;\n}\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n.loading-state p {\n  margin-top: 1rem;\n  color: #6f6f6f;\n  font-size: 1.125rem;\n}\n.empty-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  padding: 2rem;\n}\n.empty-state-content {\n  text-align: center;\n  max-width: 400px;\n}\n.empty-state-content h2 {\n  margin: 0 0 1rem 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n  color: #161616;\n}\n.empty-state-content p {\n  margin: 0 0 2rem 0;\n  color: #6f6f6f;\n  font-size: 1rem;\n  line-height: 1.5;\n}\n.projects-list {\n  display: grid;\n  gap: 1rem;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n}\n@media (max-width: 768px) {\n  .projects-container {\n    padding: 1rem;\n  }\n  .projects-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  .header-actions {\n    justify-content: center;\n  }\n  .projects-list {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=projects.page.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProjectsPage, { className: "ProjectsPage", filePath: "src/app/projects/projects.page.ts", lineNumber: 181 });
})();
export {
  ProjectsPage
};
//# sourceMappingURL=chunk-COMGYDWR.js.map
