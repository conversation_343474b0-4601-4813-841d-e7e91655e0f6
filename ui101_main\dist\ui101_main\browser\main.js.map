{"version": 3, "sources": ["src/app/app.routes.ts", "src/environments/environments.dev.ts", "src/app/app.config.ts", "src/app/components/loading.component.ts", "src/app/app.ts", "src/main.ts"], "sourcesContent": ["import { Routes, CanActivateFn, Router } from '@angular/router';\r\nimport { inject } from '@angular/core';\r\nimport { Auth, authState } from '@angular/fire/auth';\r\nimport { map, take } from 'rxjs/operators';\r\n\r\nexport const authGuard: CanActivateFn = () => {\r\n  const auth = inject(Auth), router = inject(Router);\r\n  return authState(auth).pipe(\r\n    take(1),\r\n    map(u => {\r\n      console.log('AuthGuard: User state:', u);\r\n      return u ? true : router.createUrlTree(['/login']);\r\n    })\r\n  );\r\n};\r\n\r\nexport const redirectLoggedInGuard: CanActivateFn = () => {\r\n  const auth = inject(Auth), router = inject(Router);\r\n  return authState(auth).pipe(\r\n    take(1),\r\n    map(u => {\r\n      console.log('RedirectLoggedInGuard: User state:', u);\r\n      return u ? router.createUrlTree(['/projects']) : true;\r\n    })\r\n  );\r\n};\r\n\r\nexport const routes: Routes = [\r\n  { path: 'login', canActivate: [redirectLoggedInGuard], loadComponent: () => import('./login/login.component').then(m => m.LoginComponent) },\r\n  { path: 'projects', canActivate: [authGuard], loadComponent: () => import('./projects/projects.page').then(m => m.ProjectsPage) },\r\n  { path: '**', redirectTo: 'projects' }\r\n];\r\n", "export const environment = {\r\n    production: false,\r\n    firebaseConfig: {\r\n        apiKey: \"AIzaSyDts9OKt9xyFy5SemqQ_ZF26hulhdvck7Q\",\r\n        authDomain: \"aa103-poc.firebaseapp.com\",\r\n        projectId: \"aa103-poc\",\r\n        storageBucket: \"aa103-poc.firebasestorage.app\",\r\n        messagingSenderId: \"50286062087\",\r\n        appId: \"1:50286062087:web:35e0b95924835cc7f518a2\",\r\n        measurementId: \"G-2SZKT467MZ\"\r\n    }\r\n};", "import { ApplicationConfig, provideBrowserGlobalErrorListeners, provideZonelessChangeDetection, PLATFORM_ID, inject } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideFirebaseApp, initializeApp } from '@angular/fire/app';\r\nimport { provideAuth, getAuth, browserLocalPersistence, setPersistence } from '@angular/fire/auth';\r\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\r\nimport { isPlatformBrowser } from '@angular/common';\r\n\r\nimport { routes } from './app.routes';\r\nimport { provideClientHydration, withEventReplay } from '@angular/platform-browser';\r\nimport { environment } from '../environments/environments.dev';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideBrowserGlobalErrorListeners(),\r\n    provideZonelessChangeDetection(),\r\n    provideRouter(routes),\r\n    provideClientHydration(withEventReplay()),\r\n    provideFirebaseApp(() => initializeApp(environment.firebaseConfig)),\r\n    provideAuth(() => {\r\n      const auth = getAuth();\r\n      const platformId = inject(PLATFORM_ID);\r\n      // Only set persistence in browser context\r\n      if (isPlatformBrowser(platformId)) {\r\n        setPersistence(auth, browserLocalPersistence);\r\n      }\r\n      return auth;\r\n    }),\r\n    provideFirestore(() => getFirestore()),\r\n  ]\r\n};\r\n", "import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n// Carbon Design System imports\nimport { LoadingModule } from 'carbon-components-angular/loading';\n\n@Component({\n  selector: 'app-loading',\n  standalone: true,\n  imports: [\n    CommonModule,\n    LoadingModule\n  ],\n  template: `\n    <div class=\"loading-container\">\n      <div class=\"loading-content\">\n        <ibm-loading size=\"normal\"></ibm-loading>\n        <p class=\"loading-text\">Loading...</p>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .loading-container {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(255, 255, 255, 0.9);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 9999;\n    }\n    \n    .loading-content {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 1rem;\n    }\n    \n    .loading-text {\n      margin: 0;\n      font-size: 1rem;\n      color: #525252;\n    }\n  `]\n})\nexport class LoadingComponent {}\n", "import { Component, signal, inject, OnInit } from '@angular/core';\r\nimport { RouterOutlet } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Auth, authState } from '@angular/fire/auth';\r\nimport { LoadingComponent } from './components/loading.component';\r\nimport { map, startWith } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  imports: [RouterOutlet, CommonModule, LoadingComponent],\r\n  template: `\r\n    @if (isAuthLoading$ | async) {\r\n      <app-loading></app-loading>\r\n    } @else {\r\n      <router-outlet></router-outlet>\r\n    }\r\n  `,\r\n  styleUrl: './app.scss'\r\n})\r\nexport class App implements OnInit {\r\n  protected readonly title = signal('ui101_main');\r\n  private auth = inject(Auth);\r\n\r\n  // Track if auth state is still loading (null at startup until Firebase restores session)\r\n  isAuthLoading$ = authState(this.auth).pipe(\r\n    startWith(undefined), // Start with undefined to show loading initially\r\n    map(user => user === undefined) // Show loading only when user is undefined (initial state)\r\n  );\r\n\r\n  ngOnInit() {\r\n    // Optional: Add any initialization logic here\r\n  }\r\n}\r\n", "import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { appConfig } from './app/app.config';\r\nimport { App } from './app/app';\r\n\r\nbootstrapApplication(App, appConfig)\r\n  .catch((err) => console.error(err));\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKO,IAAM,YAA2B,MAAK;AAC3C,QAAM,OAAO,OAAO,IAAI,GAAG,SAAS,OAAO,MAAM;AACjD,SAAO,UAAU,IAAI,EAAE,KACrB,KAAK,CAAC,GACN,IAAI,OAAI;AACN,YAAQ,IAAI,0BAA0B,CAAC;AACvC,WAAO,IAAI,OAAO,OAAO,cAAc,CAAC,QAAQ,CAAC;EACnD,CAAC,CAAC;AAEN;AAEO,IAAM,wBAAuC,MAAK;AACvD,QAAM,OAAO,OAAO,IAAI,GAAG,SAAS,OAAO,MAAM;AACjD,SAAO,UAAU,IAAI,EAAE,KACrB,KAAK,CAAC,GACN,IAAI,OAAI;AACN,YAAQ,IAAI,sCAAsC,CAAC;AACnD,WAAO,IAAI,OAAO,cAAc,CAAC,WAAW,CAAC,IAAI;EACnD,CAAC,CAAC;AAEN;AAEO,IAAM,SAAiB;EAC5B,EAAE,MAAM,SAAS,aAAa,CAAC,qBAAqB,GAAG,eAAe,MAAM,OAAO,qBAAyB,EAAE,KAAK,OAAK,EAAE,cAAc,EAAC;EACzI,EAAE,MAAM,YAAY,aAAa,CAAC,SAAS,GAAG,eAAe,MAAM,OAAO,qBAA0B,EAAE,KAAK,OAAK,EAAE,YAAY,EAAC;EAC/H,EAAE,MAAM,MAAM,YAAY,WAAU;;;;AC9B/B,IAAM,cAAc;EACvB,YAAY;EACZ,gBAAgB;IACZ,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;IACf,mBAAmB;IACnB,OAAO;IACP,eAAe;;;;;ACEhB,IAAM,YAA+B;EAC1C,WAAW;IACT,mCAAkC;IAClC,+BAA8B;IAC9B,cAAc,MAAM;IACpB,uBAAuB,gBAAe,CAAE;IACxC,mBAAmB,MAAM,cAAc,YAAY,cAAc,CAAC;IAClE,YAAY,MAAK;AACf,YAAM,OAAO,QAAO;AACpB,YAAM,aAAa,OAAO,WAAW;AAErC,UAAI,kBAAkB,UAAU,GAAG;AACjC,uBAAe,MAAM,uBAAuB;MAC9C;AACA,aAAO;IACT,CAAC;IACD,iBAAiB,MAAM,aAAY,CAAE;;;;;ACsBnC,IAAO,mBAAP,MAAO,kBAAgB;;qCAAhB,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,GAAA,cAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAnCzB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA;AAE3B,MAAA,oBAAA,GAAA,eAAA,CAAA;AACA,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAwB,MAAA,iBAAA,GAAA,YAAA;AAAU,MAAA,uBAAA,EAAI,EAClC;;;IARR;IACA;IAAa;EAAA,GAAA,QAAA,CAAA,+gBAAA,EAAA,CAAA;;;sEAsCJ,kBAAgB,CAAA;UA3C5B;uBACW,eAAa,YACX,MAAI,SACP;MACP;MACA;OACD,UACS;;;;;;;KAOT,QAAA,CAAA,2pBAAA,EAAA,CAAA;;;;6EA6BU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,2CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;;;ACrCvB,IAAA,oBAAA,GAAA,aAAA;;;;;AAEA,IAAA,oBAAA,GAAA,eAAA;;;AAKA,IAAO,MAAP,MAAO,KAAG;EACK,QAAQ,OAAO,cAAY,GAAA,YAAA,CAAA,EAAA,WAAA,QAAA,CAAA,IAAA,CAAA,CAAA;EACtC,OAAO,OAAO,IAAI;;EAG1B,iBAAiB,UAAU,KAAK,IAAI,EAAE;IACpC,UAAU,MAAS;;IACnB,IAAI,UAAQ,SAAS,MAAS;;;EAGhC,WAAQ;EAER;;qCAZW,MAAG;EAAA;yEAAH,MAAG,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,aAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AARZ,MAAA,8BAAA,GAAA,4BAAA,GAAA,GAAA,aAAA;;AAEE,MAAA,oCAAA,GAAA,4BAAA,GAAA,GAAA,eAAA;;;AAFF,MAAA,wBAAA,sBAAA,GAAA,GAAA,IAAA,cAAA,IAAA,IAAA,CAAA;;oBAFQ,cAAc,cAAc,kBAAgB,SAAA,GAAA,eAAA,EAAA,CAAA;;;sEAU3C,KAAG,CAAA;UAZf;uBACW,YAAU,SACX,CAAC,cAAc,cAAc,gBAAgB,GAAC,UAC7C;;;;;;IAMT,CAAA;;;;6EAGU,KAAG,EAAA,WAAA,OAAA,UAAA,kBAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;ACfhB,qBAAqB,KAAK,SAAS,EAChC,MAAM,CAAC,QAAQ,QAAQ,MAAM,GAAG,CAAC;", "names": []}