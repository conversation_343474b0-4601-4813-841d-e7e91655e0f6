import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ProjectsService, Project } from '../services/projects.service';
import { AuthService } from '../services/auth.service';
import { ProjectCardComponent } from '../components/project-card.component';

// Carbon Design System imports
import { ButtonModule } from 'carbon-components-angular/button';
import { LoadingModule } from 'carbon-components-angular/loading';

@Component({
  selector: 'app-projects-page',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    LoadingModule,
    ProjectCardComponent
  ],
  template: `
    <div class="projects-container">
      <header class="projects-header">
        <h1>Your Projects</h1>
        <div class="header-actions">
          <button
            cdsButton="primary"
            size="md"
            (click)="newProject()">
            + New Project
          </button>
          <button
            cdsButton="secondary"
            size="md"
            (click)="signOut()">
            Sign Out
          </button>
        </div>
      </header>

      <main class="projects-content">
        <!-- Loading while first emission pending -->
        @if (projects$ | async; as projects) {
          <!-- Empty state -->
          @if (projects.length === 0) {
            <div class="empty-state">
              <div class="empty-state-content">
                <h2>No projects yet</h2>
                <p>Create your first project to get started.</p>
                <button
                  cdsButton="primary"
                  size="lg"
                  (click)="newProject()">
                  Create First Project
                </button>
              </div>
            </div>
          } @else {
            <!-- List -->
            <div class="projects-list">
              @for (project of projects; track project.id) {
                <app-project-card
                  [project]="project"
                  (enter)="enterProject($event)">
                </app-project-card>
              }
            </div>
          }
        } @else {
          <!-- Loading state -->
          <div class="loading-state">
            <cds-loading></cds-loading>
            <p>Loading projects...</p>
          </div>
        }
      </main>
    </div>
  `,
  styles: [`
    .projects-container {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
      min-height: 100vh;
    }
    
    .projects-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .projects-header h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: 400;
      color: #161616;
    }
    
    .header-actions {
      display: flex;
      gap: 1rem;
    }
    
    .projects-content {
      padding: 1rem 0;
    }
    
    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem 2rem;
      text-align: center;
    }
    
    .loading-state p {
      margin-top: 1rem;
      color: #6f6f6f;
      font-size: 1.125rem;
    }
    
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: 2rem;
    }
    
    .empty-state-content {
      text-align: center;
      max-width: 400px;
    }
    
    .empty-state-content h2 {
      margin: 0 0 1rem 0;
      font-size: 1.5rem;
      font-weight: 400;
      color: #161616;
    }
    
    .empty-state-content p {
      margin: 0 0 2rem 0;
      color: #6f6f6f;
      font-size: 1rem;
      line-height: 1.5;
    }
    
    .projects-list {
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    }
    
    @media (max-width: 768px) {
      .projects-container {
        padding: 1rem;
      }
      
      .projects-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }
      
      .header-actions {
        justify-content: center;
      }
      
      .projects-list {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ProjectsPage {
  private projectsService = inject(ProjectsService);
  private auth = inject(AuthService);
  private router = inject(Router);

  projects$ = this.projectsService.projects$;

  async newProject() {
    try {
      await this.projectsService.createProject('New Project', 'DRYWALL');
    } catch (error) {
      console.error('Error creating project:', error);
    }
  }

  enterProject(project: Project) {
    console.log('Entering project:', project);
    // TODO: Navigate to project detail page
    // this.router.navigate(['/projects', project.id]);
  }

  async signOut() {
    try {
      console.log('Signing out...');
      await this.auth.signOut();
      console.log('Sign out successful, navigating to login...');

      // Small delay to ensure auth state is updated
      setTimeout(async () => {
        await this.router.navigate(['/login']);
      }, 100);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }
}
