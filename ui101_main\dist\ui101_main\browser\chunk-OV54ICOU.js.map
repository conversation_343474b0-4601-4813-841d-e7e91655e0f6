{"version": 3, "sources": ["src/app/projects/projects.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n// Carbon Design System imports\nimport { ButtonModule } from 'carbon-components-angular/button';\n\n@Component({\n  selector: 'app-projects',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ButtonModule\n  ],\n  template: `\n    <div class=\"projects-container\">\n      <header class=\"projects-header\">\n        <h1>Projects Dashboard</h1>\n        <button\n          cdsButton=\"primary\"\n          size=\"md\"\n          (click)=\"signOut()\">\n          Sign Out\n        </button>\n      </header>\n\n      <main class=\"projects-content\">\n        <p>Welcome to your projects dashboard!</p>\n        @if (auth.user$ | async; as user) {\n          <p>Logged in as: {{ user.email }}</p>\n        }\n      </main>\n    </div>\n  `,\n  styles: [`\n    .projects-container {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n    \n    .projects-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n      padding-bottom: 1rem;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    \n    .projects-header h1 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: 400;\n    }\n    \n    .projects-content {\n      padding: 1rem 0;\n    }\n  `]\n})\nexport class ProjectsComponent {\n  protected auth = inject(AuthService);\n  private router = inject(Router);\n\n  async signOut() {\n    try {\n      console.log('Signing out...');\n      await this.auth.signOut();\n      console.log('Sign out successful, navigating to login...');\n\n      // Small delay to ensure auth state is updated\n      setTimeout(async () => {\n        await this.router.navigate(['/login']);\n      }, 100);\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BU,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA8B,IAAA,uBAAA;;;AAA9B,IAAA,oBAAA;AAAA,IAAA,6BAAA,kBAAA,IAAA,KAAA;;;AAgCP,IAAO,oBAAP,MAAO,mBAAiB;EAClB,OAAO,OAAO,WAAW;EAC3B,SAAS,OAAO,MAAM;EAE9B,MAAM,UAAO;AACX,QAAI;AACF,cAAQ,IAAI,gBAAgB;AAC5B,YAAM,KAAK,KAAK,QAAO;AACvB,cAAQ,IAAI,6CAA6C;AAGzD,iBAAW,YAAW;AACpB,cAAM,KAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACvC,GAAG,GAAG;IACR,SAAS,OAAO;AACd,cAAQ,MAAM,mBAAmB,KAAK;IACxC;EACF;;qCAjBW,oBAAiB;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,aAAA,WAAA,QAAA,MAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA9C1B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,UAAA,CAAA,EACE,GAAA,IAAA;AAC1B,MAAA,iBAAA,GAAA,oBAAA;AAAkB,MAAA,uBAAA;AACtB,MAAA,yBAAA,GAAA,UAAA,CAAA;AAGE,MAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClB,MAAA,iBAAA,GAAA,YAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA+B,GAAA,GAAA;AAC1B,MAAA,iBAAA,GAAA,qCAAA;AAAmC,MAAA,uBAAA;AACtC,MAAA,8BAAA,GAAA,0CAAA,GAAA,GAAA,GAAA;;AAGF,MAAA,uBAAA,EAAO;;;;AAHL,MAAA,oBAAA,CAAA;AAAA,MAAA,yBAAA,UAAA,sBAAA,IAAA,GAAA,IAAA,KAAA,KAAA,KAAA,IAAA,IAAA,OAAA;;;IAjBJ;IACA;IAAY;IAAA;EAAA,GAAA,QAAA,CAAA,qiBAAA,EAAA,CAAA;;;sEAiDH,mBAAiB,CAAA;UAtD7B;uBACW,gBAAc,YACZ,MAAI,SACP;MACP;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;KAmBT,QAAA,CAAA,woBAAA,EAAA,CAAA;;;;6EA4BU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,0CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}