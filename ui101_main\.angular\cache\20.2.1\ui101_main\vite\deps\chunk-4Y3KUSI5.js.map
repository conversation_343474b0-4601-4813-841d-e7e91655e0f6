{"version": 3, "sources": ["../../../../../../node_modules/@carbon/utils-position/index.js", "../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-utils.mjs", "../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-i18n.mjs"], "sourcesContent": ["/**\n * Utilites to manipulate the position of elements relative to other elements\n */\nvar _a;\nexport var PLACEMENTS;\n(function (PLACEMENTS) {\n    PLACEMENTS[\"LEFT\"] = \"left\";\n    PLACEMENTS[\"RIGHT\"] = \"right\";\n    PLACEMENTS[\"TOP\"] = \"top\";\n    PLACEMENTS[\"BOTTOM\"] = \"bottom\";\n})(PLACEMENTS || (PLACEMENTS = {}));\nexport var defaultPositions = (_a = {},\n    _a[PLACEMENTS.LEFT] = function (referenceOffset, target, referenceRect) { return ({\n        top: referenceOffset.top - Math.round(target.offsetHeight / 2) + Math.round(referenceRect.height / 2),\n        left: Math.round(referenceOffset.left - target.offsetWidth)\n    }); },\n    _a[PLACEMENTS.RIGHT] = function (referenceOffset, target, referenceRect) { return ({\n        top: referenceOffset.top - Math.round(target.offsetHeight / 2) + Math.round(referenceRect.height / 2),\n        left: Math.round(referenceOffset.left + referenceRect.width)\n    }); },\n    _a[PLACEMENTS.TOP] = function (referenceOffset, target, referenceRect) { return ({\n        top: Math.round(referenceOffset.top - target.offsetHeight),\n        left: referenceOffset.left - Math.round(target.offsetWidth / 2) + Math.round(referenceRect.width / 2)\n    }); },\n    _a[PLACEMENTS.BOTTOM] = function (referenceOffset, target, referenceRect) { return ({\n        top: Math.round(referenceOffset.top + referenceRect.height),\n        left: referenceOffset.left - Math.round(target.offsetWidth / 2) + Math.round(referenceRect.width / 2)\n    }); },\n    _a);\nvar windowRef = typeof window !== \"undefined\" ? window : {\n    innerHeight: 0,\n    scrollY: 0,\n    innerWidth: 0,\n    scrollX: 0\n};\nvar Position = /** @class */ (function () {\n    function Position(positions) {\n        if (positions === void 0) { positions = {}; }\n        this.positions = defaultPositions;\n        this.positions = Object.assign({}, defaultPositions, positions);\n    }\n    Position.prototype.getRelativeOffset = function (target) {\n        // start with the initial element offsets\n        var offsets = {\n            left: target.offsetLeft,\n            top: target.offsetTop\n        };\n        // get each static (i.e. not absolute or relative) offsetParent and sum the left/right offsets\n        while (target.offsetParent && getComputedStyle(target.offsetParent).position === \"static\") {\n            offsets.left += target.offsetLeft;\n            offsets.top += target.offsetTop;\n            target = target.offsetParent;\n        }\n        return offsets;\n    };\n    Position.prototype.getAbsoluteOffset = function (target) {\n        var currentNode = target;\n        var margins = {\n            top: 0,\n            left: 0\n        };\n        // searches for containing elements with additional margins\n        while (currentNode.offsetParent) {\n            var computed = getComputedStyle(currentNode.offsetParent);\n            // find static elements with additional margins\n            // since they tend to throw off our positioning\n            // (usually this is just the body)\n            if (computed.position === \"static\" &&\n                computed.marginLeft &&\n                computed.marginTop) {\n                if (parseInt(computed.marginTop, 10)) {\n                    margins.top += parseInt(computed.marginTop, 10);\n                }\n                if (parseInt(computed.marginLeft, 10)) {\n                    margins.left += parseInt(computed.marginLeft, 10);\n                }\n            }\n            currentNode = currentNode.offsetParent;\n        }\n        var targetRect = target.getBoundingClientRect();\n        var relativeRect = document.body.getBoundingClientRect();\n        return {\n            top: targetRect.top - relativeRect.top + margins.top,\n            left: targetRect.left - relativeRect.left + margins.left\n        };\n    };\n    // finds the position relative to the `reference` element\n    Position.prototype.findRelative = function (reference, target, placement) {\n        var referenceOffset = this.getRelativeOffset(reference);\n        var referenceRect = reference.getBoundingClientRect();\n        return this.calculatePosition(referenceOffset, referenceRect, target, placement);\n    };\n    Position.prototype.findAbsolute = function (reference, target, placement) {\n        var referenceOffset = this.getAbsoluteOffset(reference);\n        var referenceRect = reference.getBoundingClientRect();\n        return this.calculatePosition(referenceOffset, referenceRect, target, placement);\n    };\n    Position.prototype.findPosition = function (reference, target, placement, offsetFunction) {\n        if (offsetFunction === void 0) { offsetFunction = this.getAbsoluteOffset.bind(this); }\n        var referenceOffset = offsetFunction(reference);\n        var referenceRect = reference.getBoundingClientRect();\n        return this.calculatePosition(referenceOffset, referenceRect, target, placement);\n    };\n    Position.prototype.findPositionAt = function (offset, target, placement) {\n        return this.calculatePosition(offset, { top: 0, left: 0, height: 0, width: 0 }, target, placement);\n    };\n    /**\n     * Get the dimensions of an element from an AbsolutePosition and a reference element\n     */\n    Position.prototype.getPlacementBox = function (target, position) {\n        var targetBottom = target.offsetHeight + position.top;\n        var targetRight = target.offsetWidth + position.left;\n        return {\n            top: position.top,\n            bottom: targetBottom,\n            left: position.left,\n            right: targetRight\n        };\n    };\n    Position.prototype.addOffset = function (position, top, left) {\n        if (top === void 0) { top = 0; }\n        if (left === void 0) { left = 0; }\n        return Object.assign({}, position, {\n            top: position.top + top,\n            left: position.left + left\n        });\n    };\n    Position.prototype.setElement = function (element, position) {\n        element.style.top = position.top + \"px\";\n        element.style.left = position.left + \"px\";\n    };\n    Position.prototype.findBestPlacement = function (reference, target, placements, containerFunction, positionFunction) {\n        var _this = this;\n        if (containerFunction === void 0) { containerFunction = this.defaultContainerFunction.bind(this); }\n        if (positionFunction === void 0) { positionFunction = this.findPosition.bind(this); }\n        /**\n         * map over the array of placements and weight them based on the percentage of visible area\n         * where visible area is defined as the area not obscured by the window borders\n         */\n        var weightedPlacements = placements.map(function (placement) {\n            var pos = positionFunction(reference, target, placement);\n            var box = _this.getPlacementBox(target, pos);\n            var hiddenHeight = 0;\n            var hiddenWidth = 0;\n            var container = containerFunction();\n            // the element is exceeding from top or bottom of its container\n            if (box.top < container.top) {\n                hiddenHeight = container.top - box.top;\n            }\n            else if (box.bottom > container.height) {\n                hiddenHeight = box.bottom - container.height;\n            }\n            // the element is exceeding from left or right of its container\n            if (box.left < container.left) {\n                hiddenWidth = container.left - box.left;\n            }\n            else if (box.right > container.width) {\n                hiddenWidth = box.right - container.width;\n            }\n            // if one of the hidden dimensions is 0 but the other is > 0\n            // we want to have a positive area, so setting the null one to 1\n            if (hiddenHeight && !hiddenWidth) {\n                hiddenWidth = 1;\n            }\n            else if (hiddenWidth && !hiddenHeight) {\n                hiddenHeight = 1;\n            }\n            var area = target.offsetHeight * target.offsetWidth;\n            var hiddenArea = hiddenHeight * hiddenWidth;\n            // if visibleArea is 0 it means the element is fully outside container bounds\n            // and visiblePercent will then be 0\n            var visibleArea = area - hiddenArea;\n            var visiblePercent = visibleArea / area;\n            return {\n                placement: placement,\n                weight: visiblePercent\n            };\n        });\n        // sort the placements from best to worst\n        weightedPlacements.sort(function (a, b) { return b.weight - a.weight; });\n        // pick the best!\n        return weightedPlacements[0].placement;\n    };\n    Position.prototype.findBestPlacementAt = function (offset, target, placements, containerFunction) {\n        var _this = this;\n        if (containerFunction === void 0) { containerFunction = this.defaultContainerFunction.bind(this); }\n        var positionAt = function (_, target, placement) {\n            return _this.findPositionAt(offset, target, placement);\n        };\n        return this.findBestPlacement(null, target, placements, containerFunction, positionAt);\n    };\n    Position.prototype.defaultContainerFunction = function () {\n        return {\n            // we go with window here, because that's going to be the simple/common case\n            top: 0,\n            left: 0,\n            height: windowRef.innerHeight,\n            width: windowRef.innerWidth\n        };\n    };\n    Position.prototype.calculatePosition = function (referenceOffset, referenceRect, target, placement) {\n        if (this.positions[placement]) {\n            return this.positions[placement](referenceOffset, target, referenceRect);\n        }\n        console.error(\"No function found for placement, defaulting to 0,0\");\n        return { left: 0, top: 0 };\n    };\n    return Position;\n}());\nexport { Position };\nexport var position = new Position();\nexport default Position;\n", "import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, NgZone, NgModule } from '@angular/core';\nimport { Subject, from, fromEvent, merge as merge$1, Subscription, Observable } from 'rxjs';\nexport { PLACEMENTS, Position, defaultPositions, position } from '@carbon/utils-position';\nimport { map } from 'rxjs/operators';\nfunction findSiblingElem(target, direction) {\n  if (target[direction]) {\n    if (target[direction].classList.contains(\"disabled\")) {\n      return findSiblingElem(target[direction], direction);\n    }\n    return target[direction];\n  }\n}\nfunction findNextElem(target) {\n  return findSiblingElem(target, \"nextElementSibling\");\n}\nfunction findPrevElem(target) {\n  return findSiblingElem(target, \"previousElementSibling\");\n}\n// check for Hight contrast mode\nfunction HcModeChecker() {\n  let colorTest = \"rgb(255, 0, 0)\";\n  let htmlChecker = document.createElement(\"div\");\n  htmlChecker.classList.add(\"hc-checker\");\n  document.body.appendChild(htmlChecker);\n  if (window.getComputedStyle(htmlChecker).backgroundColor.toString() !== colorTest) {\n    document.body.classList.add(\"a11y\");\n  }\n  document.body.removeChild(htmlChecker);\n}\nfunction focusNextTree(elem, rootElem = null) {\n  if (elem) {\n    let focusable = elem.querySelector(\"[tabindex='0']\");\n    if (focusable) {\n      focusable.focus();\n    } else {\n      focusNextElem(elem, rootElem);\n    }\n  }\n}\nfunction focusNextElem(elem, rootElem = null) {\n  if (elem) {\n    let nextElem = elem.nextElementSibling;\n    if (nextElem) {\n      let focusableElem = nextElem.querySelector(\"[tabindex='0']\");\n      if (focusableElem) {\n        focusableElem.focus();\n      } else {\n        focusNextElem(nextElem, rootElem);\n      }\n    } else {\n      if (rootElem) {\n        let nextRootElem = rootElem.nextElementSibling;\n        if (nextRootElem) {\n          focusNextTree(nextRootElem, rootElem);\n        }\n      }\n    }\n  }\n}\nfunction focusPrevElem(elem, parentRef = null) {\n  if (elem) {\n    let prevElem = elem.previousElementSibling;\n    if (prevElem) {\n      let focusableElem = prevElem.querySelector(\"[tabindex='0']\");\n      if (focusableElem) {\n        if (focusableElem.getAttribute(\"aria-expanded\") === \"true\") {\n          let lastFocElms = prevElem.querySelectorAll(\"[tabindex='0']\");\n          let arrLen = lastFocElms.length - 1;\n          for (let i = arrLen; i >= 0; i--) {\n            if (!!(lastFocElms[i].offsetWidth || lastFocElms[i].offsetHeight || lastFocElms[i].getClientRects().length)) {\n              focusableElem = lastFocElms[i];\n              break;\n            }\n          }\n        }\n        focusableElem.focus();\n      } else {\n        focusPrevElem(prevElem, parentRef);\n      }\n    } else {\n      if (parentRef) {\n        parentRef.querySelector(\"[tabindex='0']\").focus();\n      }\n    }\n  }\n}\nclass AnimationFrameServiceSingleton {\n  constructor(ngZone) {\n    this.ngZone = ngZone;\n    this.frameSource = new Subject();\n    this.tick = this.frameSource.asObservable();\n    this.ngZone.runOutsideAngular(() => {\n      this.animationFrameId = requestAnimationFrame(this.doTick.bind(this));\n    });\n  }\n  ngOnDestroy() {\n    cancelAnimationFrame(this.animationFrameId);\n  }\n  doTick(frame) {\n    this.frameSource.next(frame);\n    this.ngZone.runOutsideAngular(() => {\n      requestAnimationFrame(this.doTick.bind(this));\n    });\n  }\n}\nAnimationFrameServiceSingleton.ɵfac = function AnimationFrameServiceSingleton_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || AnimationFrameServiceSingleton)(i0.ɵɵinject(i0.NgZone));\n};\nAnimationFrameServiceSingleton.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AnimationFrameServiceSingleton,\n  factory: AnimationFrameServiceSingleton.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationFrameServiceSingleton, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass AnimationFrameService {\n  constructor(singleton) {\n    this.singleton = singleton;\n    this.tick = from(this.singleton.tick);\n  }\n}\nAnimationFrameService.ɵfac = function AnimationFrameService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || AnimationFrameService)(i0.ɵɵinject(AnimationFrameServiceSingleton));\n};\nAnimationFrameService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AnimationFrameService,\n  factory: AnimationFrameService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationFrameService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: AnimationFrameServiceSingleton\n    }];\n  }, null);\n})();\n\n// custom deep object merge\nconst merge = (target, ...objects) => {\n  for (const object of objects) {\n    for (const key in object) {\n      if (object.hasOwnProperty(key)) {\n        // since we're dealing just with JSON this simple check should be enough\n        if (object[key] instanceof Object) {\n          if (!target[key]) {\n            target[key] = {};\n          }\n          // recursively merge into the target\n          // most translations only run 3 or 4 levels deep, so no stack explosions\n          target[key] = merge(target[key], object[key]);\n        } else {\n          target[key] = object[key];\n        }\n      }\n    }\n  }\n  return target;\n};\n\n/**\n * Checks if a given element is scrollable.\n * If the element has an overflow set as part of its computed style it can scroll.\n * @param element the element to check scrollability\n */\nconst isScrollableElement = element => {\n  const computedStyle = getComputedStyle(element);\n  return computedStyle.overflow === \"auto\" || computedStyle.overflow === \"scroll\" || computedStyle[\"overflow-y\"] === \"auto\" || computedStyle[\"overflow-y\"] === \"scroll\" || computedStyle[\"overflow-x\"] === \"auto\" || computedStyle[\"overflow-x\"] === \"scroll\";\n};\n/**\n * Checks if an element is visible within a container\n * @param element the element to check\n * @param container the container to check\n */\nconst isVisibleInContainer = (element, container) => {\n  const elementRect = element.getBoundingClientRect();\n  const containerRect = container.getBoundingClientRect();\n  // If there exists `height: 100%` on the `html` or `body` tag of an application,\n  // it causes the calculation to return true if you need to scroll before the element is seen.\n  // In that case we calculate its visibility based on the window viewport.\n  if (container.tagName === \"BODY\" || container.tagName === \"HTML\") {\n    // This checks if element is within the top, bottom, left and right of viewport, ie. if the element is visible in\n    // the screen. This also takes into account partial visibility of an element.\n    const isAboveViewport = elementRect.top < 0 && elementRect.top + element.clientHeight < 0;\n    const isLeftOfViewport = elementRect.left < 0;\n    const isBelowViewport = elementRect.bottom - element.clientHeight > (window.innerHeight || document.documentElement.clientHeight);\n    const isRightOfViewport = elementRect.right > (window.innerWidth || document.documentElement.clientWidth);\n    const isVisibleInViewport = !(isAboveViewport || isBelowViewport || isLeftOfViewport || isRightOfViewport);\n    return isVisibleInViewport;\n  }\n  return (\n    // This also accounts for partial visibility. It will still return true if the element is partially visible inside the container.\n    elementRect.bottom - element.clientHeight <= containerRect.bottom + (container.offsetHeight - container.clientHeight) / 2 && elementRect.top >= -element.clientHeight\n  );\n};\nconst getScrollableParents = node => {\n  const elements = [document.body];\n  while (node.parentElement && node !== document.body) {\n    if (isScrollableElement(node)) {\n      elements.push(node);\n    }\n    node = node.parentElement;\n  }\n  return elements;\n};\nconst hasScrollableParents = node => {\n  while (node.parentElement && node !== document.body) {\n    if (isScrollableElement(node)) {\n      return true;\n    }\n    node = node.parentElement;\n  }\n  return false;\n};\n/**\n * Returns an observable that emits whenever any scrollable parent element scrolls\n *\n * @param node root element to start finding scrolling parents from\n */\nconst scrollableParentsObservable = node => {\n  const windowScroll = fromEvent(window, \"scroll\", {\n    passive: true\n  }).pipe(map(event =>\n  // update the event target to be something useful. In this case `body` is a sensible replacement\n  Object.assign({}, event, {\n    target: document.body\n  })));\n  let observables = [windowScroll];\n  // walk the parents and subscribe to all the scroll events we can\n  while (node.parentElement && node !== document.body) {\n    if (isScrollableElement(node)) {\n      observables.push(fromEvent(node, \"scroll\", {\n        passive: true\n      }));\n    }\n    node = node.parentElement;\n  }\n  return merge$1(...observables);\n};\nfunction clone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}\nfunction matchesAttr(el, attr, val) {\n  const styles = window.getComputedStyle(el);\n  return val.includes(styles[attr]);\n}\nfunction closestAttr(s, t, element) {\n  let el = element;\n  if (!element) {\n    return null;\n  }\n  do {\n    if (matchesAttr(el, s, t)) {\n      return el;\n    }\n    el = el.parentElement || el.parentNode;\n  } while (el !== null && el.nodeType === 1);\n  return null;\n}\nclass ElementService {\n  constructor(singleton) {\n    this.singleton = singleton;\n    this.tick = from(this.singleton.tick);\n  }\n  visibility(target, parentElement = target) {\n    const scrollableParents = getScrollableParents(parentElement);\n    return this.tick.pipe(map(() => {\n      for (const parent of scrollableParents) {\n        if (!isVisibleInContainer(target, parent)) {\n          return {\n            visible: false\n          };\n        }\n      }\n      return {\n        visible: true\n      };\n    }));\n  }\n}\nElementService.ɵfac = function ElementService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ElementService)(i0.ɵɵinject(AnimationFrameServiceSingleton));\n};\nElementService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ElementService,\n  factory: ElementService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ElementService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: AnimationFrameServiceSingleton\n    }];\n  }, null);\n})();\nconst getEventObservable = (targetElement, eventType) => {\n  switch (eventType) {\n    case \"scroll\":\n    case \"resize\":\n    case \"touchstart\":\n    case \"touchmove\":\n    case \"touchend\":\n      return fromEvent(targetElement, eventType, {\n        passive: true\n      });\n    default:\n      return fromEvent(targetElement, eventType);\n  }\n};\nclass DocumentService {\n  constructor() {\n    this.globalEvents = new Map();\n    this.documentRef = document;\n    this.subscriptions = new Subscription();\n  }\n  handleEvent(eventType, callback) {\n    if (!this.globalEvents.has(eventType)) {\n      if (this.documentRef) {\n        this.globalEvents.set(eventType, getEventObservable(this.documentRef, eventType));\n      } else {\n        this.globalEvents.set(eventType, new Observable());\n      }\n    }\n    const observable = this.globalEvents.get(eventType);\n    this.subscriptions.add(observable.subscribe(callback));\n  }\n  handleClick(callback) {\n    this.handleEvent(\"click\", callback);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    this.globalEvents = null;\n  }\n}\nDocumentService.ɵfac = function DocumentService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DocumentService)();\n};\nDocumentService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DocumentService,\n  factory: DocumentService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DocumentService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass EventService {\n  constructor(documentService) {\n    this.documentService = documentService;\n    this.subscriptions = new Subscription();\n    this.targets = new WeakMap();\n  }\n  on(targetElement, eventType, callback) {\n    if (!this.targets.has(targetElement)) {\n      this.targets.set(targetElement, new Map());\n    }\n    const eventMap = this.targets.get(targetElement);\n    if (!eventMap.has(eventType)) {\n      eventMap.set(eventType, getEventObservable(targetElement, eventType));\n    }\n    const subscription = eventMap.get(eventType).subscribe(callback);\n    this.subscriptions.add(subscription);\n  }\n  onDocument(eventType, callback) {\n    this.documentService.handleEvent(eventType, callback);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n  }\n}\nEventService.ɵfac = function EventService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || EventService)(i0.ɵɵinject(DocumentService));\n};\nEventService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventService,\n  factory: EventService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: DocumentService\n    }];\n  }, null);\n})();\n\n// either provides a new instance of DocumentService, or returns the parent\nfunction DOCUMENT_SERVICE_PROVIDER_FACTORY(parentService) {\n  return parentService || new DocumentService();\n}\n// DocumentService *must* be a singleton to ensure that we handle events and other document level settings once (and only once)\nconst DOCUMENT_SERVICE_PROVIDER = {\n  provide: DocumentService,\n  deps: [[new Optional(), new SkipSelf(), DocumentService]],\n  useFactory: DOCUMENT_SERVICE_PROVIDER_FACTORY\n};\n// either provides a new instance of AnimationFrameServiceSingleton, or returns the parent\nfunction ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER_FACTORY(parentService, ngZone) {\n  return parentService || new AnimationFrameServiceSingleton(ngZone);\n}\n// AnimationFrameServiceSingleton is a singleton so we don't have a ton of duplicate RAFs firing (better for scheduling)\nconst ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER = {\n  provide: AnimationFrameServiceSingleton,\n  deps: [[new Optional(), new SkipSelf(), AnimationFrameServiceSingleton], NgZone],\n  useFactory: ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER_FACTORY\n};\nclass UtilsModule {}\nUtilsModule.ɵfac = function UtilsModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || UtilsModule)();\n};\nUtilsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: UtilsModule\n});\nUtilsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DOCUMENT_SERVICE_PROVIDER, ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER, AnimationFrameServiceSingleton, DocumentService, AnimationFrameService, ElementService, EventService]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UtilsModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DOCUMENT_SERVICE_PROVIDER, ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER, AnimationFrameServiceSingleton, DocumentService, AnimationFrameService, ElementService, EventService]\n    }]\n  }], null, null);\n})();\nlet _scrollbarWidth = -1;\nfunction getScrollbarWidth() {\n  // lets not recreate this whole thing every time\n  if (_scrollbarWidth >= 0) {\n    return _scrollbarWidth;\n  }\n  // do the calculations the first time\n  const outer = document.createElement(\"div\");\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"100px\";\n  outer.style[\"msOverflowStyle\"] = \"scrollbar\"; // needed for WinJS apps\n  document.body.appendChild(outer);\n  const widthNoScroll = outer.offsetWidth;\n  // force scrollbars\n  outer.style.overflow = \"scroll\";\n  // add innerdiv\n  const inner = document.createElement(\"div\");\n  inner.style.width = \"100%\";\n  outer.appendChild(inner);\n  const widthWithScroll = inner.offsetWidth;\n  // remove divs\n  outer.parentNode.removeChild(outer);\n  _scrollbarWidth = widthNoScroll - widthWithScroll;\n  return _scrollbarWidth;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER, ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER_FACTORY, AnimationFrameService, AnimationFrameServiceSingleton, DOCUMENT_SERVICE_PROVIDER, DOCUMENT_SERVICE_PROVIDER_FACTORY, DocumentService, ElementService, EventService, HcModeChecker, UtilsModule, clone, closestAttr, findNextElem, findPrevElem, focusNextElem, focusNextTree, focusPrevElem, getEventObservable, getScrollableParents, getScrollbarWidth, hasScrollableParents, isScrollableElement, isVisibleInContainer, merge, scrollableParentsObservable };\n", "import * as i0 from '@angular/core';\nimport { Injectable, Pi<PERSON>, Optional, SkipSelf, NgModule } from '@angular/core';\nimport { BehaviorSubject, iif, isObservable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { merge } from 'carbon-components-angular/utils';\nvar EN = {\n  \"BREADCRUMB\": {\n    \"LABEL\": \"Breadcrumb\",\n    \"OVERFLOW_MENU_DESCRIPTION\": \"Options\"\n  },\n  \"CODE_SNIPPET\": {\n    \"CODE_SNIPPET_TEXT\": \"Code Snippet Text\",\n    \"SHOW_MORE\": \"Show more\",\n    \"SHOW_LESS\": \"Show less\",\n    \"SHOW_MORE_ICON\": \"Show more icon\",\n    \"COPY_CODE\": \"Copy code\",\n    \"COPIED\": \"Copied!\"\n  },\n  \"COMBOBOX\": {\n    \"PLACEHOLDER\": \"Filter...\",\n    \"CLEAR_SELECTIONS\": \"Clear all selected items\",\n    \"CLEAR_SELECTED\": \"Clear selected item\",\n    \"A11Y\": {\n      \"OPEN_MENU\": \"Open menu\",\n      \"CLOSE_MENU\": \"Close menu\",\n      \"CLEAR_SELECTIONS\": \"Clear all selected items\",\n      \"CLEAR_SELECTED\": \"Clear Selection\"\n    }\n  },\n  \"DROPDOWN\": {\n    \"OPEN\": \"Open menu\",\n    \"SELECTED\": \"Selected\",\n    \"CLEAR\": \"Clear all selected items\",\n    \"FILTER\": {\n      \"SELECTED_ONLY\": \"Show selected only\",\n      \"SEARCH\": \"Search\",\n      \"NO_RESULTS\": \"No search results\",\n      \"RESET_SEARCH\": \"Reset search\"\n    }\n  },\n  \"DROPDOWN_LIST\": {\n    \"LABEL\": \"Listbox\"\n  },\n  \"FILE_UPLOADER\": {\n    \"CHECKMARK\": \"Checkmark\",\n    \"OPEN\": \"Add file\",\n    \"REMOVE_BUTTON\": \"Close button\"\n  },\n  \"LOADING\": {\n    \"TITLE\": \"Loading\"\n  },\n  \"MODAL\": {\n    \"CLOSE\": \"Close\"\n  },\n  \"NOTIFICATION\": {\n    \"CLOSE_BUTTON\": \"Close alert notification\"\n  },\n  \"NUMBER\": {\n    \"INCREMENT\": \"Increment value\",\n    \"DECREMENT\": \"Decrement value\"\n  },\n  \"OVERFLOW_MENU\": {\n    \"OVERFLOW\": \"Overflow\",\n    \"ICON_DESCRIPTION\": \"Options\"\n  },\n  \"SEARCH\": {\n    \"LABEL\": \"Search\",\n    \"PLACEHOLDER\": \"Search\",\n    \"CLEAR_BUTTON\": \"Clear search input\"\n  },\n  \"PAGINATION\": {\n    \"ITEMS_PER_PAGE\": \"Items per page:\",\n    \"OPEN_LIST_OF_OPTIONS\": \"Open list of options\",\n    \"BACKWARD\": \"Backward\",\n    \"FORWARD\": \"Forward\",\n    \"TOTAL_ITEMS_UNKNOWN\": \"{{start}}-{{end}} items\",\n    \"TOTAL_ITEMS\": \"{{start}}-{{end}} of {{total}} items\",\n    \"TOTAL_ITEM\": \"{{start}}-{{end}} of {{total}} item\",\n    \"PAGE\": \"page\",\n    \"OF_LAST_PAGES\": \"of {{last}} pages\",\n    \"OF_LAST_PAGE\": \"of {{last}} page\",\n    \"NEXT\": \"Next\",\n    \"PREVIOUS\": \"Previous\",\n    \"SELECT_ARIA\": \"Select page number\"\n  },\n  \"PROGRESS_INDICATOR\": {\n    \"CURRENT\": \"Current\",\n    \"INCOMPLETE\": \"Incomplete\",\n    \"COMPLETE\": \"Complete\",\n    \"INVALID\": \"Invalid\"\n  },\n  \"TABLE\": {\n    \"FILTER\": \"Filter\",\n    \"END_OF_DATA\": \"You've reached the end of your content\",\n    \"SCROLL_TOP\": \"Scroll to top\",\n    \"CHECKBOX_HEADER\": \"Select all rows\",\n    \"CHECKBOX_ROW\": \"Select {{value}}\",\n    \"EXPAND_BUTTON\": \"Expand row\",\n    \"EXPAND_ALL_BUTTON\": \"Expand all rows\",\n    \"SORT_DESCENDING\": \"Sort rows by this header in descending order\",\n    \"SORT_ASCENDING\": \"Sort rows by this header in ascending order\",\n    \"ROW\": \"row\"\n  },\n  \"TABLE_TOOLBAR\": {\n    \"ACTION_BAR\": \"Table action bar\",\n    \"BATCH_TEXT\": \"\",\n    \"BATCH_TEXT_SINGLE\": \"1 item selected\",\n    \"BATCH_TEXT_MULTIPLE\": \"{{count}} items selected\",\n    \"CANCEL\": \"Cancel\"\n  },\n  \"TABS\": {\n    \"BUTTON_ARIA_LEFT\": \"Go to the previous tab\",\n    \"BUTTON_ARIA_RIGHT\": \"Go to the next tab\",\n    \"HEADER_ARIA_LABEL\": \"List of tabs\"\n  },\n  \"TILES\": {\n    \"TILE\": \"tile\",\n    \"EXPAND\": \"Expand\",\n    \"COLLAPSE\": \"Collapse\"\n  },\n  \"TOGGLE\": {\n    \"OFF\": \"Off\",\n    \"ON\": \"On\"\n  },\n  \"UI_SHELL\": {\n    \"SKIP_TO\": \"Skip to content\",\n    \"HEADER\": {\n      \"OPEN_MENU\": \"Open menu\",\n      \"CLOSE_MENU\": \"Close menu\"\n    },\n    \"SIDE_NAV\": {\n      \"TOGGLE_OPEN\": \"Open\",\n      \"TOGGLE_CLOSE\": \"Close\"\n    }\n  }\n};\n\n/**\n * Takes the `Observable` returned from `i18n.get` and an object of variables to replace.\n *\n * The keys specify the variable name in the string.\n *\n * Example:\n * ```typescript\n * service.set({ \"TEST\": \"{{foo}} {{bar}}\" });\n *\n * service.replace(service.get(\"TEST\"), { foo: \"test\", bar: \"asdf\" })\n * ```\n *\n * Produces: `\"test asdf\"`\n *\n * @param subject the translation to replace variables on\n * @param variables object of variables to replace\n */\nconst replace = (subject, variables) => subject.pipe(map(str => {\n  const keys = Object.keys(variables);\n  for (const key of keys) {\n    const value = variables[key];\n    str = str.replace(new RegExp(`{{\\\\s*${key}\\\\s*}}`, \"g\"), value);\n  }\n  return str;\n}));\n/**\n * Represents an \"overridable\" translation value.\n *\n * Largely an internal usecase. There are situations where we want an `Observable` that\n * can emit events from a centralized source **OR** an `Observable` that will emit events\n * from a component local source. The key example being on/off text in a `Toggle` - In some cases\n * we want the `Toggle` to use `I18n`s global translations, but in others we'd prefer to use a local\n * override. We don't ever need to return to a non-overridden state, but we do need the ability to\n * switch _to_ an overridden sate.\n */\nclass Overridable {\n  constructor(path, i18n) {\n    this.path = path;\n    this.i18n = i18n;\n    /**\n     * Our base non-overridden translation.\n     */\n    this.baseTranslation = this.i18n.get(this.path);\n    /**\n     * A boolean to flip between overridden and non-overridden states.\n     */\n    this.isOverridden = false;\n    /**\n     * ensure `$override` is initialized with the correct default value\n     * in some cases `_value` can get changed for an `Observable` before `$override` is created\n     */\n    const value = this.i18n.getValueFromPath(this.path);\n    this.$override = new BehaviorSubject(value);\n    this._value = value;\n  }\n  /**\n   * The raw value of the translation. Defaults to the string value, but will return the value passed to `override`\n   *\n   * @readonly\n   */\n  get value() {\n    return this._value;\n  }\n  set value(v) {\n    this.override(v);\n  }\n  /**\n   * The translation subject. Returns either a stream of overridden values, or our base translation values.\n   *\n   * @readonly\n   */\n  get subject() {\n    /**\n     * since inputs are bound on template instantiation (and thusly will always have _some_ value)\n     * We can use a simple boolean and the `iif` function to determine which subject to return on subscription\n     */\n    return iif(() => this.isOverridden, this.$override, this.baseTranslation);\n  }\n  /**\n   * Takes a string or an `Observable` that emits strings.\n   * Overrides the value provided by the `I18n` service.\n   */\n  override(value) {\n    this.isOverridden = true;\n    // To ensure that there are not multiple subscriptions created for the same observable, we\n    // unsubscribe if a subscription already exists for an observable before creating a new one.\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      this.subscription = null;\n    }\n    this._value = value;\n    if (isObservable(value)) {\n      this.subscription = value.subscribe(v => {\n        this.$override.next(v);\n      });\n    } else {\n      this.$override.next(value);\n    }\n  }\n}\n/**\n * The I18n service is a minimal internal singleton service used to supply our components with translated strings.\n *\n * All the components that support I18n also support directly passed strings.\n * Usage of I18n is optional, and it is not recommended for application use (libraries like ngx-translate\n * are a better choice)\n *\n */\nclass I18n {\n  constructor() {\n    this.translationStrings = EN;\n    this.translations = new Map();\n    this.locale = new BehaviorSubject(\"en\");\n  }\n  /**\n   * Sets the locale and optionally the translation strings. Locale is used by components that\n   * are already locale aware (datepicker for example) while the translation strings are used\n   * for components that are not.\n   *\n   * Locales set here will override locales/languages set in components\n   * @param language an ISO 639-1 language code - https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes\n   * @param strings an object of strings, optional\n   */\n  setLocale(language, strings) {\n    this.locale.next(language);\n    if (strings) {\n      this.set(strings);\n    }\n  }\n  /**\n   * Returns the current locale\n   */\n  getLocale() {\n    return this.locale.value;\n  }\n  /**\n   * Returns an observable that resolves to the current locale, and will update when changed\n   */\n  getLocaleObservable() {\n    return this.locale.asObservable();\n  }\n  /**\n   * Set/update the translations from an object. Also notifies all participating components of the update.\n   *\n   * @param strings an object of strings, should follow the same format as src/i18n/en.json\n   */\n  set(strings) {\n    this.translationStrings = merge({}, EN, strings);\n    // iterate over all our tracked translations and update each observable\n    const translations = Array.from(this.translations);\n    for (const [path, subject] of translations) {\n      subject.next(this.getValueFromPath(path));\n    }\n  }\n  /**\n   * When a path is specified returns an observable that will resolve to the translation string value.\n   *\n   * Returns the full translations object if path is not specified.\n   *\n   * @param path optional, looks like `\"NOTIFICATION.CLOSE_BUTTON\"`\n   */\n  get(path) {\n    if (!path) {\n      return this.translationStrings;\n    }\n    return this.getSubject(path);\n  }\n  /**\n   * Returns all descendents of some path fragment as an object.\n   *\n   * @param partialPath a path fragment, for example `\"NOTIFICATION\"`\n   */\n  getMultiple(partialPath) {\n    const values = this.getValueFromPath(partialPath);\n    const subjects = {};\n    for (const key of Object.keys(values)) {\n      if (values[key] === Object(values[key])) {\n        subjects[key] = this.getMultiple(`${partialPath}.${key}`);\n      } else {\n        subjects[key] = this.getSubject(`${partialPath}.${key}`);\n      }\n    }\n    return subjects;\n  }\n  /**\n   * Returns an instance of `Overridable` that can be used to optionally override the value provided by `I18n`\n   * @param path looks like `\"NOTIFICATION.CLOSE_BUTTON\"`\n   */\n  getOverridable(path) {\n    return new Overridable(path, this);\n  }\n  /**\n   * Takes the `Observable` returned from `i18n.get` and an object of variables to replace.\n   *\n   * The keys specify the variable name in the string.\n   *\n   * Example:\n   * ```\n   * service.set({ \"TEST\": \"{{foo}} {{bar}}\" });\n   *\n   * service.replace(service.get(\"TEST\"), { foo: \"test\", bar: \"asdf\" })\n   * ```\n   *\n   * Produces: `\"test asdf\"`\n   *\n   * @param subject the translation to replace variables on\n   * @param variables object of variables to replace\n   */\n  replace(subject, variables) {\n    return replace(subject, variables);\n  }\n  /**\n   * Trys to resolve a value from the provided path.\n   *\n   * @param path looks like `\"NOTIFICATION.CLOSE_BUTTON\"`\n   */\n  getValueFromPath(path) {\n    let value = this.translationStrings;\n    for (const segment of path.split(\".\")) {\n      if (value[segment] !== undefined && value[segment] !== null) {\n        value = value[segment];\n      } else {\n        throw new Error(`no key ${segment} at ${path}`);\n      }\n    }\n    return value;\n  }\n  /**\n   * Helper method that returns an observable from the internal cache based on the path\n   *\n   * @param path looks like `\"NOTIFICATION.CLOSE_BUTTON\"`\n   */\n  getSubject(path) {\n    try {\n      // we run this here to validate the path exists before adding it to the translation map\n      const value = this.getValueFromPath(path);\n      if (this.translations.has(path)) {\n        return this.translations.get(path);\n      }\n      const translation = new BehaviorSubject(value);\n      this.translations.set(path, translation);\n      return translation;\n    } catch (error) {\n      console.error(error);\n    }\n  }\n}\nI18n.ɵfac = function I18n_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || I18n)();\n};\nI18n.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: I18n,\n  factory: I18n.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(I18n, [{\n    type: Injectable\n  }], null, null);\n})();\nclass ReplacePipe {\n  transform(value, variables) {\n    return replace(value, variables);\n  }\n}\nReplacePipe.ɵfac = function ReplacePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ReplacePipe)();\n};\nReplacePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"i18nReplace\",\n  type: ReplacePipe,\n  pure: true,\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReplacePipe, [{\n    type: Pipe,\n    args: [{\n      name: \"i18nReplace\"\n    }]\n  }], null, null);\n})();\n\n// either provides a new instance of I18n, or returns the parent\nfunction I18N_SERVICE_PROVIDER_FACTORY(parentService) {\n  return parentService || new I18n();\n}\n// I18n should provide a single instance of itself to ensure that translations are consistent through the app\nconst I18N_SERVICE_PROVIDER = {\n  provide: I18n,\n  deps: [[new Optional(), new SkipSelf(), I18n]],\n  useFactory: I18N_SERVICE_PROVIDER_FACTORY\n};\nclass I18nModule {}\nI18nModule.ɵfac = function I18nModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || I18nModule)();\n};\nI18nModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: I18nModule,\n  declarations: [ReplacePipe],\n  exports: [ReplacePipe]\n});\nI18nModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [I18n, I18N_SERVICE_PROVIDER]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(I18nModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ReplacePipe],\n      exports: [ReplacePipe],\n      providers: [I18n, I18N_SERVICE_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { I18N_SERVICE_PROVIDER, I18N_SERVICE_PROVIDER_FACTORY, I18n, I18nModule, Overridable, ReplacePipe, replace };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAI;AACG,IAAI;AAAA,CACV,SAAUA,aAAY;AACnB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,KAAK,IAAI;AACpB,EAAAA,YAAW,QAAQ,IAAI;AAC3B,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI,oBAAoB,KAAK,CAAC,GACjC,GAAG,WAAW,IAAI,IAAI,SAAU,iBAAiB,QAAQ,eAAe;AAAE,SAAQ;AAAA,IAC9E,KAAK,gBAAgB,MAAM,KAAK,MAAM,OAAO,eAAe,CAAC,IAAI,KAAK,MAAM,cAAc,SAAS,CAAC;AAAA,IACpG,MAAM,KAAK,MAAM,gBAAgB,OAAO,OAAO,WAAW;AAAA,EAC9D;AAAI,GACJ,GAAG,WAAW,KAAK,IAAI,SAAU,iBAAiB,QAAQ,eAAe;AAAE,SAAQ;AAAA,IAC/E,KAAK,gBAAgB,MAAM,KAAK,MAAM,OAAO,eAAe,CAAC,IAAI,KAAK,MAAM,cAAc,SAAS,CAAC;AAAA,IACpG,MAAM,KAAK,MAAM,gBAAgB,OAAO,cAAc,KAAK;AAAA,EAC/D;AAAI,GACJ,GAAG,WAAW,GAAG,IAAI,SAAU,iBAAiB,QAAQ,eAAe;AAAE,SAAQ;AAAA,IAC7E,KAAK,KAAK,MAAM,gBAAgB,MAAM,OAAO,YAAY;AAAA,IACzD,MAAM,gBAAgB,OAAO,KAAK,MAAM,OAAO,cAAc,CAAC,IAAI,KAAK,MAAM,cAAc,QAAQ,CAAC;AAAA,EACxG;AAAI,GACJ,GAAG,WAAW,MAAM,IAAI,SAAU,iBAAiB,QAAQ,eAAe;AAAE,SAAQ;AAAA,IAChF,KAAK,KAAK,MAAM,gBAAgB,MAAM,cAAc,MAAM;AAAA,IAC1D,MAAM,gBAAgB,OAAO,KAAK,MAAM,OAAO,cAAc,CAAC,IAAI,KAAK,MAAM,cAAc,QAAQ,CAAC;AAAA,EACxG;AAAI,GACJ;AACJ,IAAI,YAAY,OAAO,WAAW,cAAc,SAAS;AAAA,EACrD,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AACb;AACA,IAAI;AAAA;AAAA,GAA0B,WAAY;AACtC,aAASC,UAAS,WAAW;AACzB,UAAI,cAAc,QAAQ;AAAE,oBAAY,CAAC;AAAA,MAAG;AAC5C,WAAK,YAAY;AACjB,WAAK,YAAY,OAAO,OAAO,CAAC,GAAG,kBAAkB,SAAS;AAAA,IAClE;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,QAAQ;AAErD,UAAI,UAAU;AAAA,QACV,MAAM,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,MAChB;AAEA,aAAO,OAAO,gBAAgB,iBAAiB,OAAO,YAAY,EAAE,aAAa,UAAU;AACvF,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,OAAO,OAAO;AACtB,iBAAS,OAAO;AAAA,MACpB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,QAAQ;AACrD,UAAI,cAAc;AAClB,UAAI,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,MACV;AAEA,aAAO,YAAY,cAAc;AAC7B,YAAI,WAAW,iBAAiB,YAAY,YAAY;AAIxD,YAAI,SAAS,aAAa,YACtB,SAAS,cACT,SAAS,WAAW;AACpB,cAAI,SAAS,SAAS,WAAW,EAAE,GAAG;AAClC,oBAAQ,OAAO,SAAS,SAAS,WAAW,EAAE;AAAA,UAClD;AACA,cAAI,SAAS,SAAS,YAAY,EAAE,GAAG;AACnC,oBAAQ,QAAQ,SAAS,SAAS,YAAY,EAAE;AAAA,UACpD;AAAA,QACJ;AACA,sBAAc,YAAY;AAAA,MAC9B;AACA,UAAI,aAAa,OAAO,sBAAsB;AAC9C,UAAI,eAAe,SAAS,KAAK,sBAAsB;AACvD,aAAO;AAAA,QACH,KAAK,WAAW,MAAM,aAAa,MAAM,QAAQ;AAAA,QACjD,MAAM,WAAW,OAAO,aAAa,OAAO,QAAQ;AAAA,MACxD;AAAA,IACJ;AAEA,IAAAA,UAAS,UAAU,eAAe,SAAU,WAAW,QAAQ,WAAW;AACtE,UAAI,kBAAkB,KAAK,kBAAkB,SAAS;AACtD,UAAI,gBAAgB,UAAU,sBAAsB;AACpD,aAAO,KAAK,kBAAkB,iBAAiB,eAAe,QAAQ,SAAS;AAAA,IACnF;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,WAAW,QAAQ,WAAW;AACtE,UAAI,kBAAkB,KAAK,kBAAkB,SAAS;AACtD,UAAI,gBAAgB,UAAU,sBAAsB;AACpD,aAAO,KAAK,kBAAkB,iBAAiB,eAAe,QAAQ,SAAS;AAAA,IACnF;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,WAAW,QAAQ,WAAW,gBAAgB;AACtF,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB,KAAK,kBAAkB,KAAK,IAAI;AAAA,MAAG;AACrF,UAAI,kBAAkB,eAAe,SAAS;AAC9C,UAAI,gBAAgB,UAAU,sBAAsB;AACpD,aAAO,KAAK,kBAAkB,iBAAiB,eAAe,QAAQ,SAAS;AAAA,IACnF;AACA,IAAAA,UAAS,UAAU,iBAAiB,SAAU,QAAQ,QAAQ,WAAW;AACrE,aAAO,KAAK,kBAAkB,QAAQ,EAAE,KAAK,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,EAAE,GAAG,QAAQ,SAAS;AAAA,IACrG;AAIA,IAAAA,UAAS,UAAU,kBAAkB,SAAU,QAAQC,WAAU;AAC7D,UAAI,eAAe,OAAO,eAAeA,UAAS;AAClD,UAAI,cAAc,OAAO,cAAcA,UAAS;AAChD,aAAO;AAAA,QACH,KAAKA,UAAS;AAAA,QACd,QAAQ;AAAA,QACR,MAAMA,UAAS;AAAA,QACf,OAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAD,UAAS,UAAU,YAAY,SAAUC,WAAU,KAAK,MAAM;AAC1D,UAAI,QAAQ,QAAQ;AAAE,cAAM;AAAA,MAAG;AAC/B,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,aAAO,OAAO,OAAO,CAAC,GAAGA,WAAU;AAAA,QAC/B,KAAKA,UAAS,MAAM;AAAA,QACpB,MAAMA,UAAS,OAAO;AAAA,MAC1B,CAAC;AAAA,IACL;AACA,IAAAD,UAAS,UAAU,aAAa,SAAU,SAASC,WAAU;AACzD,cAAQ,MAAM,MAAMA,UAAS,MAAM;AACnC,cAAQ,MAAM,OAAOA,UAAS,OAAO;AAAA,IACzC;AACA,IAAAD,UAAS,UAAU,oBAAoB,SAAU,WAAW,QAAQ,YAAY,mBAAmB,kBAAkB;AACjH,UAAI,QAAQ;AACZ,UAAI,sBAAsB,QAAQ;AAAE,4BAAoB,KAAK,yBAAyB,KAAK,IAAI;AAAA,MAAG;AAClG,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB,KAAK,aAAa,KAAK,IAAI;AAAA,MAAG;AAKpF,UAAI,qBAAqB,WAAW,IAAI,SAAU,WAAW;AACzD,YAAI,MAAM,iBAAiB,WAAW,QAAQ,SAAS;AACvD,YAAI,MAAM,MAAM,gBAAgB,QAAQ,GAAG;AAC3C,YAAI,eAAe;AACnB,YAAI,cAAc;AAClB,YAAI,YAAY,kBAAkB;AAElC,YAAI,IAAI,MAAM,UAAU,KAAK;AACzB,yBAAe,UAAU,MAAM,IAAI;AAAA,QACvC,WACS,IAAI,SAAS,UAAU,QAAQ;AACpC,yBAAe,IAAI,SAAS,UAAU;AAAA,QAC1C;AAEA,YAAI,IAAI,OAAO,UAAU,MAAM;AAC3B,wBAAc,UAAU,OAAO,IAAI;AAAA,QACvC,WACS,IAAI,QAAQ,UAAU,OAAO;AAClC,wBAAc,IAAI,QAAQ,UAAU;AAAA,QACxC;AAGA,YAAI,gBAAgB,CAAC,aAAa;AAC9B,wBAAc;AAAA,QAClB,WACS,eAAe,CAAC,cAAc;AACnC,yBAAe;AAAA,QACnB;AACA,YAAI,OAAO,OAAO,eAAe,OAAO;AACxC,YAAI,aAAa,eAAe;AAGhC,YAAI,cAAc,OAAO;AACzB,YAAI,iBAAiB,cAAc;AACnC,eAAO;AAAA,UACH;AAAA,UACA,QAAQ;AAAA,QACZ;AAAA,MACJ,CAAC;AAED,yBAAmB,KAAK,SAAU,GAAG,GAAG;AAAE,eAAO,EAAE,SAAS,EAAE;AAAA,MAAQ,CAAC;AAEvE,aAAO,mBAAmB,CAAC,EAAE;AAAA,IACjC;AACA,IAAAA,UAAS,UAAU,sBAAsB,SAAU,QAAQ,QAAQ,YAAY,mBAAmB;AAC9F,UAAI,QAAQ;AACZ,UAAI,sBAAsB,QAAQ;AAAE,4BAAoB,KAAK,yBAAyB,KAAK,IAAI;AAAA,MAAG;AAClG,UAAI,aAAa,SAAU,GAAGE,SAAQ,WAAW;AAC7C,eAAO,MAAM,eAAe,QAAQA,SAAQ,SAAS;AAAA,MACzD;AACA,aAAO,KAAK,kBAAkB,MAAM,QAAQ,YAAY,mBAAmB,UAAU;AAAA,IACzF;AACA,IAAAF,UAAS,UAAU,2BAA2B,WAAY;AACtD,aAAO;AAAA;AAAA,QAEH,KAAK;AAAA,QACL,MAAM;AAAA,QACN,QAAQ,UAAU;AAAA,QAClB,OAAO,UAAU;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,iBAAiB,eAAe,QAAQ,WAAW;AAChG,UAAI,KAAK,UAAU,SAAS,GAAG;AAC3B,eAAO,KAAK,UAAU,SAAS,EAAE,iBAAiB,QAAQ,aAAa;AAAA,MAC3E;AACA,cAAQ,MAAM,oDAAoD;AAClE,aAAO,EAAE,MAAM,GAAG,KAAK,EAAE;AAAA,IAC7B;AACA,WAAOA;AAAA,EACX,GAAE;AAAA;AAEK,IAAI,WAAW,IAAI,SAAS;;;AC3HnC,IAAM,iCAAN,MAAqC;AAAA,EACnC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,OAAO,KAAK,YAAY,aAAa;AAC1C,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,mBAAmB,sBAAsB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IACtE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,yBAAqB,KAAK,gBAAgB;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,YAAY,KAAK,KAAK;AAC3B,SAAK,OAAO,kBAAkB,MAAM;AAClC,4BAAsB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH;AACF;AACA,+BAA+B,OAAO,SAAS,uCAAuC,mBAAmB;AACvG,SAAO,KAAK,qBAAqB,gCAAmC,SAAY,MAAM,CAAC;AACzF;AACA,+BAA+B,QAA0B,mBAAmB;AAAA,EAC1E,OAAO;AAAA,EACP,SAAS,+BAA+B;AAC1C,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,KAAK,UAAU,IAAI;AAAA,EACtC;AACF;AACA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAA0B,SAAS,8BAA8B,CAAC;AACrG;AACA,sBAAsB,QAA0B,mBAAmB;AAAA,EACjE,OAAO;AAAA,EACP,SAAS,sBAAsB;AACjC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAGH,IAAMG,SAAQ,CAAC,WAAW,YAAY;AACpC,aAAW,UAAU,SAAS;AAC5B,eAAW,OAAO,QAAQ;AACxB,UAAI,OAAO,eAAe,GAAG,GAAG;AAE9B,YAAI,OAAO,GAAG,aAAa,QAAQ;AACjC,cAAI,CAAC,OAAO,GAAG,GAAG;AAChB,mBAAO,GAAG,IAAI,CAAC;AAAA,UACjB;AAGA,iBAAO,GAAG,IAAIA,OAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,QAC9C,OAAO;AACL,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,IAAM,sBAAsB,aAAW;AACrC,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,SAAO,cAAc,aAAa,UAAU,cAAc,aAAa,YAAY,cAAc,YAAY,MAAM,UAAU,cAAc,YAAY,MAAM,YAAY,cAAc,YAAY,MAAM,UAAU,cAAc,YAAY,MAAM;AACrP;AAMA,IAAM,uBAAuB,CAAC,SAAS,cAAc;AACnD,QAAM,cAAc,QAAQ,sBAAsB;AAClD,QAAM,gBAAgB,UAAU,sBAAsB;AAItD,MAAI,UAAU,YAAY,UAAU,UAAU,YAAY,QAAQ;AAGhE,UAAM,kBAAkB,YAAY,MAAM,KAAK,YAAY,MAAM,QAAQ,eAAe;AACxF,UAAM,mBAAmB,YAAY,OAAO;AAC5C,UAAM,kBAAkB,YAAY,SAAS,QAAQ,gBAAgB,OAAO,eAAe,SAAS,gBAAgB;AACpH,UAAM,oBAAoB,YAAY,SAAS,OAAO,cAAc,SAAS,gBAAgB;AAC7F,UAAM,sBAAsB,EAAE,mBAAmB,mBAAmB,oBAAoB;AACxF,WAAO;AAAA,EACT;AACA;AAAA;AAAA,IAEE,YAAY,SAAS,QAAQ,gBAAgB,cAAc,UAAU,UAAU,eAAe,UAAU,gBAAgB,KAAK,YAAY,OAAO,CAAC,QAAQ;AAAA;AAE7J;AACA,IAAM,uBAAuB,UAAQ;AACnC,QAAM,WAAW,CAAC,SAAS,IAAI;AAC/B,SAAO,KAAK,iBAAiB,SAAS,SAAS,MAAM;AACnD,QAAI,oBAAoB,IAAI,GAAG;AAC7B,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,KAAK;AAAA,EACd;AACA,SAAO;AACT;AAuDA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,KAAK,UAAU,IAAI;AAAA,EACtC;AAAA,EACA,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,UAAM,oBAAoB,qBAAqB,aAAa;AAC5D,WAAO,KAAK,KAAK,KAAK,IAAI,MAAM;AAC9B,iBAAW,UAAU,mBAAmB;AACtC,YAAI,CAAC,qBAAqB,QAAQ,MAAM,GAAG;AACzC,iBAAO;AAAA,YACL,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAmB,SAAS,8BAA8B,CAAC;AAC9F;AACA,eAAe,QAA0B,mBAAmB;AAAA,EAC1D,OAAO;AAAA,EACP,SAAS,eAAe;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,qBAAqB,CAAC,eAAe,cAAc;AACvD,UAAQ,WAAW;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,eAAe,WAAW;AAAA,QACzC,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACE,aAAO,UAAU,eAAe,SAAS;AAAA,EAC7C;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,cAAc;AACZ,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,cAAc;AACnB,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,YAAY,WAAW,UAAU;AAC/B,QAAI,CAAC,KAAK,aAAa,IAAI,SAAS,GAAG;AACrC,UAAI,KAAK,aAAa;AACpB,aAAK,aAAa,IAAI,WAAW,mBAAmB,KAAK,aAAa,SAAS,CAAC;AAAA,MAClF,OAAO;AACL,aAAK,aAAa,IAAI,WAAW,IAAI,WAAW,CAAC;AAAA,MACnD;AAAA,IACF;AACA,UAAM,aAAa,KAAK,aAAa,IAAI,SAAS;AAClD,SAAK,cAAc,IAAI,WAAW,UAAU,QAAQ,CAAC;AAAA,EACvD;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,YAAY,SAAS,QAAQ;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,YAAY;AAC/B,SAAK,eAAe;AAAA,EACtB;AACF;AACA,gBAAgB,OAAO,SAAS,wBAAwB,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,iBAAiB;AACpD;AACA,gBAAgB,QAA0B,mBAAmB;AAAA,EAC3D,OAAO;AAAA,EACP,SAAS,gBAAgB;AAC3B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,UAAU,oBAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,GAAG,eAAe,WAAW,UAAU;AACrC,QAAI,CAAC,KAAK,QAAQ,IAAI,aAAa,GAAG;AACpC,WAAK,QAAQ,IAAI,eAAe,oBAAI,IAAI,CAAC;AAAA,IAC3C;AACA,UAAM,WAAW,KAAK,QAAQ,IAAI,aAAa;AAC/C,QAAI,CAAC,SAAS,IAAI,SAAS,GAAG;AAC5B,eAAS,IAAI,WAAW,mBAAmB,eAAe,SAAS,CAAC;AAAA,IACtE;AACA,UAAM,eAAe,SAAS,IAAI,SAAS,EAAE,UAAU,QAAQ;AAC/D,SAAK,cAAc,IAAI,YAAY;AAAA,EACrC;AAAA,EACA,WAAW,WAAW,UAAU;AAC9B,SAAK,gBAAgB,YAAY,WAAW,QAAQ;AAAA,EACtD;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,YAAY;AAAA,EACjC;AACF;AACA,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAiB,SAAS,eAAe,CAAC;AAC7E;AACA,aAAa,QAA0B,mBAAmB;AAAA,EACxD,OAAO;AAAA,EACP,SAAS,aAAa;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAGH,SAAS,kCAAkC,eAAe;AACxD,SAAO,iBAAiB,IAAI,gBAAgB;AAC9C;AAEA,IAAM,4BAA4B;AAAA,EAChC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,eAAe,CAAC;AAAA,EACxD,YAAY;AACd;AAEA,SAAS,mDAAmD,eAAe,QAAQ;AACjF,SAAO,iBAAiB,IAAI,+BAA+B,MAAM;AACnE;AAEA,IAAM,6CAA6C;AAAA,EACjD,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,8BAA8B,GAAG,MAAM;AAAA,EAC/E,YAAY;AACd;AACA,IAAM,cAAN,MAAkB;AAAC;AACnB,YAAY,OAAO,SAAS,oBAAoB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,aAAa;AAChD;AACA,YAAY,OAAyB,iBAAiB;AAAA,EACpD,MAAM;AACR,CAAC;AACD,YAAY,OAAyB,iBAAiB;AAAA,EACpD,WAAW,CAAC,2BAA2B,4CAA4C,gCAAgC,iBAAiB,uBAAuB,gBAAgB,YAAY;AACzL,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,2BAA2B,4CAA4C,gCAAgC,iBAAiB,uBAAuB,gBAAgB,YAAY;AAAA,IACzL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3aH,IAAI,KAAK;AAAA,EACP,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,6BAA6B;AAAA,EAC/B;AAAA,EACA,gBAAgB;AAAA,IACd,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,MACR,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACnB;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,EACf;AAAA,EACA,iBAAiB;AAAA,IACf,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,IACZ,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,IACxB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,uBAAuB;AAAA,IACvB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB;AAAA,EACA,sBAAsB;AAAA,IACpB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,cAAc;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,EACvB;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,MACR,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB;AAAA,IAClB;AAAA,EACF;AACF;AAmBA,IAAM,UAAU,CAAC,SAAS,cAAc,QAAQ,KAAK,IAAI,SAAO;AAC9D,QAAM,OAAO,OAAO,KAAK,SAAS;AAClC,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,UAAU,GAAG;AAC3B,UAAM,IAAI,QAAQ,IAAI,OAAO,SAAS,GAAG,UAAU,GAAG,GAAG,KAAK;AAAA,EAChE;AACA,SAAO;AACT,CAAC,CAAC;AAWF,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,MAAM,MAAM;AACtB,SAAK,OAAO;AACZ,SAAK,OAAO;AAIZ,SAAK,kBAAkB,KAAK,KAAK,IAAI,KAAK,IAAI;AAI9C,SAAK,eAAe;AAKpB,UAAM,QAAQ,KAAK,KAAK,iBAAiB,KAAK,IAAI;AAClD,SAAK,YAAY,IAAI,gBAAgB,KAAK;AAC1C,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AAKZ,WAAO,IAAI,MAAM,KAAK,cAAc,KAAK,WAAW,KAAK,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACd,SAAK,eAAe;AAGpB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,SAAS;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,WAAK,eAAe,MAAM,UAAU,OAAK;AACvC,aAAK,UAAU,KAAK,CAAC;AAAA,MACvB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,KAAK,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AASA,IAAM,OAAN,MAAW;AAAA,EACT,cAAc;AACZ,SAAK,qBAAqB;AAC1B,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,SAAS,IAAI,gBAAgB,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,UAAU,SAAS;AAC3B,SAAK,OAAO,KAAK,QAAQ;AACzB,QAAI,SAAS;AACX,WAAK,IAAI,OAAO;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACV,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,SAAK,qBAAqBC,OAAM,CAAC,GAAG,IAAI,OAAO;AAE/C,UAAM,eAAe,MAAM,KAAK,KAAK,YAAY;AACjD,eAAW,CAAC,MAAM,OAAO,KAAK,cAAc;AAC1C,cAAQ,KAAK,KAAK,iBAAiB,IAAI,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,QAAI,CAAC,MAAM;AACT,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,WAAW,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,aAAa;AACvB,UAAM,SAAS,KAAK,iBAAiB,WAAW;AAChD,UAAM,WAAW,CAAC;AAClB,eAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,UAAI,OAAO,GAAG,MAAM,OAAO,OAAO,GAAG,CAAC,GAAG;AACvC,iBAAS,GAAG,IAAI,KAAK,YAAY,GAAG,WAAW,IAAI,GAAG,EAAE;AAAA,MAC1D,OAAO;AACL,iBAAS,GAAG,IAAI,KAAK,WAAW,GAAG,WAAW,IAAI,GAAG,EAAE;AAAA,MACzD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM;AACnB,WAAO,IAAI,YAAY,MAAM,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,QAAQ,SAAS,WAAW;AAC1B,WAAO,QAAQ,SAAS,SAAS;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,MAAM;AACrB,QAAI,QAAQ,KAAK;AACjB,eAAW,WAAW,KAAK,MAAM,GAAG,GAAG;AACrC,UAAI,MAAM,OAAO,MAAM,UAAa,MAAM,OAAO,MAAM,MAAM;AAC3D,gBAAQ,MAAM,OAAO;AAAA,MACvB,OAAO;AACL,cAAM,IAAI,MAAM,UAAU,OAAO,OAAO,IAAI,EAAE;AAAA,MAChD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AACf,QAAI;AAEF,YAAM,QAAQ,KAAK,iBAAiB,IAAI;AACxC,UAAI,KAAK,aAAa,IAAI,IAAI,GAAG;AAC/B,eAAO,KAAK,aAAa,IAAI,IAAI;AAAA,MACnC;AACA,YAAM,cAAc,IAAI,gBAAgB,KAAK;AAC7C,WAAK,aAAa,IAAI,MAAM,WAAW;AACvC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,KAAK;AAAA,IACrB;AAAA,EACF;AACF;AACA,KAAK,OAAO,SAAS,aAAa,mBAAmB;AACnD,SAAO,KAAK,qBAAqB,MAAM;AACzC;AACA,KAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,KAAK;AAChB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAN,MAAkB;AAAA,EAChB,UAAU,OAAO,WAAW;AAC1B,WAAO,QAAQ,OAAO,SAAS;AAAA,EACjC;AACF;AACA,YAAY,OAAO,SAAS,oBAAoB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,aAAa;AAChD;AACA,YAAY,QAA0B,aAAa;AAAA,EACjD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,SAAS,8BAA8B,eAAe;AACpD,SAAO,iBAAiB,IAAI,KAAK;AACnC;AAEA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,CAAC;AAAA,EAC7C,YAAY;AACd;AACA,IAAM,aAAN,MAAiB;AAAC;AAClB,WAAW,OAAO,SAAS,mBAAmB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,YAAY;AAC/C;AACA,WAAW,OAAyB,iBAAiB;AAAA,EACnD,MAAM;AAAA,EACN,cAAc,CAAC,WAAW;AAAA,EAC1B,SAAS,CAAC,WAAW;AACvB,CAAC;AACD,WAAW,OAAyB,iBAAiB;AAAA,EACnD,WAAW,CAAC,MAAM,qBAAqB;AACzC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,WAAW;AAAA,MAC1B,SAAS,CAAC,WAAW;AAAA,MACrB,WAAW,CAAC,MAAM,qBAAqB;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["PLACEMENTS", "Position", "position", "target", "merge", "merge"]}