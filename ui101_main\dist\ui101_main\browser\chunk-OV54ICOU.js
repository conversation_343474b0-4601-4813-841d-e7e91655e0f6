import {
  AuthService,
  Button,
  ButtonModule
} from "./chunk-KFEL747P.js";
import {
  Async<PERSON>ipe,
  CommonModule,
  Component,
  Router,
  inject,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-U3IFNCJ5.js";

// src/app/projects/projects.component.ts
function ProjectsComponent_Conditional_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Logged in as: ", ctx.email);
  }
}
var ProjectsComponent = class _ProjectsComponent {
  auth = inject(AuthService);
  router = inject(Router);
  async signOut() {
    try {
      console.log("Signing out...");
      await this.auth.signOut();
      console.log("Sign out successful, navigating to login...");
      setTimeout(async () => {
        await this.router.navigate(["/login"]);
      }, 100);
    } catch (error) {
      console.error("Sign out error:", error);
    }
  }
  static \u0275fac = function ProjectsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProjectsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProjectsComponent, selectors: [["app-projects"]], decls: 11, vars: 3, consts: [[1, "projects-container"], [1, "projects-header"], ["cdsButton", "primary", "size", "md", 3, "click"], [1, "projects-content"]], template: function ProjectsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "header", 1)(2, "h1");
      \u0275\u0275text(3, "Projects Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "button", 2);
      \u0275\u0275listener("click", function ProjectsComponent_Template_button_click_4_listener() {
        return ctx.signOut();
      });
      \u0275\u0275text(5, " Sign Out ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "main", 3)(7, "p");
      \u0275\u0275text(8, "Welcome to your projects dashboard!");
      \u0275\u0275elementEnd();
      \u0275\u0275conditionalCreate(9, ProjectsComponent_Conditional_9_Template, 2, 1, "p");
      \u0275\u0275pipe(10, "async");
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      let tmp_0_0;
      \u0275\u0275advance(9);
      \u0275\u0275conditional((tmp_0_0 = \u0275\u0275pipeBind1(10, 1, ctx.auth.user$)) ? 9 : -1, tmp_0_0);
    }
  }, dependencies: [
    CommonModule,
    ButtonModule,
    Button,
    AsyncPipe
  ], styles: ["\n\n.projects-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.projects-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.projects-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 400;\n}\n.projects-content[_ngcontent-%COMP%] {\n  padding: 1rem 0;\n}\n/*# sourceMappingURL=projects.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProjectsComponent, [{
    type: Component,
    args: [{ selector: "app-projects", standalone: true, imports: [
      CommonModule,
      ButtonModule
    ], template: `
    <div class="projects-container">
      <header class="projects-header">
        <h1>Projects Dashboard</h1>
        <button
          cdsButton="primary"
          size="md"
          (click)="signOut()">
          Sign Out
        </button>
      </header>

      <main class="projects-content">
        <p>Welcome to your projects dashboard!</p>
        @if (auth.user$ | async; as user) {
          <p>Logged in as: {{ user.email }}</p>
        }
      </main>
    </div>
  `, styles: ["/* angular:styles/component:scss;9f6b3f2990caf18e9f0c91865e409808bb7b927f7d2311e0587c1ac70d616057;C:/Users/<USER>/ANACE/Dev/UI101_main_dashboard/ui101_main/src/app/projects/projects.component.ts */\n.projects-container {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.projects-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.projects-header h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 400;\n}\n.projects-content {\n  padding: 1rem 0;\n}\n/*# sourceMappingURL=projects.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProjectsComponent, { className: "ProjectsComponent", filePath: "src/app/projects/projects.component.ts", lineNumber: 63 });
})();
export {
  ProjectsComponent
};
//# sourceMappingURL=chunk-OV54ICOU.js.map
