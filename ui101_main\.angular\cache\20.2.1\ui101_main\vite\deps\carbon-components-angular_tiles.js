import {
  IconDirective,
  IconModule
} from "./chunk-PHHHLKJS.js";
import {
  I18n,
  I18nModule,
  merge
} from "./chunk-4Y3KUSI5.js";
import {
  Router
} from "./chunk-LPU4MK7Q.js";
import "./chunk-AOLA72SF.js";
import {
  NG_VALUE_ACCESSOR
} from "./chunk-RULK2ZOQ.js";
import {
  AsyncPipe,
  CommonModule,
  NgClass,
  NgIf,
  NgStyle,
  NgTemplateOutlet
} from "./chunk-Y7V6UAPQ.js";
import {
  Component,
  ContentChildren,
  Directive,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  NgModule,
  Optional,
  Output,
  TemplateRef,
  ViewChild,
  setClassMetadata,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵpureFunction3,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-6YWT35PS.js";
import "./chunk-OSECCFIU.js";
import "./chunk-IONO6HLE.js";
import {
  Subject,
  takeUntil
} from "./chunk-PHHPLELC.js";

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-link.mjs
var Link = class {
  constructor() {
    this.baseClass = true;
    this.inline = false;
  }
  /**
   * Set to true to disable link.
   */
  set disabled(disabled) {
    this._disabled = disabled;
    this.tabindex = this.disabled ? -1 : null;
  }
  get disabled() {
    return this._disabled;
  }
};
Link.ɵfac = function Link_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Link)();
};
Link.ɵdir = ɵɵdefineDirective({
  type: Link,
  selectors: [["", "cdsLink", ""], ["", "ibmLink", ""]],
  hostVars: 8,
  hostBindings: function Link_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵattribute("tabindex", ctx.tabindex)("aria-disabled", ctx.disabled);
      ɵɵclassProp("cds--link", ctx.baseClass)("cds--link--inline", ctx.inline)("cds--link--disabled", ctx.disabled);
    }
  },
  inputs: {
    inline: "inline",
    disabled: "disabled"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Link, [{
    type: Directive,
    args: [{
      selector: "[cdsLink], [ibmLink]"
    }]
  }], null, {
    baseClass: [{
      type: HostBinding,
      args: ["class.cds--link"]
    }],
    tabindex: [{
      type: HostBinding,
      args: ["attr.tabindex"]
    }],
    inline: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["class.cds--link--inline"]
    }],
    disabled: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["attr.aria-disabled"]
    }, {
      type: HostBinding,
      args: ["class.cds--link--disabled"]
    }]
  });
})();
var LinkIconDirective = class {
  constructor() {
    this.iconClass = true;
  }
};
LinkIconDirective.ɵfac = function LinkIconDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LinkIconDirective)();
};
LinkIconDirective.ɵdir = ɵɵdefineDirective({
  type: LinkIconDirective,
  selectors: [["", "ibmLinkIcon", ""], ["", "cdsLinkIcon", ""]],
  hostVars: 2,
  hostBindings: function LinkIconDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--link__icon", ctx.iconClass);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LinkIconDirective, [{
    type: Directive,
    args: [{
      selector: "[ibmLinkIcon], [cdsLinkIcon]"
    }]
  }], null, {
    iconClass: [{
      type: HostBinding,
      args: ["class.cds--link__icon"]
    }]
  });
})();
var LinkModule = class {
};
LinkModule.ɵfac = function LinkModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LinkModule)();
};
LinkModule.ɵmod = ɵɵdefineNgModule({
  type: LinkModule,
  declarations: [Link, LinkIconDirective],
  imports: [CommonModule],
  exports: [Link, LinkIconDirective]
});
LinkModule.ɵinj = ɵɵdefineInjector({
  imports: [CommonModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LinkModule, [{
    type: NgModule,
    args: [{
      declarations: [Link, LinkIconDirective],
      exports: [Link, LinkIconDirective],
      imports: [CommonModule]
    }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-tiles.mjs
var _c0 = ["*"];
var _c1 = (a0, a1) => ({
  "cds--tile--light": a0,
  "cds--tile--disabled cds--link--disabled": a1
});
var _c2 = ["container"];
var _c3 = [[["", "cdsAboveFold", ""], ["", "ibmAboveFold", ""], ["", 8, "cds--tile-content__above-the-fold"]], [["", "cdsBelowFold", ""], ["", "ibmBelowFold", ""], ["", 8, "cds--tile-content__below-the-fold"]]];
var _c4 = ["[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold", "[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold"];
var _c5 = (a0, a1) => ({
  "cds--tile--is-expanded": a0,
  "cds--tile--light": a1
});
var _c6 = (a0) => ({
  "max-height": a0
});
function ExpandableTile_button_0_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExpandableTile_button_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 5);
    ɵɵpipe(1, "async");
    ɵɵlistener("click", function ExpandableTile_button_0_Template_button_click_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onClick());
    });
    ɵɵtemplate(2, ExpandableTile_button_0_ng_container_2_Template, 1, 0, "ng-container", 6);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    const expandableTileContent_r3 = ɵɵreference(5);
    ɵɵproperty("ngClass", ɵɵpureFunction2(7, _c5, ctx_r1.expanded, ctx_r1.theme === "light"))("ngStyle", ɵɵpureFunction1(10, _c6, ctx_r1.expandedHeight + "px"));
    ɵɵattribute("aria-expanded", ctx_r1.expanded)("title", ɵɵpipeBind1(1, 5, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", expandableTileContent_r3);
  }
}
function ExpandableTile_div_1_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExpandableTile_div_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 7);
    ɵɵpipe(1, "async");
    ɵɵtemplate(2, ExpandableTile_div_1_ng_container_2_Template, 1, 0, "ng-container", 6);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    const expandableTileContent_r3 = ɵɵreference(5);
    ɵɵproperty("ngClass", ɵɵpureFunction2(6, _c5, ctx_r1.expanded, ctx_r1.theme === "light"))("ngStyle", ɵɵpureFunction1(9, _c6, ctx_r1.expandedHeight + "px"));
    ɵɵattribute("title", ɵɵpipeBind1(1, 4, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", expandableTileContent_r3);
  }
}
function ExpandableTile_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 8);
  }
}
function ExpandableTile_ng_template_4_div_4_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExpandableTile_ng_template_4_div_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 12);
    ɵɵtemplate(1, ExpandableTile_ng_template_4_div_4_ng_container_1_Template, 1, 0, "ng-container", 6);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵnextContext(2);
    const chevronIcon_r4 = ɵɵreference(3);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", chevronIcon_r4);
  }
}
function ExpandableTile_ng_template_4_button_5_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExpandableTile_ng_template_4_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 13);
    ɵɵpipe(1, "async");
    ɵɵlistener("click", function ExpandableTile_ng_template_4_button_5_Template_button_click_0_listener() {
      ɵɵrestoreView(_r5);
      const ctx_r1 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r1.onClick());
    });
    ɵɵtemplate(2, ExpandableTile_ng_template_4_button_5_ng_container_2_Template, 1, 0, "ng-container", 6);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext(2);
    const chevronIcon_r4 = ɵɵreference(3);
    ɵɵattribute("aria-expanded", ctx_r1.expanded)("aria-label", ɵɵpipeBind1(1, 3, ctx_r1.expanded ? ctx_r1.collapse.subject : ctx_r1.expand.subject));
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", chevronIcon_r4);
  }
}
function ExpandableTile_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", null, 2)(2, "div", 9);
    ɵɵprojection(3);
    ɵɵelementEnd();
    ɵɵtemplate(4, ExpandableTile_ng_template_4_div_4_Template, 2, 1, "div", 10)(5, ExpandableTile_ng_template_4_button_5_Template, 3, 5, "button", 11);
    ɵɵelementStart(6, "div", 9);
    ɵɵprojection(7, 1);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵadvance(4);
    ɵɵproperty("ngIf", !ctx_r1.interactive);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r1.interactive);
  }
}
var _c7 = ["input"];
var _c8 = (a0, a1, a2) => ({
  "cds--tile--is-selected": a0,
  "cds--tile--light": a1,
  "cds--tile--disabled": a2
});
function SelectionTile__svg_svg_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 7);
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("cdsIcon", ctx_r1.multiple ? "checkbox" : "checkmark");
  }
}
function SelectionTile_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 7);
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("cdsIcon", ctx_r1.multiple ? "checkbox--checked--filled" : "checkmark--filled");
  }
}
var _c9 = [[["ibm-selection-tile"], ["cds-selection-tile"]]];
var _c10 = ["ibm-selection-tile,cds-selection-tile"];
function TileGroup_legend_1_1_ng_template_0_Template(rf, ctx) {
}
function TileGroup_legend_1_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TileGroup_legend_1_1_ng_template_0_Template, 0, 0, "ng-template", 4);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.legend);
  }
}
function TileGroup_legend_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵtextInterpolate(ctx_r0.legend);
  }
}
function TileGroup_legend_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "legend", 2);
    ɵɵtemplate(1, TileGroup_legend_1_1_Template, 1, 1, null, 3)(2, TileGroup_legend_1_ng_template_2_Template, 1, 1, "ng-template", null, 0, ɵɵtemplateRefExtractor);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const legendLabel_r2 = ɵɵreference(3);
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.legend))("ngIfElse", legendLabel_r2);
  }
}
var ClickableTileIconDirective = class {
  constructor() {
    this.icon = true;
  }
};
ClickableTileIconDirective.ɵfac = function ClickableTileIconDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ClickableTileIconDirective)();
};
ClickableTileIconDirective.ɵdir = ɵɵdefineDirective({
  type: ClickableTileIconDirective,
  selectors: [["", "cdsClickableTileIcon", ""], ["", "ibmClickableTileIcon", ""]],
  hostVars: 2,
  hostBindings: function ClickableTileIconDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--tile--icon", ctx.icon);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClickableTileIconDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsClickableTileIcon], [ibmClickableTileIcon]"
    }]
  }], null, {
    icon: [{
      type: HostBinding,
      args: ["class.cds--tile--icon"]
    }]
  });
})();
var ClickableTile = class {
  constructor(router) {
    this.router = router;
    this.theme = "dark";
    this.href = "#";
    this.disabled = false;
    this.navigation = new EventEmitter();
  }
  navigate(event) {
    if (this.router && this.route && !this.disabled) {
      event.preventDefault();
      const status = this.router.navigate(this.route, this.routeExtras);
      this.navigation.emit(status);
    }
  }
};
ClickableTile.ɵfac = function ClickableTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ClickableTile)(ɵɵdirectiveInject(Router, 8));
};
ClickableTile.ɵcmp = ɵɵdefineComponent({
  type: ClickableTile,
  selectors: [["cds-clickable-tile"], ["ibm-clickable-tile"]],
  inputs: {
    theme: "theme",
    href: "href",
    target: "target",
    rel: "rel",
    disabled: "disabled",
    route: "route",
    routeExtras: "routeExtras"
  },
  outputs: {
    navigation: "navigation"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 2,
  vars: 8,
  consts: [["cdsLink", "", "tabindex", "0", 1, "cds--tile", "cds--tile--clickable", 3, "click", "ngClass"]],
  template: function ClickableTile_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef();
      ɵɵelementStart(0, "a", 0);
      ɵɵlistener("click", function ClickableTile_Template_a_click_0_listener($event) {
        return ctx.navigate($event);
      });
      ɵɵprojection(1);
      ɵɵelementEnd();
    }
    if (rf & 2) {
      ɵɵproperty("ngClass", ɵɵpureFunction2(5, _c1, ctx.theme === "light", ctx.disabled));
      ɵɵattribute("href", ctx.disabled ? null : ctx.href, ɵɵsanitizeUrl)("target", ctx.target)("rel", ctx.rel ? ctx.rel : null)("aria-disabled", ctx.disabled);
    }
  },
  dependencies: [NgClass, Link],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClickableTile, [{
    type: Component,
    args: [{
      selector: "cds-clickable-tile, ibm-clickable-tile",
      template: `
	<a
		cdsLink
		class="cds--tile cds--tile--clickable"
		[ngClass]="{
			'cds--tile--light': theme === 'light',
			'cds--tile--disabled cds--link--disabled' : disabled
		}"
		tabindex="0"
		(click)="navigate($event)"
		[attr.href]="disabled ? null : href"
		[attr.target]="target"
		[attr.rel]="rel ? rel : null"
		[attr.aria-disabled]="disabled">
		<ng-content></ng-content>
	</a>`
    }]
  }], function() {
    return [{
      type: Router,
      decorators: [{
        type: Optional
      }]
    }];
  }, {
    theme: [{
      type: Input
    }],
    href: [{
      type: Input
    }],
    target: [{
      type: Input
    }],
    rel: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    route: [{
      type: Input
    }],
    routeExtras: [{
      type: Input
    }],
    navigation: [{
      type: Output
    }]
  });
})();
var ExpandableTile = class {
  constructor(i18n, element) {
    this.i18n = i18n;
    this.element = element;
    this.theme = "dark";
    this.expanded = false;
    this.interactive = false;
    this.tileMaxHeight = 0;
    this.currentExpandedHeight = 0;
    this.expand = this.i18n.getOverridable("TILES.EXPAND");
    this.collapse = this.i18n.getOverridable("TILES.COLLAPSE");
  }
  /**
   * Expects an object that contains some or all of:
   * ```
   * {
   *		"EXPAND": "Expand",
   *		"COLLAPSE": "Collapse",
   * }
   * ```
   */
  set translations(value) {
    const valueWithDefaults = merge(this.i18n.getMultiple("TILES"), value);
    this.expand.override(valueWithDefaults.EXPAND);
    this.collapse.override(valueWithDefaults.COLLAPSE);
  }
  ngAfterViewInit() {
    this.updateMaxHeight();
  }
  get expandedHeight() {
    const tile = this.element.nativeElement.querySelector(".cds--tile");
    const tilePadding = parseInt(getComputedStyle(tile).paddingBottom, 10) + parseInt(getComputedStyle(tile).paddingTop, 10);
    const expandedHeight = this.tileMaxHeight + tilePadding;
    if (!isNaN(expandedHeight)) {
      this.currentExpandedHeight = expandedHeight;
    }
    return this.currentExpandedHeight;
  }
  updateMaxHeight() {
    if (this.expanded) {
      this.tileMaxHeight = this.tileContainer.nativeElement.getBoundingClientRect().height;
    } else {
      this.tileMaxHeight = this.element.nativeElement.querySelector(".cds--tile-content__above-the-fold").getBoundingClientRect().height;
    }
  }
  onClick() {
    this.expanded = !this.expanded;
    this.updateMaxHeight();
  }
};
ExpandableTile.ɵfac = function ExpandableTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTile)(ɵɵdirectiveInject(I18n), ɵɵdirectiveInject(ElementRef));
};
ExpandableTile.ɵcmp = ɵɵdefineComponent({
  type: ExpandableTile,
  selectors: [["cds-expandable-tile"], ["ibm-expandable-tile"]],
  viewQuery: function ExpandableTile_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c2, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.tileContainer = _t.first);
    }
  },
  inputs: {
    theme: "theme",
    expanded: "expanded",
    interactive: "interactive",
    translations: "translations"
  },
  standalone: false,
  ngContentSelectors: _c4,
  decls: 6,
  vars: 2,
  consts: [["chevronIcon", ""], ["expandableTileContent", ""], ["container", ""], ["class", "cds--tile cds--tile--expandable", "type", "button", 3, "ngClass", "ngStyle", "click", 4, "ngIf"], ["class", "cds--tile cds--tile--expandable cds--tile--expandable--interactive", 3, "ngClass", "ngStyle", 4, "ngIf"], ["type", "button", 1, "cds--tile", "cds--tile--expandable", 3, "click", "ngClass", "ngStyle"], [4, "ngTemplateOutlet"], [1, "cds--tile", "cds--tile--expandable", "cds--tile--expandable--interactive", 3, "ngClass", "ngStyle"], ["cdsIcon", "chevron--down", "size", "16"], [1, "cds--tile-content"], ["class", "cds--tile__chevron", 4, "ngIf"], ["class", "cds--tile__chevron cds--tile__chevron--interactive", "type", "button", 3, "click", 4, "ngIf"], [1, "cds--tile__chevron"], ["type", "button", 1, "cds--tile__chevron", "cds--tile__chevron--interactive", 3, "click"]],
  template: function ExpandableTile_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c3);
      ɵɵtemplate(0, ExpandableTile_button_0_Template, 3, 12, "button", 3)(1, ExpandableTile_div_1_Template, 3, 11, "div", 4)(2, ExpandableTile_ng_template_2_Template, 1, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor)(4, ExpandableTile_ng_template_4_Template, 8, 2, "ng-template", null, 1, ɵɵtemplateRefExtractor);
    }
    if (rf & 2) {
      ɵɵproperty("ngIf", !ctx.interactive);
      ɵɵadvance();
      ɵɵproperty("ngIf", ctx.interactive);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, NgStyle, IconDirective, AsyncPipe],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTile, [{
    type: Component,
    args: [{
      selector: "cds-expandable-tile, ibm-expandable-tile",
      template: `
		<button
			*ngIf="!interactive"
			class="cds--tile cds--tile--expandable"
			[ngClass]="{
				'cds--tile--is-expanded' : expanded,
				'cds--tile--light': theme === 'light'
			}"
			[ngStyle]="{'max-height': expandedHeight + 'px'}"
			type="button"
			(click)="onClick()"
			[attr.aria-expanded]="expanded"
			[attr.title]="(expanded ? collapse.subject : expand.subject) | async">
				<ng-container *ngTemplateOutlet="expandableTileContent"></ng-container>
		</button>

		<div
			*ngIf="interactive"
			class="cds--tile cds--tile--expandable cds--tile--expandable--interactive"
			[ngClass]="{
				'cds--tile--is-expanded' : expanded,
				'cds--tile--light': theme === 'light'
			}"
			[ngStyle]="{'max-height': expandedHeight + 'px'}"
			[attr.title]="(expanded ? collapse.subject : expand.subject) | async">
			<ng-container *ngTemplateOutlet="expandableTileContent"></ng-container>
		</div>

		<ng-template #chevronIcon>
			<svg cdsIcon="chevron--down" size="16"></svg>
		</ng-template>

		<ng-template #expandableTileContent>
			<div #container>
				<div class="cds--tile-content">
					<ng-content select="[cdsAboveFold],[ibmAboveFold],.cds--tile-content__above-the-fold"></ng-content>
				</div>
				<div *ngIf="!interactive" class="cds--tile__chevron">
					<ng-container *ngTemplateOutlet="chevronIcon"></ng-container>
				</div>
				<button
					*ngIf="interactive"
					class="cds--tile__chevron cds--tile__chevron--interactive"
					type="button"
					(click)="onClick()"
					[attr.aria-expanded]="expanded"
					[attr.aria-label]="(expanded ? collapse.subject : expand.subject) | async">
					<ng-container *ngTemplateOutlet="chevronIcon"></ng-container>
				</button>
				<div class="cds--tile-content">
					<ng-content select="[cdsBelowFold],[ibmBelowFold],.cds--tile-content__below-the-fold"></ng-content>
				</div>
			</div>
		</ng-template>
	`
    }]
  }], function() {
    return [{
      type: I18n
    }, {
      type: ElementRef
    }];
  }, {
    theme: [{
      type: Input
    }],
    expanded: [{
      type: Input
    }],
    interactive: [{
      type: Input
    }],
    translations: [{
      type: Input
    }],
    tileContainer: [{
      type: ViewChild,
      args: ["container"]
    }]
  });
})();
var ExpandableTileAboveFoldDirective = class {
  constructor() {
    this.aboveFold = true;
  }
};
ExpandableTileAboveFoldDirective.ɵfac = function ExpandableTileAboveFoldDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTileAboveFoldDirective)();
};
ExpandableTileAboveFoldDirective.ɵdir = ɵɵdefineDirective({
  type: ExpandableTileAboveFoldDirective,
  selectors: [["", "cdsAboveFold", ""], ["", "ibmAboveFold", ""]],
  hostVars: 2,
  hostBindings: function ExpandableTileAboveFoldDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--tile-content__above-the-fold", ctx.aboveFold);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTileAboveFoldDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsAboveFold], [ibmAboveFold]"
    }]
  }], null, {
    aboveFold: [{
      type: HostBinding,
      args: ["class.cds--tile-content__above-the-fold"]
    }]
  });
})();
var ExpandableTileBelowFoldDirective = class {
  constructor() {
    this.belowFold = true;
  }
};
ExpandableTileBelowFoldDirective.ɵfac = function ExpandableTileBelowFoldDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ExpandableTileBelowFoldDirective)();
};
ExpandableTileBelowFoldDirective.ɵdir = ɵɵdefineDirective({
  type: ExpandableTileBelowFoldDirective,
  selectors: [["", "cdsBelowFold", ""], ["", "ibmBelowFold", ""]],
  hostVars: 2,
  hostBindings: function ExpandableTileBelowFoldDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--tile-content__below-the-fold", ctx.belowFold);
    }
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExpandableTileBelowFoldDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsBelowFold], [ibmBelowFold]"
    }]
  }], null, {
    belowFold: [{
      type: HostBinding,
      args: ["class.cds--tile-content__below-the-fold"]
    }]
  });
})();
var SelectionTile = class _SelectionTile {
  constructor(i18n) {
    this.i18n = i18n;
    this.theme = "dark";
    this.id = `tile-${_SelectionTile.tileCount}`;
    this.change = new EventEmitter();
    this.disabled = false;
    this.name = "tile-group-unbound";
    this.multiple = true;
    this._selected = null;
    _SelectionTile.tileCount++;
  }
  /**
   * Updating the state of the input to match the state of the parameter passed in.
   * Set to `true` if this tile should be selected.
   */
  set selected(value) {
    this._selected = value ? true : null;
    if (this.input) {
      this.input.nativeElement.checked = this._selected;
    }
  }
  get selected() {
    return this.input ? this.input.nativeElement.checked : false;
  }
  ngAfterViewInit() {
    if (this.input) {
      setTimeout(() => {
        this.input.nativeElement.checked = this._selected;
      });
    }
  }
  keyboardInput(event) {
    if (event.key === "Enter" || event.key === "Spacebar" || event.key === " ") {
      this.selected = !this.selected;
      this.change.emit(event);
    }
  }
  onChange(event) {
    this.change.emit(event);
  }
};
SelectionTile.tileCount = 0;
SelectionTile.ɵfac = function SelectionTile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || SelectionTile)(ɵɵdirectiveInject(I18n));
};
SelectionTile.ɵcmp = ɵɵdefineComponent({
  type: SelectionTile,
  selectors: [["cds-selection-tile"], ["ibm-selection-tile"]],
  viewQuery: function SelectionTile_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c7, 7);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.input = _t.first);
    }
  },
  hostBindings: function SelectionTile_HostBindings(rf, ctx) {
    if (rf & 1) {
      ɵɵlistener("keydown", function SelectionTile_keydown_HostBindingHandler($event) {
        return ctx.keyboardInput($event);
      });
    }
  },
  inputs: {
    theme: "theme",
    id: "id",
    selected: "selected",
    value: "value",
    disabled: "disabled"
  },
  outputs: {
    change: "change"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 10,
  vars: 19,
  consts: [["input", ""], ["selectedIcon", ""], [1, "cds--tile-input", 3, "change", "id", "disabled", "type", "value", "name"], [1, "cds--tile", "cds--tile--selectable", 3, "for", "ngClass"], [1, "cds--tile__checkmark"], ["size", "16", 3, "cdsIcon", 4, "ngIf", "ngIfElse"], [1, "cds--tile-content"], ["size", "16", 3, "cdsIcon"]],
  template: function SelectionTile_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = ɵɵgetCurrentView();
      ɵɵprojectionDef();
      ɵɵelementStart(0, "input", 2, 0);
      ɵɵlistener("change", function SelectionTile_Template_input_change_0_listener($event) {
        ɵɵrestoreView(_r1);
        return ɵɵresetView(ctx.onChange($event));
      });
      ɵɵelementEnd();
      ɵɵelementStart(2, "label", 3);
      ɵɵpipe(3, "async");
      ɵɵelementStart(4, "div", 4);
      ɵɵtemplate(5, SelectionTile__svg_svg_5_Template, 1, 1, "svg", 5)(6, SelectionTile_ng_template_6_Template, 1, 1, "ng-template", null, 1, ɵɵtemplateRefExtractor);
      ɵɵelementEnd();
      ɵɵelementStart(8, "div", 6);
      ɵɵprojection(9);
      ɵɵelementEnd()();
    }
    if (rf & 2) {
      const selectedIcon_r3 = ɵɵreference(7);
      ɵɵproperty("id", ctx.id)("disabled", ctx.disabled)("type", ctx.multiple ? "checkbox" : "radio")("value", ctx.value)("name", ctx.name);
      ɵɵattribute("tabindex", ctx.disabled ? null : 0);
      ɵɵadvance(2);
      ɵɵproperty("for", ctx.id)("ngClass", ɵɵpureFunction3(15, _c8, ctx.selected, ctx.theme === "light", ctx.disabled));
      ɵɵattribute("aria-label", ɵɵpipeBind1(3, 13, ctx.i18n.get("TILES.TILE")));
      ɵɵadvance(2);
      ɵɵclassProp("cds--tile__checkmark--persistent", ctx.multiple);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.selected)("ngIfElse", selectedIcon_r3);
    }
  },
  dependencies: [NgClass, NgIf, IconDirective, AsyncPipe],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SelectionTile, [{
    type: Component,
    args: [{
      selector: "cds-selection-tile, ibm-selection-tile",
      template: `
		<input
			#input
			[attr.tabindex]="disabled ? null : 0"
			class="cds--tile-input"
			[id]="id"
			[disabled]="disabled"
			[type]="(multiple ? 'checkbox': 'radio')"
			[value]="value"
			[name]="name"
			(change)="onChange($event)"/>
		<label
			class="cds--tile cds--tile--selectable"
			[for]="id"
			[ngClass]="{
				'cds--tile--is-selected' : selected,
				'cds--tile--light': theme === 'light',
				'cds--tile--disabled' : disabled
			}"
			[attr.aria-label]="i18n.get('TILES.TILE') | async">
			<div class="cds--tile__checkmark"
				[class.cds--tile__checkmark--persistent]="multiple">
				<svg *ngIf="!selected; else selectedIcon"
					[cdsIcon]="multiple ? 'checkbox' : 'checkmark'"
					size="16">
				</svg>
				<ng-template #selectedIcon>
					<svg [cdsIcon]="multiple ? 'checkbox--checked--filled' : 'checkmark--filled'" size="16"></svg>
				</ng-template>
			</div>
			<div class="cds--tile-content">
				<ng-content></ng-content>
			</div>
		</label>
	`
    }]
  }], function() {
    return [{
      type: I18n
    }];
  }, {
    theme: [{
      type: Input
    }],
    id: [{
      type: Input
    }],
    selected: [{
      type: Input
    }],
    value: [{
      type: Input
    }],
    change: [{
      type: Output
    }],
    disabled: [{
      type: Input
    }],
    input: [{
      type: ViewChild,
      args: ["input", {
        static: true
      }]
    }],
    keyboardInput: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }]
  });
})();
var TileGroup = class _TileGroup {
  constructor() {
    this.name = `tile-group-${_TileGroup.tileGroupCount}`;
    this.multiple = false;
    this.selected = new EventEmitter();
    this.tileGroupClass = true;
    this.unsubscribe$ = new Subject();
    this.unsubscribeTiles$ = new Subject();
    this.onChange = (_) => {
    };
    this.onTouched = () => {
    };
    _TileGroup.tileGroupCount++;
  }
  ngAfterContentInit() {
    const updateTiles = () => {
      this.unsubscribeTiles$.next();
      setTimeout(() => {
        this.selectionTiles.forEach((tile) => {
          tile.name = this.name;
          tile.change.pipe(takeUntil(this.unsubscribeTiles$)).subscribe(() => {
            this.selected.emit({
              value: tile.value,
              selected: tile.selected,
              name: this.name
            });
            this.onChange(tile.value);
          });
          tile.multiple = this.multiple;
        });
      });
    };
    updateTiles();
    this.selectionTiles.changes.pipe(takeUntil(this.unsubscribe$)).subscribe((_) => updateTiles());
  }
  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this.unsubscribeTiles$.next();
    this.unsubscribeTiles$.complete();
  }
  writeValue(value) {
    if (!this.selectionTiles) {
      return;
    }
    this.selectionTiles.forEach((tile) => {
      if (tile.value === value) {
        tile.selected = true;
      } else {
        tile.selected = false;
      }
    });
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TileGroup.tileGroupCount = 0;
TileGroup.ɵfac = function TileGroup_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TileGroup)();
};
TileGroup.ɵcmp = ɵɵdefineComponent({
  type: TileGroup,
  selectors: [["cds-tile-group"], ["ibm-tile-group"]],
  contentQueries: function TileGroup_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      ɵɵcontentQuery(dirIndex, SelectionTile, 4);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.selectionTiles = _t);
    }
  },
  hostVars: 2,
  hostBindings: function TileGroup_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--tile-group", ctx.tileGroupClass);
    }
  },
  inputs: {
    name: "name",
    multiple: "multiple",
    legend: "legend"
  },
  outputs: {
    selected: "selected"
  },
  standalone: false,
  features: [ɵɵProvidersFeature([{
    provide: NG_VALUE_ACCESSOR,
    useExisting: TileGroup,
    multi: true
  }])],
  ngContentSelectors: _c10,
  decls: 3,
  vars: 1,
  consts: [["legendLabel", ""], ["class", "cds--label", 4, "ngIf"], [1, "cds--label"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"]],
  template: function TileGroup_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c9);
      ɵɵelementStart(0, "fieldset");
      ɵɵtemplate(1, TileGroup_legend_1_Template, 4, 2, "legend", 1);
      ɵɵprojection(2);
      ɵɵelementEnd();
    }
    if (rf & 2) {
      ɵɵadvance();
      ɵɵproperty("ngIf", ctx.legend);
    }
  },
  dependencies: [NgIf, NgTemplateOutlet],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TileGroup, [{
    type: Component,
    args: [{
      selector: "cds-tile-group, ibm-tile-group",
      template: `
		<fieldset>
			<legend *ngIf="legend" class="cds--label">
				<ng-template *ngIf="isTemplate(legend); else legendLabel;" [ngTemplateOutlet]="legend"></ng-template>
				<ng-template #legendLabel>{{legend}}</ng-template>
			</legend>
			<ng-content select="ibm-selection-tile,cds-selection-tile"></ng-content>
		</fieldset>`,
      providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: TileGroup,
        multi: true
      }]
    }]
  }], function() {
    return [];
  }, {
    name: [{
      type: Input
    }],
    multiple: [{
      type: Input
    }],
    legend: [{
      type: Input
    }],
    selected: [{
      type: Output
    }],
    tileGroupClass: [{
      type: HostBinding,
      args: ["class.cds--tile-group"]
    }],
    selectionTiles: [{
      type: ContentChildren,
      args: [SelectionTile]
    }]
  });
})();
var Tile = class {
  constructor() {
    this.tileClass = true;
    this.theme = "dark";
  }
  get lightThemeEnabled() {
    return this.theme === "light";
  }
};
Tile.ɵfac = function Tile_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Tile)();
};
Tile.ɵcmp = ɵɵdefineComponent({
  type: Tile,
  selectors: [["cds-tile"], ["ibm-tile"]],
  hostVars: 4,
  hostBindings: function Tile_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--tile", ctx.tileClass)("cds--tile--light", ctx.lightThemeEnabled);
    }
  },
  inputs: {
    theme: "theme"
  },
  standalone: false,
  ngContentSelectors: _c0,
  decls: 1,
  vars: 0,
  template: function Tile_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef();
      ɵɵprojection(0);
    }
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Tile, [{
    type: Component,
    args: [{
      selector: "cds-tile, ibm-tile",
      template: `<ng-content></ng-content>`
    }]
  }], null, {
    tileClass: [{
      type: HostBinding,
      args: ["class.cds--tile"]
    }],
    lightThemeEnabled: [{
      type: HostBinding,
      args: ["class.cds--tile--light"]
    }],
    theme: [{
      type: Input
    }]
  });
})();
var TilesModule = class {
};
TilesModule.ɵfac = function TilesModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TilesModule)();
};
TilesModule.ɵmod = ɵɵdefineNgModule({
  type: TilesModule,
  declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
  imports: [CommonModule, I18nModule, IconModule, LinkModule],
  exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup]
});
TilesModule.ɵinj = ɵɵdefineInjector({
  imports: [CommonModule, I18nModule, IconModule, LinkModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TilesModule, [{
    type: NgModule,
    args: [{
      declarations: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
      exports: [Tile, ClickableTile, ClickableTileIconDirective, ExpandableTileAboveFoldDirective, ExpandableTileBelowFoldDirective, ExpandableTile, SelectionTile, TileGroup],
      imports: [CommonModule, I18nModule, IconModule, LinkModule]
    }]
  }], null, null);
})();
export {
  ClickableTile,
  ClickableTileIconDirective,
  ExpandableTile,
  ExpandableTileAboveFoldDirective,
  ExpandableTileBelowFoldDirective,
  SelectionTile,
  Tile,
  TileGroup,
  TilesModule
};
//# sourceMappingURL=carbon-components-angular_tiles.js.map
