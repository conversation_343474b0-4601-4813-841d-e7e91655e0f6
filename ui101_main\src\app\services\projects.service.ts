import { Injectable, inject } from '@angular/core';
import { Firestore, collection, query, orderBy, limit, collectionData, doc, setDoc, serverTimestamp } from '@angular/fire/firestore';
import { Auth } from '@angular/fire/auth';
import { authState } from '@angular/fire/auth';
import { switchMap, map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';

export type ProjectType = 'CONCRETE' | 'FINISHES' | 'DRYWALL';

export interface Project {
  id: string;
  title: string;
  type: ProjectType;
  ownerUid: string;
  createdAt: any;   // Firestore Timestamp
  updatedAt: any;   // Firestore Timestamp
}

@Injectable({ providedIn: 'root' })
export class ProjectsService {
  private db = inject(Firestore);
  private auth = inject(Auth);

  /** Realtime stream of the current user's projects (ordered by updatedAt desc, limited to 5) */
  projects$: Observable<Project[]> = authState(this.auth).pipe(
    switchMap(user => {
      if (!user) return of([]); // not signed in
      const col = collection(this.db, `users/${user.uid}/projects`);
      const q = query(col, orderBy('updatedAt', 'desc'), limit(5));
      // idField echoes doc.id into 'id' so it matches our rules (id == projectId)
      return collectionData(q, { idField: 'id' }).pipe(map(r => r as Project[]));
    })
  );

  /** Create a project with auto-ID and echo id into the document */
  async createProject(title: string, type: ProjectType) {
    const user = this.auth.currentUser;
    if (!user) throw new Error('Not signed in');

    const col = collection(this.db, `users/${user.uid}/projects`);
    const ref = doc(col); // auto-ID
    const data: Project = {
      id: ref.id,
      title: title.trim(),
      type,
      ownerUid: user.uid,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
    await setDoc(ref, data);
    return ref.id;
  }
}
