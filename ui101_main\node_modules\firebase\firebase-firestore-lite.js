import{_registerComponent as e,registerVersion as r,_isFirebaseServerApp as i,_getProvider,getApp as s,_removeServiceInstance as o,SDK_VERSION as a}from"https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js";const stringToByteArray$1=function(e){const r=[];let i=0;for(let s=0;s<e.length;s++){let o=e.charCodeAt(s);o<128?r[i++]=o:o<2048?(r[i++]=o>>6|192,r[i++]=63&o|128):55296==(64512&o)&&s+1<e.length&&56320==(64512&e.charCodeAt(s+1))?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++s)),r[i++]=o>>18|240,r[i++]=o>>12&63|128,r[i++]=o>>6&63|128,r[i++]=63&o|128):(r[i++]=o>>12|224,r[i++]=o>>6&63|128,r[i++]=63&o|128)}return r},_={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,r){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();const i=r?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let r=0;r<e.length;r+=3){const o=e[r],a=r+1<e.length,_=a?e[r+1]:0,h=r+2<e.length,d=h?e[r+2]:0,f=o>>2,g=(3&o)<<4|_>>4;let E=(15&_)<<2|d>>6,T=63&d;h||(T=64,a||(E=64)),s.push(i[f],i[g],i[E],i[T])}return s.join("")},encodeString(e,r){return this.HAS_NATIVE_SUPPORT&&!r?btoa(e):this.encodeByteArray(stringToByteArray$1(e),r)},decodeString(e,r){return this.HAS_NATIVE_SUPPORT&&!r?atob(e):function(e){const r=[];let i=0,s=0;for(;i<e.length;){const o=e[i++];if(o<128)r[s++]=String.fromCharCode(o);else if(o>191&&o<224){const a=e[i++];r[s++]=String.fromCharCode((31&o)<<6|63&a)}else if(o>239&&o<365){const a=((7&o)<<18|(63&e[i++])<<12|(63&e[i++])<<6|63&e[i++])-65536;r[s++]=String.fromCharCode(55296+(a>>10)),r[s++]=String.fromCharCode(56320+(1023&a))}else{const a=e[i++],_=e[i++];r[s++]=String.fromCharCode((15&o)<<12|(63&a)<<6|63&_)}}return r.join("")}(this.decodeStringToByteArray(e,r))},decodeStringToByteArray(e,r){this.init_();const i=r?this.charToByteMapWebSafe_:this.charToByteMap_,s=[];for(let r=0;r<e.length;){const o=i[e.charAt(r++)],a=r<e.length?i[e.charAt(r)]:0;++r;const _=r<e.length?i[e.charAt(r)]:64;++r;const h=r<e.length?i[e.charAt(r)]:64;if(++r,null==o||null==a||null==_||null==h)throw new DecodeBase64StringError;const d=o<<2|a>>4;if(s.push(d),64!==_){const e=a<<4&240|_>>2;if(s.push(e),64!==h){const e=_<<6&192|h;s.push(e)}}}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class DecodeBase64StringError extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const base64urlEncodeWithoutPadding=function(e){return function(e){const r=stringToByteArray$1(e);return _.encodeByteArray(r,!0)}(e).replace(/\./g,"")};const getDefaultsFromGlobal=()=>function getGlobal(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,getDefaultsFromCookie=()=>{if("undefined"==typeof document)return;let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}const r=e&&function(e){try{return _.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null}(e[1]);return r&&JSON.parse(r)},getDefaults=()=>{try{return getDefaultsFromGlobal()||(()=>{if("undefined"==typeof process||void 0===process.env)return;const e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0})()||getDefaultsFromCookie()}catch(e){return void console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`)}},getDefaultEmulatorHostnameAndPort=e=>{const r=(e=>{var r,i;return null===(i=null===(r=getDefaults())||void 0===r?void 0:r.emulatorHosts)||void 0===i?void 0:i[e]})(e);if(!r)return;const i=r.lastIndexOf(":");if(i<=0||i+1===r.length)throw new Error(`Invalid host ${r} with no separate hostname and port!`);const s=parseInt(r.substring(i+1),10);return"["===r[0]?[r.substring(1,i-1),s]:[r.substring(0,i),s]};function isCloudWorkstation(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch(e){return!1}}const h={};let d=!1;function updateEmulatorBanner(e,r){if("undefined"==typeof window||"undefined"==typeof document||!isCloudWorkstation(window.location.host)||h[e]===r||h[e]||d)return;function prefixedId(e){return`__firebase__banner__${e}`}h[e]=r;const i="__firebase__banner",s=function getEmulatorSummary(){const e={prod:[],emulator:[]};for(const r of Object.keys(h))h[r]?e.emulator.push(r):e.prod.push(r);return e}().prod.length>0;function setupCloseBtn(){const e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{d=!0,function tearDown(){const e=document.getElementById(i);e&&e.remove()}()},e}function setupDom(){const e=function getOrCreateEl(e){let r=document.getElementById(e),i=!1;return r||(r=document.createElement("div"),r.setAttribute("id",e),i=!0),{created:i,element:r}}(i),r=prefixedId("text"),o=document.getElementById(r)||document.createElement("span"),a=prefixedId("learnmore"),_=document.getElementById(a)||document.createElement("a"),h=prefixedId("preprendIcon"),d=document.getElementById(h)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(e.created){const r=e.element;!function setupBannerStyles(e){e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center"}(r),function setupLinkStyles(e,r){e.setAttribute("id",r),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline"}(_,a);const i=setupCloseBtn();!function setupIconStyles(e,r){e.setAttribute("width","24"),e.setAttribute("id",r),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px"}(d,h),r.append(d,o,_,i),document.body.appendChild(r)}s?(o.innerText="Preview backend disconnected.",d.innerHTML='<g clip-path="url(#clip0_6013_33858)">\n<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6013_33858">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>'):(d.innerHTML='<g clip-path="url(#clip0_6083_34804)">\n<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6083_34804">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>',o.innerText="Preview backend running in this workspace."),o.setAttribute("id",r)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",setupDom):setupDom()}class FirebaseError extends Error{constructor(e,r,i){super(r),this.code=e,this.customData=i,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,r,i){this.service=e,this.serviceName=r,this.errors=i}create(e,...r){const i=r[0]||{},s=`${this.service}/${e}`,o=this.errors[e],a=o?function replaceTemplate(e,r){return e.replace(f,((e,i)=>{const s=r[i];return null!=s?String(s):`<${i}?>`}))}(o,i):"Error",_=`${this.serviceName}: ${a} (${s}).`;return new FirebaseError(s,_,i)}}const f=/\{\$([^}]+)}/g;function deepEqual(e,r){if(e===r)return!0;const i=Object.keys(e),s=Object.keys(r);for(const o of i){if(!s.includes(o))return!1;const i=e[o],a=r[o];if(isObject(i)&&isObject(a)){if(!deepEqual(i,a))return!1}else if(i!==a)return!1}for(const e of s)if(!i.includes(e))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}function getModularInstance(e){return e&&e._delegate?e._delegate:e}class Component{constructor(e,r,i){this.name=e,this.instanceFactory=r,this.type=i,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var g;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(g||(g={}));const E={debug:g.DEBUG,verbose:g.VERBOSE,info:g.INFO,warn:g.WARN,error:g.ERROR,silent:g.SILENT},T=g.INFO,A={[g.DEBUG]:"log",[g.VERBOSE]:"log",[g.INFO]:"info",[g.WARN]:"warn",[g.ERROR]:"error"},defaultLogHandler=(e,r,...i)=>{if(r<e.logLevel)return;const s=(new Date).toISOString(),o=A[r];if(!o)throw new Error(`Attempted to log a message with an invalid logType (value: ${r})`);console[o](`[${s}]  ${e.name}:`,...i)};var V,P="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};(function(){var e;function m(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(e,r,i){i||(i=0);var s=Array(16);if("string"==typeof r)for(var o=0;16>o;++o)s[o]=r.charCodeAt(i++)|r.charCodeAt(i++)<<8|r.charCodeAt(i++)<<16|r.charCodeAt(i++)<<24;else for(o=0;16>o;++o)s[o]=r[i++]|r[i++]<<8|r[i++]<<16|r[i++]<<24;r=e.g[0],i=e.g[1],o=e.g[2];var a=e.g[3],_=r+(a^i&(o^a))+s[0]+3614090360&4294967295;_=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=(i=(o=(a=(r=i+(_<<7&4294967295|_>>>25))+((_=a+(o^r&(i^o))+s[1]+3905402710&4294967295)<<12&4294967295|_>>>20))+((_=o+(i^a&(r^i))+s[2]+606105819&4294967295)<<17&4294967295|_>>>15))+((_=i+(r^o&(a^r))+s[3]+3250441966&4294967295)<<22&4294967295|_>>>10))+((_=r+(a^i&(o^a))+s[4]+4118548399&4294967295)<<7&4294967295|_>>>25))+((_=a+(o^r&(i^o))+s[5]+1200080426&4294967295)<<12&4294967295|_>>>20))+((_=o+(i^a&(r^i))+s[6]+2821735955&4294967295)<<17&4294967295|_>>>15))+((_=i+(r^o&(a^r))+s[7]+4249261313&4294967295)<<22&4294967295|_>>>10))+((_=r+(a^i&(o^a))+s[8]+1770035416&4294967295)<<7&4294967295|_>>>25))+((_=a+(o^r&(i^o))+s[9]+2336552879&4294967295)<<12&4294967295|_>>>20))+((_=o+(i^a&(r^i))+s[10]+4294925233&4294967295)<<17&4294967295|_>>>15))+((_=i+(r^o&(a^r))+s[11]+2304563134&4294967295)<<22&4294967295|_>>>10))+((_=r+(a^i&(o^a))+s[12]+1804603682&4294967295)<<7&4294967295|_>>>25))+((_=a+(o^r&(i^o))+s[13]+4254626195&4294967295)<<12&4294967295|_>>>20))+((_=o+(i^a&(r^i))+s[14]+2792965006&4294967295)<<17&4294967295|_>>>15))+((_=i+(r^o&(a^r))+s[15]+1236535329&4294967295)<<22&4294967295|_>>>10))+((_=r+(o^a&(i^o))+s[1]+4129170786&4294967295)<<5&4294967295|_>>>27))+((_=a+(i^o&(r^i))+s[6]+3225465664&4294967295)<<9&4294967295|_>>>23))+((_=o+(r^i&(a^r))+s[11]+643717713&4294967295)<<14&4294967295|_>>>18))+((_=i+(a^r&(o^a))+s[0]+3921069994&4294967295)<<20&4294967295|_>>>12))+((_=r+(o^a&(i^o))+s[5]+3593408605&4294967295)<<5&4294967295|_>>>27))+((_=a+(i^o&(r^i))+s[10]+38016083&4294967295)<<9&4294967295|_>>>23))+((_=o+(r^i&(a^r))+s[15]+3634488961&4294967295)<<14&4294967295|_>>>18))+((_=i+(a^r&(o^a))+s[4]+3889429448&4294967295)<<20&4294967295|_>>>12))+((_=r+(o^a&(i^o))+s[9]+568446438&4294967295)<<5&4294967295|_>>>27))+((_=a+(i^o&(r^i))+s[14]+3275163606&4294967295)<<9&4294967295|_>>>23))+((_=o+(r^i&(a^r))+s[3]+4107603335&4294967295)<<14&4294967295|_>>>18))+((_=i+(a^r&(o^a))+s[8]+1163531501&4294967295)<<20&4294967295|_>>>12))+((_=r+(o^a&(i^o))+s[13]+2850285829&4294967295)<<5&4294967295|_>>>27))+((_=a+(i^o&(r^i))+s[2]+4243563512&4294967295)<<9&4294967295|_>>>23))+((_=o+(r^i&(a^r))+s[7]+1735328473&4294967295)<<14&4294967295|_>>>18))+((_=i+(a^r&(o^a))+s[12]+2368359562&4294967295)<<20&4294967295|_>>>12))+((_=r+(i^o^a)+s[5]+4294588738&4294967295)<<4&4294967295|_>>>28))+((_=a+(r^i^o)+s[8]+2272392833&4294967295)<<11&4294967295|_>>>21))+((_=o+(a^r^i)+s[11]+1839030562&4294967295)<<16&4294967295|_>>>16))+((_=i+(o^a^r)+s[14]+4259657740&4294967295)<<23&4294967295|_>>>9))+((_=r+(i^o^a)+s[1]+2763975236&4294967295)<<4&4294967295|_>>>28))+((_=a+(r^i^o)+s[4]+1272893353&4294967295)<<11&4294967295|_>>>21))+((_=o+(a^r^i)+s[7]+4139469664&4294967295)<<16&4294967295|_>>>16))+((_=i+(o^a^r)+s[10]+3200236656&4294967295)<<23&4294967295|_>>>9))+((_=r+(i^o^a)+s[13]+681279174&4294967295)<<4&4294967295|_>>>28))+((_=a+(r^i^o)+s[0]+3936430074&4294967295)<<11&4294967295|_>>>21))+((_=o+(a^r^i)+s[3]+3572445317&4294967295)<<16&4294967295|_>>>16))+((_=i+(o^a^r)+s[6]+76029189&4294967295)<<23&4294967295|_>>>9))+((_=r+(i^o^a)+s[9]+3654602809&4294967295)<<4&4294967295|_>>>28))+((_=a+(r^i^o)+s[12]+3873151461&4294967295)<<11&4294967295|_>>>21))+((_=o+(a^r^i)+s[15]+530742520&4294967295)<<16&4294967295|_>>>16))+((_=i+(o^a^r)+s[2]+3299628645&4294967295)<<23&4294967295|_>>>9))+((_=r+(o^(i|~a))+s[0]+4096336452&4294967295)<<6&4294967295|_>>>26))+((_=a+(i^(r|~o))+s[7]+1126891415&4294967295)<<10&4294967295|_>>>22))+((_=o+(r^(a|~i))+s[14]+2878612391&4294967295)<<15&4294967295|_>>>17))+((_=i+(a^(o|~r))+s[5]+4237533241&4294967295)<<21&4294967295|_>>>11))+((_=r+(o^(i|~a))+s[12]+1700485571&4294967295)<<6&4294967295|_>>>26))+((_=a+(i^(r|~o))+s[3]+2399980690&4294967295)<<10&4294967295|_>>>22))+((_=o+(r^(a|~i))+s[10]+4293915773&4294967295)<<15&4294967295|_>>>17))+((_=i+(a^(o|~r))+s[1]+2240044497&4294967295)<<21&4294967295|_>>>11))+((_=r+(o^(i|~a))+s[8]+1873313359&4294967295)<<6&4294967295|_>>>26))+((_=a+(i^(r|~o))+s[15]+4264355552&4294967295)<<10&4294967295|_>>>22))+((_=o+(r^(a|~i))+s[6]+2734768916&4294967295)<<15&4294967295|_>>>17))+((_=i+(a^(o|~r))+s[13]+1309151649&4294967295)<<21&4294967295|_>>>11))+((a=(r=i+((_=r+(o^(i|~a))+s[4]+4149444226&4294967295)<<6&4294967295|_>>>26))+((_=a+(i^(r|~o))+s[11]+3174756917&4294967295)<<10&4294967295|_>>>22))^((o=a+((_=o+(r^(a|~i))+s[2]+718787259&4294967295)<<15&4294967295|_>>>17))|~r))+s[9]+3951481745&4294967295,e.g[0]=e.g[0]+r&4294967295,e.g[1]=e.g[1]+(o+(_<<21&4294967295|_>>>11))&4294967295,e.g[2]=e.g[2]+o&4294967295,e.g[3]=e.g[3]+a&4294967295}function t(e,r){this.h=r;for(var i=[],s=!0,o=e.length-1;0<=o;o--){var a=0|e[o];s&&a==r||(i[o]=a,s=!1)}this.g=i}!function k(e,r){function c(){}c.prototype=r.prototype,e.D=r.prototype,e.prototype=new c,e.prototype.constructor=e,e.C=function(e,i,s){for(var o=Array(arguments.length-2),a=2;a<arguments.length;a++)o[a-2]=arguments[a];return r.prototype[i].apply(e,o)}}(m,(function l(){this.blockSize=-1})),m.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},m.prototype.u=function(e,r){void 0===r&&(r=e.length);for(var i=r-this.blockSize,s=this.B,o=this.h,a=0;a<r;){if(0==o)for(;a<=i;)n(this,e,a),a+=this.blockSize;if("string"==typeof e){for(;a<r;)if(s[o++]=e.charCodeAt(a++),o==this.blockSize){n(this,s),o=0;break}}else for(;a<r;)if(s[o++]=e[a++],o==this.blockSize){n(this,s),o=0;break}}this.h=o,this.o+=r},m.prototype.v=function(){var e=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var r=1;r<e.length-8;++r)e[r]=0;var i=8*this.o;for(r=e.length-8;r<e.length;++r)e[r]=255&i,i/=256;for(this.u(e),e=Array(16),r=i=0;4>r;++r)for(var s=0;32>s;s+=8)e[i++]=this.g[r]>>>s&255;return e};var r={};function u(e){return-128<=e&&128>e?function p(e,i){var s=r;return Object.prototype.hasOwnProperty.call(s,e)?s[e]:s[e]=i(e)}(e,(function(e){return new t([0|e],0>e?-1:0)})):new t([0|e],0>e?-1:0)}function v(e){if(isNaN(e)||!isFinite(e))return i;if(0>e)return x(v(-e));for(var r=[],s=1,o=0;e>=s;o++)r[o]=e/s|0,s*=4294967296;return new t(r,0)}var i=u(0),s=u(1),o=u(16777216);function C(e){if(0!=e.h)return!1;for(var r=0;r<e.g.length;r++)if(0!=e.g[r])return!1;return!0}function B(e){return-1==e.h}function x(e){for(var r=e.g.length,i=[],o=0;o<r;o++)i[o]=~e.g[o];return new t(i,~e.h).add(s)}function F(e,r){return e.add(x(r))}function G(e,r){for(;(65535&e[r])!=e[r];)e[r+1]+=e[r]>>>16,e[r]&=65535,r++}function H(e,r){this.g=e,this.h=r}function D(e,r){if(C(r))throw Error("division by zero");if(C(e))return new H(i,i);if(B(e))return r=D(x(e),r),new H(x(r.g),x(r.h));if(B(r))return r=D(e,x(r)),new H(x(r.g),r.h);if(30<e.g.length){if(B(e)||B(r))throw Error("slowDivide_ only works with positive integers.");for(var o=s,a=r;0>=a.l(e);)o=I(o),a=I(a);var _=J(o,1),h=J(a,1);for(a=J(a,2),o=J(o,2);!C(a);){var d=h.add(a);0>=d.l(e)&&(_=_.add(o),h=d),a=J(a,1),o=J(o,1)}return r=F(e,_.j(r)),new H(_,r)}for(_=i;0<=e.l(r);){for(o=Math.max(1,Math.floor(e.m()/r.m())),a=48>=(a=Math.ceil(Math.log(o)/Math.LN2))?1:Math.pow(2,a-48),d=(h=v(o)).j(r);B(d)||0<d.l(e);)d=(h=v(o-=a)).j(r);C(h)&&(h=s),_=_.add(h),e=F(e,d)}return new H(_,e)}function I(e){for(var r=e.g.length+1,i=[],s=0;s<r;s++)i[s]=e.i(s)<<1|e.i(s-1)>>>31;return new t(i,e.h)}function J(e,r){var i=r>>5;r%=32;for(var s=e.g.length-i,o=[],a=0;a<s;a++)o[a]=0<r?e.i(a+i)>>>r|e.i(a+i+1)<<32-r:e.i(a+i);return new t(o,e.h)}(e=t.prototype).m=function(){if(B(this))return-x(this).m();for(var e=0,r=1,i=0;i<this.g.length;i++){var s=this.i(i);e+=(0<=s?s:4294967296+s)*r,r*=4294967296}return e},e.toString=function(e){if(2>(e=e||10)||36<e)throw Error("radix out of range: "+e);if(C(this))return"0";if(B(this))return"-"+x(this).toString(e);for(var r=v(Math.pow(e,6)),i=this,s="";;){var o=D(i,r).g,a=((0<(i=F(i,o.j(r))).g.length?i.g[0]:i.h)>>>0).toString(e);if(C(i=o))return a+s;for(;6>a.length;)a="0"+a;s=a+s}},e.i=function(e){return 0>e?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return B(e=F(this,e))?-1:C(e)?0:1},e.abs=function(){return B(this)?x(this):this},e.add=function(e){for(var r=Math.max(this.g.length,e.g.length),i=[],s=0,o=0;o<=r;o++){var a=s+(65535&this.i(o))+(65535&e.i(o)),_=(a>>>16)+(this.i(o)>>>16)+(e.i(o)>>>16);s=_>>>16,a&=65535,_&=65535,i[o]=_<<16|a}return new t(i,-2147483648&i[i.length-1]?-1:0)},e.j=function(e){if(C(this)||C(e))return i;if(B(this))return B(e)?x(this).j(x(e)):x(x(this).j(e));if(B(e))return x(this.j(x(e)));if(0>this.l(o)&&0>e.l(o))return v(this.m()*e.m());for(var r=this.g.length+e.g.length,s=[],a=0;a<2*r;a++)s[a]=0;for(a=0;a<this.g.length;a++)for(var _=0;_<e.g.length;_++){var h=this.i(a)>>>16,d=65535&this.i(a),f=e.i(_)>>>16,g=65535&e.i(_);s[2*a+2*_]+=d*g,G(s,2*a+2*_),s[2*a+2*_+1]+=h*g,G(s,2*a+2*_+1),s[2*a+2*_+1]+=d*f,G(s,2*a+2*_+1),s[2*a+2*_+2]+=h*f,G(s,2*a+2*_+2)}for(a=0;a<r;a++)s[a]=s[2*a+1]<<16|s[2*a];for(a=r;a<2*r;a++)s[a]=0;return new t(s,0)},e.A=function(e){return D(this,e).h},e.and=function(e){for(var r=Math.max(this.g.length,e.g.length),i=[],s=0;s<r;s++)i[s]=this.i(s)&e.i(s);return new t(i,this.h&e.h)},e.or=function(e){for(var r=Math.max(this.g.length,e.g.length),i=[],s=0;s<r;s++)i[s]=this.i(s)|e.i(s);return new t(i,this.h|e.h)},e.xor=function(e){for(var r=Math.max(this.g.length,e.g.length),i=[],s=0;s<r;s++)i[s]=this.i(s)^e.i(s);return new t(i,this.h^e.h)},m.prototype.digest=m.prototype.v,m.prototype.reset=m.prototype.s,m.prototype.update=m.prototype.u,t.prototype.add=t.prototype.add,t.prototype.multiply=t.prototype.j,t.prototype.modulo=t.prototype.A,t.prototype.compare=t.prototype.l,t.prototype.toNumber=t.prototype.m,t.prototype.toString=t.prototype.toString,t.prototype.getBits=t.prototype.i,t.fromNumber=v,t.fromString=function y(e,r){if(0==e.length)throw Error("number format error: empty string");if(2>(r=r||10)||36<r)throw Error("radix out of range: "+r);if("-"==e.charAt(0))return x(y(e.substring(1),r));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var s=v(Math.pow(r,8)),o=i,a=0;a<e.length;a+=8){var _=Math.min(8,e.length-a),h=parseInt(e.substring(a,a+_),r);8>_?(_=v(Math.pow(r,_)),o=o.j(_).add(v(h))):o=(o=o.j(s)).add(v(h))}return o},V=t}).apply(void 0!==P?P:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});const R="4.8.0";class User{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}User.UNAUTHENTICATED=new User(null),User.GOOGLE_CREDENTIALS=new User("google-credentials-uid"),User.FIRST_PARTY=new User("first-party-uid"),User.MOCK_USER=new User("mock-user");let w="11.10.0";const S=new class Logger{constructor(e){this.name=e,this._logLevel=T,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in g))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?E[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,g.DEBUG,...e),this._logHandler(this,g.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,g.VERBOSE,...e),this._logHandler(this,g.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,g.INFO,...e),this._logHandler(this,g.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,g.WARN,...e),this._logHandler(this,g.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,g.ERROR,...e),this._logHandler(this,g.ERROR,...e)}}("@firebase/firestore");function setLogLevel(e){S.setLogLevel(e)}function __PRIVATE_logDebug(e,...r){if(S.logLevel<=g.DEBUG){const i=r.map(__PRIVATE_argToString);S.debug(`Firestore (${w}): ${e}`,...i)}}function __PRIVATE_logError(e,...r){if(S.logLevel<=g.ERROR){const i=r.map(__PRIVATE_argToString);S.error(`Firestore (${w}): ${e}`,...i)}}function __PRIVATE_logWarn(e,...r){if(S.logLevel<=g.WARN){const i=r.map(__PRIVATE_argToString);S.warn(`Firestore (${w}): ${e}`,...i)}}function __PRIVATE_argToString(e){if("string"==typeof e)return e;try{return function __PRIVATE_formatJSON(e){return JSON.stringify(e)}(e)}catch(r){return e}}function fail(e,r,i){let s="Unexpected state";"string"==typeof r?s=r:i=r,__PRIVATE__fail(e,s,i)}function __PRIVATE__fail(e,r,i){let s=`FIRESTORE (${w}) INTERNAL ASSERTION FAILED: ${r} (ID: ${e.toString(16)})`;if(void 0!==i)try{s+=" CONTEXT: "+JSON.stringify(i)}catch(e){s+=" CONTEXT: "+i}throw __PRIVATE_logError(s),new Error(s)}function __PRIVATE_hardAssert(e,r,i,s){let o="Unexpected state";"string"==typeof i?o=i:s=i,e||__PRIVATE__fail(r,o,s)}function __PRIVATE_debugCast(e,r){return e}const b="ok",N="cancelled",O="unknown",L="invalid-argument",M="deadline-exceeded",q="not-found",U="permission-denied",j="unauthenticated",$="resource-exhausted",Q="failed-precondition",z="aborted",W="out-of-range",K="unimplemented",Y="internal",Z="unavailable";class FirestoreError extends FirebaseError{constructor(e,r){super(e,r),this.code=e,this.message=r,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class __PRIVATE_Deferred{constructor(){this.promise=new Promise(((e,r)=>{this.resolve=e,this.reject=r}))}}class __PRIVATE_OAuthToken{constructor(e,r){this.user=r,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class __PRIVATE_EmptyAuthCredentialsProvider{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,r){e.enqueueRetryable((()=>r(User.UNAUTHENTICATED)))}shutdown(){}}class __PRIVATE_EmulatorAuthCredentialsProvider{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,r){this.changeListener=r,e.enqueueRetryable((()=>r(this.token.user)))}shutdown(){this.changeListener=null}}class __PRIVATE_LiteAuthCredentialsProvider{constructor(e){this.auth=null,e.onInit((e=>{this.auth=e}))}getToken(){return this.auth?this.auth.getToken().then((e=>e?(__PRIVATE_hardAssert("string"==typeof e.accessToken,42297,{t:e}),new __PRIVATE_OAuthToken(e.accessToken,new User(this.auth.getUid()))):null)):Promise.resolve(null)}invalidateToken(){}start(e,r){}shutdown(){}}class __PRIVATE_FirstPartyToken{constructor(e,r,i){this.i=e,this.o=r,this.u=i,this.type="FirstParty",this.user=User.FIRST_PARTY,this.l=new Map}h(){return this.u?this.u():null}get headers(){this.l.set("X-Goog-AuthUser",this.i);const e=this.h();return e&&this.l.set("Authorization",e),this.o&&this.l.set("X-Goog-Iam-Authorization-Token",this.o),this.l}}class __PRIVATE_FirstPartyAuthCredentialsProvider{constructor(e,r,i){this.i=e,this.o=r,this.u=i}getToken(){return Promise.resolve(new __PRIVATE_FirstPartyToken(this.i,this.o,this.u))}start(e,r){e.enqueueRetryable((()=>r(User.FIRST_PARTY)))}shutdown(){}invalidateToken(){}}class AppCheckToken{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class __PRIVATE_LiteAppCheckTokenProvider{constructor(e,r){this.m=r,this.appCheck=null,this.T=null,i(e)&&e.settings.appCheckToken&&(this.T=e.settings.appCheckToken),r.onInit((e=>{this.appCheck=e}))}getToken(){return this.T?Promise.resolve(new AppCheckToken(this.T)):this.appCheck?this.appCheck.getToken().then((e=>e?(__PRIVATE_hardAssert("string"==typeof e.token,3470,{tokenResult:e}),new AppCheckToken(e.token)):null)):Promise.resolve(null)}invalidateToken(){}start(e,r){}shutdown(){}}class DatabaseInfo{constructor(e,r,i,s,o,a,_,h,d,f){this.databaseId=e,this.appId=r,this.persistenceKey=i,this.host=s,this.ssl=o,this.forceLongPolling=a,this.autoDetectLongPolling=_,this.longPollingOptions=h,this.useFetchStreams=d,this.isUsingEmulator=f}}const X="(default)";class DatabaseId{constructor(e,r){this.projectId=e,this.database=r||X}static empty(){return new DatabaseId("","")}get isDefaultDatabase(){return this.database===X}isEqual(e){return e instanceof DatabaseId&&e.projectId===this.projectId&&e.database===this.database}}function __PRIVATE_randomBytes(e){const r="undefined"!=typeof self&&(self.crypto||self.msCrypto),i=new Uint8Array(e);if(r&&"function"==typeof r.getRandomValues)r.getRandomValues(i);else for(let r=0;r<e;r++)i[r]=Math.floor(256*Math.random());return i}class __PRIVATE_AutoId{static newId(){const e=62*Math.floor(256/62);let r="";for(;r.length<20;){const i=__PRIVATE_randomBytes(40);for(let s=0;s<i.length;++s)r.length<20&&i[s]<e&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(i[s]%62))}return r}}function __PRIVATE_primitiveComparator(e,r){return e<r?-1:e>r?1:0}function __PRIVATE_compareUtf8Strings(e,r){let i=0;for(;i<e.length&&i<r.length;){const s=e.codePointAt(i),o=r.codePointAt(i);if(s!==o){if(s<128&&o<128)return __PRIVATE_primitiveComparator(s,o);{const a=new TextEncoder,_=__PRIVATE_compareByteArrays(a.encode(__PRIVATE_getUtf8SafeSubstring(e,i)),a.encode(__PRIVATE_getUtf8SafeSubstring(r,i)));return 0!==_?_:__PRIVATE_primitiveComparator(s,o)}}i+=s>65535?2:1}return __PRIVATE_primitiveComparator(e.length,r.length)}function __PRIVATE_getUtf8SafeSubstring(e,r){return e.codePointAt(r)>65535?e.substring(r,r+2):e.substring(r,r+1)}function __PRIVATE_compareByteArrays(e,r){for(let i=0;i<e.length&&i<r.length;++i)if(e[i]!==r[i])return __PRIVATE_primitiveComparator(e[i],r[i]);return __PRIVATE_primitiveComparator(e.length,r.length)}function __PRIVATE_arrayEquals(e,r,i){return e.length===r.length&&e.every(((e,s)=>i(e,r[s])))}const ee="__name__";class BasePath{constructor(e,r,i){void 0===r?r=0:r>e.length&&fail(637,{offset:r,range:e.length}),void 0===i?i=e.length-r:i>e.length-r&&fail(1746,{length:i,range:e.length-r}),this.segments=e,this.offset=r,this.len=i}get length(){return this.len}isEqual(e){return 0===BasePath.comparator(this,e)}child(e){const r=this.segments.slice(this.offset,this.limit());return e instanceof BasePath?e.forEach((e=>{r.push(e)})):r.push(e),this.construct(r)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let r=0;r<this.length;r++)if(this.get(r)!==e.get(r))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let r=0;r<this.length;r++)if(this.get(r)!==e.get(r))return!1;return!0}forEach(e){for(let r=this.offset,i=this.limit();r<i;r++)e(this.segments[r])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,r){const i=Math.min(e.length,r.length);for(let s=0;s<i;s++){const i=BasePath.compareSegments(e.get(s),r.get(s));if(0!==i)return i}return __PRIVATE_primitiveComparator(e.length,r.length)}static compareSegments(e,r){const i=BasePath.isNumericId(e),s=BasePath.isNumericId(r);return i&&!s?-1:!i&&s?1:i&&s?BasePath.extractNumericId(e).compare(BasePath.extractNumericId(r)):__PRIVATE_compareUtf8Strings(e,r)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return V.fromString(e.substring(4,e.length-2))}}class ResourcePath extends BasePath{construct(e,r,i){return new ResourcePath(e,r,i)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){const r=[];for(const i of e){if(i.indexOf("//")>=0)throw new FirestoreError(L,`Invalid segment (${i}). Paths must not contain // in them.`);r.push(...i.split("/").filter((e=>e.length>0)))}return new ResourcePath(r)}static emptyPath(){return new ResourcePath([])}}const te=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class FieldPath$1 extends BasePath{construct(e,r,i){return new FieldPath$1(e,r,i)}static isValidIdentifier(e){return te.test(e)}canonicalString(){return this.toArray().map((e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),FieldPath$1.isValidIdentifier(e)||(e="`"+e+"`"),e))).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===ee}static keyField(){return new FieldPath$1([ee])}static fromServerFormat(e){const r=[];let i="",s=0;const __PRIVATE_addCurrentSegment=()=>{if(0===i.length)throw new FirestoreError(L,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);r.push(i),i=""};let o=!1;for(;s<e.length;){const r=e[s];if("\\"===r){if(s+1===e.length)throw new FirestoreError(L,"Path has trailing escape character: "+e);const r=e[s+1];if("\\"!==r&&"."!==r&&"`"!==r)throw new FirestoreError(L,"Path has invalid escape sequence: "+e);i+=r,s+=2}else"`"===r?(o=!o,s++):"."!==r||o?(i+=r,s++):(__PRIVATE_addCurrentSegment(),s++)}if(__PRIVATE_addCurrentSegment(),o)throw new FirestoreError(L,"Unterminated ` in path: "+e);return new FieldPath$1(r)}static emptyPath(){return new FieldPath$1([])}}class DocumentKey{constructor(e){this.path=e}static fromPath(e){return new DocumentKey(ResourcePath.fromString(e))}static fromName(e){return new DocumentKey(ResourcePath.fromString(e).popFirst(5))}static empty(){return new DocumentKey(ResourcePath.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===ResourcePath.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,r){return ResourcePath.comparator(e.path,r.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new DocumentKey(new ResourcePath(e.slice()))}}function __PRIVATE_validateNonEmptyArgument(e,r,i){if(!i)throw new FirestoreError(L,`Function ${e}() cannot be called with an empty ${r}.`)}function __PRIVATE_validateDocumentPath(e){if(!DocumentKey.isDocumentKey(e))throw new FirestoreError(L,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function __PRIVATE_validateCollectionPath(e){if(DocumentKey.isDocumentKey(e))throw new FirestoreError(L,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function __PRIVATE_isPlainObject(e){return"object"==typeof e&&null!==e&&(Object.getPrototypeOf(e)===Object.prototype||null===Object.getPrototypeOf(e))}function __PRIVATE_valueDescription(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{const r=function __PRIVATE_tryGetCustomObjectType(e){return e.constructor?e.constructor.name:null}(e);return r?`a custom ${r} object`:"an object"}}return"function"==typeof e?"a function":fail(12329,{type:typeof e})}function __PRIVATE_cast(e,r){if("_delegate"in e&&(e=e._delegate),!(e instanceof r)){if(r.name===e.constructor.name)throw new FirestoreError(L,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{const i=__PRIVATE_valueDescription(e);throw new FirestoreError(L,`Expected type '${r.name}', but it was: ${i}`)}}return e}function __PRIVATE_validatePositiveNumber(e,r){if(r<=0)throw new FirestoreError(L,`Function ${e}() requires a positive number, but it was: ${r}.`)}function __PRIVATE_cloneLongPollingOptions(e){const r={};return void 0!==e.timeoutSeconds&&(r.timeoutSeconds=e.timeoutSeconds),r}let re=null;function __PRIVATE_isNullOrUndefined(e){return null==e}function __PRIVATE_isNegativeZero(e){return 0===e&&1/e==-1/0}const ne="RestConnection",ie={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class __PRIVATE_RestConnection{get P(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;const r=e.ssl?"https":"http",i=encodeURIComponent(this.databaseId.projectId),s=encodeURIComponent(this.databaseId.database);this.A=r+"://"+e.host,this.R=`projects/${i}/databases/${s}`,this.V=this.databaseId.database===X?`project_id=${i}`:`project_id=${i}&database_id=${s}`}I(e,r,i,s,o){const a=function __PRIVATE_generateUniqueDebugId(){return null===re?re=function __PRIVATE_generateInitialUniqueDebugId(){return 268435456+Math.round(2147483648*Math.random())}():re++,"0x"+re.toString(16)}(),_=this.p(e,r.toUriEncodedString());__PRIVATE_logDebug(ne,`Sending RPC '${e}' ${a}:`,_,i);const h={"google-cloud-resource-prefix":this.R,"x-goog-request-params":this.V};this.v(h,s,o);const{host:d}=new URL(_),f=isCloudWorkstation(d);return this.F(e,_,h,i,f).then((r=>(__PRIVATE_logDebug(ne,`Received RPC '${e}' ${a}: `,r),r)),(r=>{throw __PRIVATE_logWarn(ne,`RPC '${e}' ${a} failed with error: `,r,"url: ",_,"request:",i),r}))}D(e,r,i,s,o,a){return this.I(e,r,i,s,o)}v(e,r,i){e["X-Goog-Api-Client"]=function __PRIVATE_getGoogApiClientValue(){return"gl-js/ fire/"+w}(),e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),r&&r.headers.forEach(((r,i)=>e[i]=r)),i&&i.headers.forEach(((r,i)=>e[i]=r))}p(e,r){const i=ie[e];return`${this.A}/v1/${r}:${i}`}terminate(){}}var se,oe;function __PRIVATE_mapCodeFromHttpStatus(e){if(void 0===e)return __PRIVATE_logError("RPC_ERROR","HTTP error has no status"),O;switch(e){case 200:return b;case 400:return Q;case 401:return j;case 403:return U;case 404:return q;case 409:return z;case 416:return W;case 429:return $;case 499:return N;case 500:return O;case 501:return K;case 503:return Z;case 504:return M;default:return e>=200&&e<300?b:e>=400&&e<500?Q:e>=500&&e<600?Y:O}}(oe=se||(se={}))[oe.OK=0]="OK",oe[oe.CANCELLED=1]="CANCELLED",oe[oe.UNKNOWN=2]="UNKNOWN",oe[oe.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",oe[oe.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",oe[oe.NOT_FOUND=5]="NOT_FOUND",oe[oe.ALREADY_EXISTS=6]="ALREADY_EXISTS",oe[oe.PERMISSION_DENIED=7]="PERMISSION_DENIED",oe[oe.UNAUTHENTICATED=16]="UNAUTHENTICATED",oe[oe.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",oe[oe.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",oe[oe.ABORTED=10]="ABORTED",oe[oe.OUT_OF_RANGE=11]="OUT_OF_RANGE",oe[oe.UNIMPLEMENTED=12]="UNIMPLEMENTED",oe[oe.INTERNAL=13]="INTERNAL",oe[oe.UNAVAILABLE=14]="UNAVAILABLE",oe[oe.DATA_LOSS=15]="DATA_LOSS";class __PRIVATE_FetchConnection extends __PRIVATE_RestConnection{S(e,r){throw new Error("Not supported by FetchConnection")}async F(e,r,i,s,o){var a;const _=JSON.stringify(s);let h;try{const e={method:"POST",headers:i,body:_};o&&(e.credentials="include"),h=await fetch(r,e)}catch(e){const r=e;throw new FirestoreError(__PRIVATE_mapCodeFromHttpStatus(r.status),"Request failed with error: "+r.statusText)}if(!h.ok){let e=await h.json();Array.isArray(e)&&(e=e[0]);const r=null===(a=null==e?void 0:e.error)||void 0===a?void 0:a.message;throw new FirestoreError(__PRIVATE_mapCodeFromHttpStatus(h.status),`Request failed with error: ${null!=r?r:h.statusText}`)}return h.json()}}class __PRIVATE_AggregateImpl{constructor(e,r,i){this.alias=e,this.aggregateType=r,this.fieldPath=i}}function __PRIVATE_objectSize(e){let r=0;for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&r++;return r}function forEach(e,r){for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&r(i,e[i])}class __PRIVATE_Base64DecodeError extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class ByteString{constructor(e){this.binaryString=e}static fromBase64String(e){const r=function __PRIVATE_decodeBase64(e){try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new __PRIVATE_Base64DecodeError("Invalid base64 string: "+e):e}}(e);return new ByteString(r)}static fromUint8Array(e){const r=function __PRIVATE_binaryStringFromUint8Array(e){let r="";for(let i=0;i<e.length;++i)r+=String.fromCharCode(e[i]);return r}(e);return new ByteString(r)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return function __PRIVATE_encodeBase64(e){return btoa(e)}(this.binaryString)}toUint8Array(){return function __PRIVATE_uint8ArrayFromBinaryString(e){const r=new Uint8Array(e.length);for(let i=0;i<e.length;i++)r[i]=e.charCodeAt(i);return r}(this.binaryString)}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return __PRIVATE_primitiveComparator(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}ByteString.EMPTY_BYTE_STRING=new ByteString("");const ae=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function __PRIVATE_normalizeTimestamp(e){if(__PRIVATE_hardAssert(!!e,39018),"string"==typeof e){let r=0;const i=ae.exec(e);if(__PRIVATE_hardAssert(!!i,46558,{timestamp:e}),i[1]){let e=i[1];e=(e+"000000000").substr(0,9),r=Number(e)}const s=new Date(e);return{seconds:Math.floor(s.getTime()/1e3),nanos:r}}return{seconds:__PRIVATE_normalizeNumber(e.seconds),nanos:__PRIVATE_normalizeNumber(e.nanos)}}function __PRIVATE_normalizeNumber(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function __PRIVATE_normalizeByteString(e){return"string"==typeof e?ByteString.fromBase64String(e):ByteString.fromUint8Array(e)}function property(e,r){const i={typeString:e};return r&&(i.value=r),i}function __PRIVATE_validateJSON(e,r){if(!__PRIVATE_isPlainObject(e))throw new FirestoreError(L,"JSON must be an object");let i;for(const s in r)if(r[s]){const o=r[s].typeString,a="value"in r[s]?{value:r[s].value}:void 0;if(!(s in e)){i=`JSON missing required field: '${s}'`;break}const _=e[s];if(o&&typeof _!==o){i=`JSON field '${s}' must be a ${o}.`;break}if(void 0!==a&&_!==a.value){i=`Expected '${s}' field to equal '${a.value}'`;break}}if(i)throw new FirestoreError(L,i);return!0}const ue=-62135596800,le=1e6;class Timestamp{static now(){return Timestamp.fromMillis(Date.now())}static fromDate(e){return Timestamp.fromMillis(e.getTime())}static fromMillis(e){const r=Math.floor(e/1e3),i=Math.floor((e-1e3*r)*le);return new Timestamp(r,i)}constructor(e,r){if(this.seconds=e,this.nanoseconds=r,r<0)throw new FirestoreError(L,"Timestamp nanoseconds out of range: "+r);if(r>=1e9)throw new FirestoreError(L,"Timestamp nanoseconds out of range: "+r);if(e<ue)throw new FirestoreError(L,"Timestamp seconds out of range: "+e);if(e>=253402300800)throw new FirestoreError(L,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/le}_compareTo(e){return this.seconds===e.seconds?__PRIVATE_primitiveComparator(this.nanoseconds,e.nanoseconds):__PRIVATE_primitiveComparator(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{type:Timestamp._jsonSchemaVersion,seconds:this.seconds,nanoseconds:this.nanoseconds}}static fromJSON(e){if(__PRIVATE_validateJSON(e,Timestamp._jsonSchema))return new Timestamp(e.seconds,e.nanoseconds)}valueOf(){const e=this.seconds-ue;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}function __PRIVATE_isServerTimestamp(e){var r,i;return"server_timestamp"===(null===(i=((null===(r=null==e?void 0:e.mapValue)||void 0===r?void 0:r.fields)||{}).__type__)||void 0===i?void 0:i.stringValue)}function __PRIVATE_getPreviousValue(e){const r=e.mapValue.fields.__previous_value__;return __PRIVATE_isServerTimestamp(r)?__PRIVATE_getPreviousValue(r):r}function __PRIVATE_getLocalWriteTime(e){const r=__PRIVATE_normalizeTimestamp(e.mapValue.fields.__local_write_time__.timestampValue);return new Timestamp(r.seconds,r.nanos)}Timestamp._jsonSchemaVersion="firestore/timestamp/1.0",Timestamp._jsonSchema={type:property("string",Timestamp._jsonSchemaVersion),seconds:property("number"),nanoseconds:property("number")};const ce="__type__",_e="__max__",he={fields:{__type__:{stringValue:_e}}},de="__vector__",fe="value";function __PRIVATE_typeOrder(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?__PRIVATE_isServerTimestamp(e)?4:function __PRIVATE_isMaxValue(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===_e}(e)?9007199254740991:function __PRIVATE_isVectorValue(e){var r,i;return(null===(i=((null===(r=null==e?void 0:e.mapValue)||void 0===r?void 0:r.fields)||{})[ce])||void 0===i?void 0:i.stringValue)===de}(e)?10:11:fail(28295,{value:e})}function __PRIVATE_valueEquals(e,r){if(e===r)return!0;const i=__PRIVATE_typeOrder(e);if(i!==__PRIVATE_typeOrder(r))return!1;switch(i){case 0:case 9007199254740991:return!0;case 1:return e.booleanValue===r.booleanValue;case 4:return __PRIVATE_getLocalWriteTime(e).isEqual(__PRIVATE_getLocalWriteTime(r));case 3:return function __PRIVATE_timestampEquals(e,r){if("string"==typeof e.timestampValue&&"string"==typeof r.timestampValue&&e.timestampValue.length===r.timestampValue.length)return e.timestampValue===r.timestampValue;const i=__PRIVATE_normalizeTimestamp(e.timestampValue),s=__PRIVATE_normalizeTimestamp(r.timestampValue);return i.seconds===s.seconds&&i.nanos===s.nanos}(e,r);case 5:return e.stringValue===r.stringValue;case 6:return function __PRIVATE_blobEquals(e,r){return __PRIVATE_normalizeByteString(e.bytesValue).isEqual(__PRIVATE_normalizeByteString(r.bytesValue))}(e,r);case 7:return e.referenceValue===r.referenceValue;case 8:return function __PRIVATE_geoPointEquals(e,r){return __PRIVATE_normalizeNumber(e.geoPointValue.latitude)===__PRIVATE_normalizeNumber(r.geoPointValue.latitude)&&__PRIVATE_normalizeNumber(e.geoPointValue.longitude)===__PRIVATE_normalizeNumber(r.geoPointValue.longitude)}(e,r);case 2:return function __PRIVATE_numberEquals(e,r){if("integerValue"in e&&"integerValue"in r)return __PRIVATE_normalizeNumber(e.integerValue)===__PRIVATE_normalizeNumber(r.integerValue);if("doubleValue"in e&&"doubleValue"in r){const i=__PRIVATE_normalizeNumber(e.doubleValue),s=__PRIVATE_normalizeNumber(r.doubleValue);return i===s?__PRIVATE_isNegativeZero(i)===__PRIVATE_isNegativeZero(s):isNaN(i)&&isNaN(s)}return!1}(e,r);case 9:return __PRIVATE_arrayEquals(e.arrayValue.values||[],r.arrayValue.values||[],__PRIVATE_valueEquals);case 10:case 11:return function __PRIVATE_objectEquals(e,r){const i=e.mapValue.fields||{},s=r.mapValue.fields||{};if(__PRIVATE_objectSize(i)!==__PRIVATE_objectSize(s))return!1;for(const e in i)if(i.hasOwnProperty(e)&&(void 0===s[e]||!__PRIVATE_valueEquals(i[e],s[e])))return!1;return!0}(e,r);default:return fail(52216,{left:e})}}function __PRIVATE_arrayValueContains(e,r){return void 0!==(e.values||[]).find((e=>__PRIVATE_valueEquals(e,r)))}function __PRIVATE_valueCompare(e,r){if(e===r)return 0;const i=__PRIVATE_typeOrder(e),s=__PRIVATE_typeOrder(r);if(i!==s)return __PRIVATE_primitiveComparator(i,s);switch(i){case 0:case 9007199254740991:return 0;case 1:return __PRIVATE_primitiveComparator(e.booleanValue,r.booleanValue);case 2:return function __PRIVATE_compareNumbers(e,r){const i=__PRIVATE_normalizeNumber(e.integerValue||e.doubleValue),s=__PRIVATE_normalizeNumber(r.integerValue||r.doubleValue);return i<s?-1:i>s?1:i===s?0:isNaN(i)?isNaN(s)?0:-1:1}(e,r);case 3:return __PRIVATE_compareTimestamps(e.timestampValue,r.timestampValue);case 4:return __PRIVATE_compareTimestamps(__PRIVATE_getLocalWriteTime(e),__PRIVATE_getLocalWriteTime(r));case 5:return __PRIVATE_compareUtf8Strings(e.stringValue,r.stringValue);case 6:return function __PRIVATE_compareBlobs(e,r){const i=__PRIVATE_normalizeByteString(e),s=__PRIVATE_normalizeByteString(r);return i.compareTo(s)}(e.bytesValue,r.bytesValue);case 7:return function __PRIVATE_compareReferences(e,r){const i=e.split("/"),s=r.split("/");for(let e=0;e<i.length&&e<s.length;e++){const r=__PRIVATE_primitiveComparator(i[e],s[e]);if(0!==r)return r}return __PRIVATE_primitiveComparator(i.length,s.length)}(e.referenceValue,r.referenceValue);case 8:return function __PRIVATE_compareGeoPoints(e,r){const i=__PRIVATE_primitiveComparator(__PRIVATE_normalizeNumber(e.latitude),__PRIVATE_normalizeNumber(r.latitude));return 0!==i?i:__PRIVATE_primitiveComparator(__PRIVATE_normalizeNumber(e.longitude),__PRIVATE_normalizeNumber(r.longitude))}(e.geoPointValue,r.geoPointValue);case 9:return __PRIVATE_compareArrays(e.arrayValue,r.arrayValue);case 10:return function __PRIVATE_compareVectors(e,r){var i,s,o,a;const _=e.fields||{},h=r.fields||{},d=null===(i=_[fe])||void 0===i?void 0:i.arrayValue,f=null===(s=h[fe])||void 0===s?void 0:s.arrayValue,g=__PRIVATE_primitiveComparator((null===(o=null==d?void 0:d.values)||void 0===o?void 0:o.length)||0,(null===(a=null==f?void 0:f.values)||void 0===a?void 0:a.length)||0);return 0!==g?g:__PRIVATE_compareArrays(d,f)}(e.mapValue,r.mapValue);case 11:return function __PRIVATE_compareMaps(e,r){if(e===he&&r===he)return 0;if(e===he)return 1;if(r===he)return-1;const i=e.fields||{},s=Object.keys(i),o=r.fields||{},a=Object.keys(o);s.sort(),a.sort();for(let e=0;e<s.length&&e<a.length;++e){const r=__PRIVATE_compareUtf8Strings(s[e],a[e]);if(0!==r)return r;const _=__PRIVATE_valueCompare(i[s[e]],o[a[e]]);if(0!==_)return _}return __PRIVATE_primitiveComparator(s.length,a.length)}(e.mapValue,r.mapValue);default:throw fail(23264,{C:i})}}function __PRIVATE_compareTimestamps(e,r){if("string"==typeof e&&"string"==typeof r&&e.length===r.length)return __PRIVATE_primitiveComparator(e,r);const i=__PRIVATE_normalizeTimestamp(e),s=__PRIVATE_normalizeTimestamp(r),o=__PRIVATE_primitiveComparator(i.seconds,s.seconds);return 0!==o?o:__PRIVATE_primitiveComparator(i.nanos,s.nanos)}function __PRIVATE_compareArrays(e,r){const i=e.values||[],s=r.values||[];for(let e=0;e<i.length&&e<s.length;++e){const r=__PRIVATE_valueCompare(i[e],s[e]);if(r)return r}return __PRIVATE_primitiveComparator(i.length,s.length)}function __PRIVATE_refValue(e,r){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/${r.path.canonicalString()}`}}function isArray(e){return!!e&&"arrayValue"in e}function __PRIVATE_isNullValue(e){return!!e&&"nullValue"in e}function __PRIVATE_isNanValue(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function __PRIVATE_isMapValue(e){return!!e&&"mapValue"in e}function __PRIVATE_deepClone(e){if(e.geoPointValue)return{geoPointValue:Object.assign({},e.geoPointValue)};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:Object.assign({},e.timestampValue)};if(e.mapValue){const r={mapValue:{fields:{}}};return forEach(e.mapValue.fields,((e,i)=>r.mapValue.fields[e]=__PRIVATE_deepClone(i))),r}if(e.arrayValue){const r={arrayValue:{values:[]}};for(let i=0;i<(e.arrayValue.values||[]).length;++i)r.arrayValue.values[i]=__PRIVATE_deepClone(e.arrayValue.values[i]);return r}return Object.assign({},e)}class Bound{constructor(e,r){this.position=e,this.inclusive=r}}function __PRIVATE_boundEquals(e,r){if(null===e)return null===r;if(null===r)return!1;if(e.inclusive!==r.inclusive||e.position.length!==r.position.length)return!1;for(let i=0;i<e.position.length;i++)if(!__PRIVATE_valueEquals(e.position[i],r.position[i]))return!1;return!0}class Filter{}class FieldFilter extends Filter{constructor(e,r,i){super(),this.field=e,this.op=r,this.value=i}static create(e,r,i){return e.isKeyField()?"in"===r||"not-in"===r?this.createKeyFieldInFilter(e,r,i):new __PRIVATE_KeyFieldFilter(e,r,i):"array-contains"===r?new __PRIVATE_ArrayContainsFilter(e,i):"in"===r?new __PRIVATE_InFilter(e,i):"not-in"===r?new __PRIVATE_NotInFilter(e,i):"array-contains-any"===r?new __PRIVATE_ArrayContainsAnyFilter(e,i):new FieldFilter(e,r,i)}static createKeyFieldInFilter(e,r,i){return"in"===r?new __PRIVATE_KeyFieldInFilter(e,i):new __PRIVATE_KeyFieldNotInFilter(e,i)}matches(e){const r=e.data.field(this.field);return"!="===this.op?null!==r&&void 0===r.nullValue&&this.matchesComparison(__PRIVATE_valueCompare(r,this.value)):null!==r&&__PRIVATE_typeOrder(this.value)===__PRIVATE_typeOrder(r)&&this.matchesComparison(__PRIVATE_valueCompare(r,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return fail(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class CompositeFilter extends Filter{constructor(e,r){super(),this.filters=e,this.op=r,this.N=null}static create(e,r){return new CompositeFilter(e,r)}matches(e){return function __PRIVATE_compositeFilterIsConjunction(e){return"and"===e.op}(this)?void 0===this.filters.find((r=>!r.matches(e))):void 0!==this.filters.find((r=>r.matches(e)))}getFlattenedFilters(){return null!==this.N||(this.N=this.filters.reduce(((e,r)=>e.concat(r.getFlattenedFilters())),[])),this.N}getFilters(){return Object.assign([],this.filters)}}function __PRIVATE_filterEquals(e,r){return e instanceof FieldFilter?function __PRIVATE_fieldFilterEquals(e,r){return r instanceof FieldFilter&&e.op===r.op&&e.field.isEqual(r.field)&&__PRIVATE_valueEquals(e.value,r.value)}(e,r):e instanceof CompositeFilter?function __PRIVATE_compositeFilterEquals(e,r){return r instanceof CompositeFilter&&e.op===r.op&&e.filters.length===r.filters.length&&e.filters.reduce(((e,i,s)=>e&&__PRIVATE_filterEquals(i,r.filters[s])),!0)}(e,r):void fail(19439)}class __PRIVATE_KeyFieldFilter extends FieldFilter{constructor(e,r,i){super(e,r,i),this.key=DocumentKey.fromName(i.referenceValue)}matches(e){const r=DocumentKey.comparator(e.key,this.key);return this.matchesComparison(r)}}class __PRIVATE_KeyFieldInFilter extends FieldFilter{constructor(e,r){super(e,"in",r),this.keys=__PRIVATE_extractDocumentKeysFromArrayValue("in",r)}matches(e){return this.keys.some((r=>r.isEqual(e.key)))}}class __PRIVATE_KeyFieldNotInFilter extends FieldFilter{constructor(e,r){super(e,"not-in",r),this.keys=__PRIVATE_extractDocumentKeysFromArrayValue("not-in",r)}matches(e){return!this.keys.some((r=>r.isEqual(e.key)))}}function __PRIVATE_extractDocumentKeysFromArrayValue(e,r){var i;return((null===(i=r.arrayValue)||void 0===i?void 0:i.values)||[]).map((e=>DocumentKey.fromName(e.referenceValue)))}class __PRIVATE_ArrayContainsFilter extends FieldFilter{constructor(e,r){super(e,"array-contains",r)}matches(e){const r=e.data.field(this.field);return isArray(r)&&__PRIVATE_arrayValueContains(r.arrayValue,this.value)}}class __PRIVATE_InFilter extends FieldFilter{constructor(e,r){super(e,"in",r)}matches(e){const r=e.data.field(this.field);return null!==r&&__PRIVATE_arrayValueContains(this.value.arrayValue,r)}}class __PRIVATE_NotInFilter extends FieldFilter{constructor(e,r){super(e,"not-in",r)}matches(e){if(__PRIVATE_arrayValueContains(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;const r=e.data.field(this.field);return null!==r&&void 0===r.nullValue&&!__PRIVATE_arrayValueContains(this.value.arrayValue,r)}}class __PRIVATE_ArrayContainsAnyFilter extends FieldFilter{constructor(e,r){super(e,"array-contains-any",r)}matches(e){const r=e.data.field(this.field);return!(!isArray(r)||!r.arrayValue.values)&&r.arrayValue.values.some((e=>__PRIVATE_arrayValueContains(this.value.arrayValue,e)))}}class OrderBy{constructor(e,r="asc"){this.field=e,this.dir=r}}function __PRIVATE_orderByEquals(e,r){return e.dir===r.dir&&e.field.isEqual(r.field)}class SnapshotVersion{static fromTimestamp(e){return new SnapshotVersion(e)}static min(){return new SnapshotVersion(new Timestamp(0,0))}static max(){return new SnapshotVersion(new Timestamp(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}class SortedMap{constructor(e,r){this.comparator=e,this.root=r||LLRBNode.EMPTY}insert(e,r){return new SortedMap(this.comparator,this.root.insert(e,r,this.comparator).copy(null,null,LLRBNode.BLACK,null,null))}remove(e){return new SortedMap(this.comparator,this.root.remove(e,this.comparator).copy(null,null,LLRBNode.BLACK,null,null))}get(e){let r=this.root;for(;!r.isEmpty();){const i=this.comparator(e,r.key);if(0===i)return r.value;i<0?r=r.left:i>0&&(r=r.right)}return null}indexOf(e){let r=0,i=this.root;for(;!i.isEmpty();){const s=this.comparator(e,i.key);if(0===s)return r+i.left.size;s<0?i=i.left:(r+=i.left.size+1,i=i.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal(((r,i)=>(e(r,i),!1)))}toString(){const e=[];return this.inorderTraversal(((r,i)=>(e.push(`${r}:${i}`),!1))),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new SortedMapIterator(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new SortedMapIterator(this.root,e,this.comparator,!1)}getReverseIterator(){return new SortedMapIterator(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new SortedMapIterator(this.root,e,this.comparator,!0)}}class SortedMapIterator{constructor(e,r,i,s){this.isReverse=s,this.nodeStack=[];let o=1;for(;!e.isEmpty();)if(o=r?i(e.key,r):1,r&&s&&(o*=-1),o<0)e=this.isReverse?e.left:e.right;else{if(0===o){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop();const r={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return r}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;const e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class LLRBNode{constructor(e,r,i,s,o){this.key=e,this.value=r,this.color=null!=i?i:LLRBNode.RED,this.left=null!=s?s:LLRBNode.EMPTY,this.right=null!=o?o:LLRBNode.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,r,i,s,o){return new LLRBNode(null!=e?e:this.key,null!=r?r:this.value,null!=i?i:this.color,null!=s?s:this.left,null!=o?o:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,r,i){let s=this;const o=i(e,s.key);return s=o<0?s.copy(null,null,null,s.left.insert(e,r,i),null):0===o?s.copy(null,r,null,null,null):s.copy(null,null,null,null,s.right.insert(e,r,i)),s.fixUp()}removeMin(){if(this.left.isEmpty())return LLRBNode.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),e=e.copy(null,null,null,e.left.removeMin(),null),e.fixUp()}remove(e,r){let i,s=this;if(r(e,s.key)<0)s.left.isEmpty()||s.left.isRed()||s.left.left.isRed()||(s=s.moveRedLeft()),s=s.copy(null,null,null,s.left.remove(e,r),null);else{if(s.left.isRed()&&(s=s.rotateRight()),s.right.isEmpty()||s.right.isRed()||s.right.left.isRed()||(s=s.moveRedRight()),0===r(e,s.key)){if(s.right.isEmpty())return LLRBNode.EMPTY;i=s.right.min(),s=s.copy(i.key,i.value,null,null,s.right.removeMin())}s=s.copy(null,null,null,null,s.right.remove(e,r))}return s.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=e.copy(null,null,null,null,e.right.rotateRight()),e=e.rotateLeft(),e=e.colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=e.rotateRight(),e=e.colorFlip()),e}rotateLeft(){const e=this.copy(null,null,LLRBNode.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){const e=this.copy(null,null,LLRBNode.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){const e=this.left.copy(null,null,!this.left.color,null,null),r=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,r)}checkMaxDepth(){const e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw fail(43730,{key:this.key,value:this.value});if(this.right.isRed())throw fail(14113,{key:this.key,value:this.value});const e=this.left.check();if(e!==this.right.check())throw fail(27949);return e+(this.isRed()?0:1)}}LLRBNode.EMPTY=null,LLRBNode.RED=!0,LLRBNode.BLACK=!1,LLRBNode.EMPTY=new class LLRBEmptyNode{constructor(){this.size=0}get key(){throw fail(57766)}get value(){throw fail(16141)}get color(){throw fail(16727)}get left(){throw fail(29726)}get right(){throw fail(36894)}copy(e,r,i,s,o){return this}insert(e,r,i){return new LLRBNode(e,r)}remove(e,r){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class SortedSet{constructor(e){this.comparator=e,this.data=new SortedMap(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal(((r,i)=>(e(r),!1)))}forEachInRange(e,r){const i=this.data.getIteratorFrom(e[0]);for(;i.hasNext();){const s=i.getNext();if(this.comparator(s.key,e[1])>=0)return;r(s.key)}}forEachWhile(e,r){let i;for(i=void 0!==r?this.data.getIteratorFrom(r):this.data.getIterator();i.hasNext();)if(!e(i.getNext().key))return}firstAfterOrEqual(e){const r=this.data.getIteratorFrom(e);return r.hasNext()?r.getNext().key:null}getIterator(){return new SortedSetIterator(this.data.getIterator())}getIteratorFrom(e){return new SortedSetIterator(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let r=this;return r.size<e.size&&(r=e,e=this),e.forEach((e=>{r=r.add(e)})),r}isEqual(e){if(!(e instanceof SortedSet))return!1;if(this.size!==e.size)return!1;const r=this.data.getIterator(),i=e.data.getIterator();for(;r.hasNext();){const e=r.getNext().key,s=i.getNext().key;if(0!==this.comparator(e,s))return!1}return!0}toArray(){const e=[];return this.forEach((r=>{e.push(r)})),e}toString(){const e=[];return this.forEach((r=>e.push(r))),"SortedSet("+e.toString()+")"}copy(e){const r=new SortedSet(this.comparator);return r.data=e,r}}class SortedSetIterator{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}class FieldMask{constructor(e){this.fields=e,e.sort(FieldPath$1.comparator)}static empty(){return new FieldMask([])}unionWith(e){let r=new SortedSet(FieldPath$1.comparator);for(const e of this.fields)r=r.add(e);for(const i of e)r=r.add(i);return new FieldMask(r.toArray())}covers(e){for(const r of this.fields)if(r.isPrefixOf(e))return!0;return!1}isEqual(e){return __PRIVATE_arrayEquals(this.fields,e.fields,((e,r)=>e.isEqual(r)))}}class ObjectValue{constructor(e){this.value=e}static empty(){return new ObjectValue({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let r=this.value;for(let i=0;i<e.length-1;++i)if(r=(r.mapValue.fields||{})[e.get(i)],!__PRIVATE_isMapValue(r))return null;return r=(r.mapValue.fields||{})[e.lastSegment()],r||null}}set(e,r){this.getFieldsMap(e.popLast())[e.lastSegment()]=__PRIVATE_deepClone(r)}setAll(e){let r=FieldPath$1.emptyPath(),i={},s=[];e.forEach(((e,o)=>{if(!r.isImmediateParentOf(o)){const e=this.getFieldsMap(r);this.applyChanges(e,i,s),i={},s=[],r=o.popLast()}e?i[o.lastSegment()]=__PRIVATE_deepClone(e):s.push(o.lastSegment())}));const o=this.getFieldsMap(r);this.applyChanges(o,i,s)}delete(e){const r=this.field(e.popLast());__PRIVATE_isMapValue(r)&&r.mapValue.fields&&delete r.mapValue.fields[e.lastSegment()]}isEqual(e){return __PRIVATE_valueEquals(this.value,e.value)}getFieldsMap(e){let r=this.value;r.mapValue.fields||(r.mapValue={fields:{}});for(let i=0;i<e.length;++i){let s=r.mapValue.fields[e.get(i)];__PRIVATE_isMapValue(s)&&s.mapValue.fields||(s={mapValue:{fields:{}}},r.mapValue.fields[e.get(i)]=s),r=s}return r.mapValue.fields}applyChanges(e,r,i){forEach(r,((r,i)=>e[r]=i));for(const r of i)delete e[r]}clone(){return new ObjectValue(__PRIVATE_deepClone(this.value))}}class MutableDocument{constructor(e,r,i,s,o,a,_){this.key=e,this.documentType=r,this.version=i,this.readTime=s,this.createTime=o,this.data=a,this.documentState=_}static newInvalidDocument(e){return new MutableDocument(e,0,SnapshotVersion.min(),SnapshotVersion.min(),SnapshotVersion.min(),ObjectValue.empty(),0)}static newFoundDocument(e,r,i,s){return new MutableDocument(e,1,r,SnapshotVersion.min(),i,s,0)}static newNoDocument(e,r){return new MutableDocument(e,2,r,SnapshotVersion.min(),SnapshotVersion.min(),ObjectValue.empty(),0)}static newUnknownDocument(e,r){return new MutableDocument(e,3,r,SnapshotVersion.min(),SnapshotVersion.min(),ObjectValue.empty(),2)}convertToFoundDocument(e,r){return!this.createTime.isEqual(SnapshotVersion.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=e),this.version=e,this.documentType=1,this.data=r,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=ObjectValue.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=ObjectValue.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=SnapshotVersion.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof MutableDocument&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new MutableDocument(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class __PRIVATE_TargetImpl{constructor(e,r=null,i=[],s=[],o=null,a=null,_=null){this.path=e,this.collectionGroup=r,this.orderBy=i,this.filters=s,this.limit=o,this.startAt=a,this.endAt=_,this.O=null}}function __PRIVATE_newTarget(e,r=null,i=[],s=[],o=null,a=null,_=null){return new __PRIVATE_TargetImpl(e,r,i,s,o,a,_)}class __PRIVATE_QueryImpl{constructor(e,r=null,i=[],s=[],o=null,a="F",_=null,h=null){this.path=e,this.collectionGroup=r,this.explicitOrderBy=i,this.filters=s,this.limit=o,this.limitType=a,this.startAt=_,this.endAt=h,this.q=null,this.B=null,this.$=null,this.startAt,this.endAt}}function __PRIVATE_isCollectionGroupQuery(e){return null!==e.collectionGroup}function __PRIVATE_queryNormalizedOrderBy(e){const r=__PRIVATE_debugCast(e);if(null===r.q){r.q=[];const e=new Set;for(const i of r.explicitOrderBy)r.q.push(i),e.add(i.field.canonicalString());const i=r.explicitOrderBy.length>0?r.explicitOrderBy[r.explicitOrderBy.length-1].dir:"asc",s=function __PRIVATE_getInequalityFilterFields(e){let r=new SortedSet(FieldPath$1.comparator);return e.filters.forEach((e=>{e.getFlattenedFilters().forEach((e=>{e.isInequality()&&(r=r.add(e.field))}))})),r}(r);s.forEach((s=>{e.has(s.canonicalString())||s.isKeyField()||r.q.push(new OrderBy(s,i))})),e.has(FieldPath$1.keyField().canonicalString())||r.q.push(new OrderBy(FieldPath$1.keyField(),i))}return r.q}function __PRIVATE_queryToTarget(e){const r=__PRIVATE_debugCast(e);return r.B||(r.B=__PRIVATE__queryToTarget(r,__PRIVATE_queryNormalizedOrderBy(e))),r.B}function __PRIVATE__queryToTarget(e,r){if("F"===e.limitType)return __PRIVATE_newTarget(e.path,e.collectionGroup,r,e.filters,e.limit,e.startAt,e.endAt);{r=r.map((e=>{const r="desc"===e.dir?"asc":"desc";return new OrderBy(e.field,r)}));const i=e.endAt?new Bound(e.endAt.position,e.endAt.inclusive):null,s=e.startAt?new Bound(e.startAt.position,e.startAt.inclusive):null;return __PRIVATE_newTarget(e.path,e.collectionGroup,r,e.filters,e.limit,i,s)}}function __PRIVATE_queryWithAddedFilter(e,r){const i=e.filters.concat([r]);return new __PRIVATE_QueryImpl(e.path,e.collectionGroup,e.explicitOrderBy.slice(),i,e.limit,e.limitType,e.startAt,e.endAt)}function __PRIVATE_toDouble(e,r){if(e.useProto3Json){if(isNaN(r))return{doubleValue:"NaN"};if(r===1/0)return{doubleValue:"Infinity"};if(r===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:__PRIVATE_isNegativeZero(r)?"-0":r}}function toNumber(e,r){return function isSafeInteger(e){return"number"==typeof e&&Number.isInteger(e)&&!__PRIVATE_isNegativeZero(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}(r)?function __PRIVATE_toInteger(e){return{integerValue:""+e}}(r):__PRIVATE_toDouble(e,r)}class TransformOperation{constructor(){this._=void 0}}class __PRIVATE_ServerTimestampTransform extends TransformOperation{}class __PRIVATE_ArrayUnionTransformOperation extends TransformOperation{constructor(e){super(),this.elements=e}}class __PRIVATE_ArrayRemoveTransformOperation extends TransformOperation{constructor(e){super(),this.elements=e}}class __PRIVATE_NumericIncrementTransformOperation extends TransformOperation{constructor(e,r){super(),this.serializer=e,this.k=r}}class FieldTransform{constructor(e,r){this.field=e,this.transform=r}}class Precondition{constructor(e,r){this.updateTime=e,this.exists=r}static none(){return new Precondition}static exists(e){return new Precondition(void 0,e)}static updateTime(e){return new Precondition(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}class Mutation{}class __PRIVATE_SetMutation extends Mutation{constructor(e,r,i,s=[]){super(),this.key=e,this.value=r,this.precondition=i,this.fieldTransforms=s,this.type=0}getFieldMask(){return null}}class __PRIVATE_PatchMutation extends Mutation{constructor(e,r,i,s,o=[]){super(),this.key=e,this.data=r,this.fieldMask=i,this.precondition=s,this.fieldTransforms=o,this.type=1}getFieldMask(){return this.fieldMask}}class __PRIVATE_DeleteMutation extends Mutation{constructor(e,r){super(),this.key=e,this.precondition=r,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class __PRIVATE_VerifyMutation extends Mutation{constructor(e,r){super(),this.key=e,this.precondition=r,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}const me={asc:"ASCENDING",desc:"DESCENDING"},pe={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},ge={and:"AND",or:"OR"};class JsonProtoSerializer{constructor(e,r){this.databaseId=e,this.useProto3Json=r}}function toTimestamp(e,r){return e.useProto3Json?`${new Date(1e3*r.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+r.nanoseconds).slice(-9)}Z`:{seconds:""+r.seconds,nanos:r.nanoseconds}}function __PRIVATE_toBytes(e,r){return e.useProto3Json?r.toBase64():r.toUint8Array()}function __PRIVATE_toVersion(e,r){return toTimestamp(e,r.toTimestamp())}function __PRIVATE_fromVersion(e){return __PRIVATE_hardAssert(!!e,49232),SnapshotVersion.fromTimestamp(function fromTimestamp(e){const r=__PRIVATE_normalizeTimestamp(e);return new Timestamp(r.seconds,r.nanos)}(e))}function __PRIVATE_toResourceName(e,r){return __PRIVATE_toResourcePath(e,r).canonicalString()}function __PRIVATE_toResourcePath(e,r){const i=function __PRIVATE_fullyQualifiedPrefixPath(e){return new ResourcePath(["projects",e.projectId,"databases",e.database])}(e).child("documents");return void 0===r?i:i.child(r)}function __PRIVATE_toName(e,r){return __PRIVATE_toResourceName(e.databaseId,r.path)}function fromName(e,r){const i=function __PRIVATE_fromResourceName(e){const r=ResourcePath.fromString(e);return __PRIVATE_hardAssert(__PRIVATE_isValidResourceName(r),10190,{key:r.toString()}),r}(r);if(i.get(1)!==e.databaseId.projectId)throw new FirestoreError(L,"Tried to deserialize key from different project: "+i.get(1)+" vs "+e.databaseId.projectId);if(i.get(3)!==e.databaseId.database)throw new FirestoreError(L,"Tried to deserialize key from different database: "+i.get(3)+" vs "+e.databaseId.database);return new DocumentKey(function __PRIVATE_extractLocalPathFromResourceName(e){return __PRIVATE_hardAssert(e.length>4&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}(i))}function __PRIVATE_toMutationDocument(e,r,i){return{name:__PRIVATE_toName(e,r),fields:i.value.mapValue.fields}}function __PRIVATE_toQueryTarget(e,r){const i={structuredQuery:{}},s=r.path;let o;null!==r.collectionGroup?(o=s,i.structuredQuery.from=[{collectionId:r.collectionGroup,allDescendants:!0}]):(o=s.popLast(),i.structuredQuery.from=[{collectionId:s.lastSegment()}]),i.parent=function __PRIVATE_toQueryPath(e,r){return __PRIVATE_toResourceName(e.databaseId,r)}(e,o);const a=function __PRIVATE_toFilters(e){if(0!==e.length)return __PRIVATE_toFilter(CompositeFilter.create(e,"and"))}(r.filters);a&&(i.structuredQuery.where=a);const _=function __PRIVATE_toOrder(e){if(0!==e.length)return e.map((e=>function __PRIVATE_toPropertyOrder(e){return{field:__PRIVATE_toFieldPathReference(e.field),direction:__PRIVATE_toDirection(e.dir)}}(e)))}(r.orderBy);_&&(i.structuredQuery.orderBy=_);const h=function __PRIVATE_toInt32Proto(e,r){return e.useProto3Json||__PRIVATE_isNullOrUndefined(r)?r:{value:r}}(e,r.limit);return null!==h&&(i.structuredQuery.limit=h),r.startAt&&(i.structuredQuery.startAt=function __PRIVATE_toStartAtCursor(e){return{before:e.inclusive,values:e.position}}(r.startAt)),r.endAt&&(i.structuredQuery.endAt=function __PRIVATE_toEndAtCursor(e){return{before:!e.inclusive,values:e.position}}(r.endAt)),{M:i,parent:o}}function __PRIVATE_toDirection(e){return me[e]}function __PRIVATE_toOperatorName(e){return pe[e]}function __PRIVATE_toCompositeOperatorName(e){return ge[e]}function __PRIVATE_toFieldPathReference(e){return{fieldPath:e.canonicalString()}}function __PRIVATE_toFilter(e){return e instanceof FieldFilter?function __PRIVATE_toUnaryOrFieldFilter(e){if("=="===e.op){if(__PRIVATE_isNanValue(e.value))return{unaryFilter:{field:__PRIVATE_toFieldPathReference(e.field),op:"IS_NAN"}};if(__PRIVATE_isNullValue(e.value))return{unaryFilter:{field:__PRIVATE_toFieldPathReference(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(__PRIVATE_isNanValue(e.value))return{unaryFilter:{field:__PRIVATE_toFieldPathReference(e.field),op:"IS_NOT_NAN"}};if(__PRIVATE_isNullValue(e.value))return{unaryFilter:{field:__PRIVATE_toFieldPathReference(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:__PRIVATE_toFieldPathReference(e.field),op:__PRIVATE_toOperatorName(e.op),value:e.value}}}(e):e instanceof CompositeFilter?function __PRIVATE_toCompositeFilter(e){const r=e.getFilters().map((e=>__PRIVATE_toFilter(e)));return 1===r.length?r[0]:{compositeFilter:{op:__PRIVATE_toCompositeOperatorName(e.op),filters:r}}}(e):fail(54877,{filter:e})}function __PRIVATE_toDocumentMask(e){const r=[];return e.fields.forEach((e=>r.push(e.canonicalString()))),{fieldPaths:r}}function __PRIVATE_isValidResourceName(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}function __PRIVATE_newSerializer(e){return new JsonProtoSerializer(e,!0)}class __PRIVATE_ExponentialBackoff{constructor(e,r,i=1e3,s=1.5,o=6e4){this.U=e,this.timerId=r,this.j=i,this.W=s,this.K=o,this.G=0,this.J=null,this.H=Date.now(),this.reset()}reset(){this.G=0}Y(){this.G=this.K}Z(e){this.cancel();const r=Math.floor(this.G+this.X()),i=Math.max(0,Date.now()-this.H),s=Math.max(0,r-i);s>0&&__PRIVATE_logDebug("ExponentialBackoff",`Backing off for ${s} ms (base delay: ${this.G} ms, delay with jitter: ${r} ms, last attempt: ${i} ms ago)`),this.J=this.U.enqueueAfterDelay(this.timerId,s,(()=>(this.H=Date.now(),e()))),this.G*=this.W,this.G<this.j&&(this.G=this.j),this.G>this.K&&(this.G=this.K)}tt(){null!==this.J&&(this.J.skipDelay(),this.J=null)}cancel(){null!==this.J&&(this.J.cancel(),this.J=null)}X(){return(Math.random()-.5)*this.G}}class Datastore{}class __PRIVATE_DatastoreImpl extends Datastore{constructor(e,r,i,s){super(),this.authCredentials=e,this.appCheckCredentials=r,this.connection=i,this.serializer=s,this.et=!1}rt(){if(this.et)throw new FirestoreError(Q,"The client has already been terminated.")}I(e,r,i,s){return this.rt(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([o,a])=>this.connection.I(e,__PRIVATE_toResourcePath(r,i),s,o,a))).catch((e=>{throw"FirebaseError"===e.name?(e.code===j&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new FirestoreError(O,e.toString())}))}D(e,r,i,s,o){return this.rt(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([a,_])=>this.connection.D(e,__PRIVATE_toResourcePath(r,i),s,a,_,o))).catch((e=>{throw"FirebaseError"===e.name?(e.code===j&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new FirestoreError(O,e.toString())}))}terminate(){this.et=!0,this.connection.terminate()}}async function __PRIVATE_invokeCommitRpc(e,r){const i=__PRIVATE_debugCast(e),s={writes:r.map((e=>function toMutation(e,r){let i;if(r instanceof __PRIVATE_SetMutation)i={update:__PRIVATE_toMutationDocument(e,r.key,r.value)};else if(r instanceof __PRIVATE_DeleteMutation)i={delete:__PRIVATE_toName(e,r.key)};else if(r instanceof __PRIVATE_PatchMutation)i={update:__PRIVATE_toMutationDocument(e,r.key,r.data),updateMask:__PRIVATE_toDocumentMask(r.fieldMask)};else{if(!(r instanceof __PRIVATE_VerifyMutation))return fail(16599,{L:r.type});i={verify:__PRIVATE_toName(e,r.key)}}return r.fieldTransforms.length>0&&(i.updateTransforms=r.fieldTransforms.map((e=>function __PRIVATE_toFieldTransform(e,r){const i=r.transform;if(i instanceof __PRIVATE_ServerTimestampTransform)return{fieldPath:r.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(i instanceof __PRIVATE_ArrayUnionTransformOperation)return{fieldPath:r.field.canonicalString(),appendMissingElements:{values:i.elements}};if(i instanceof __PRIVATE_ArrayRemoveTransformOperation)return{fieldPath:r.field.canonicalString(),removeAllFromArray:{values:i.elements}};if(i instanceof __PRIVATE_NumericIncrementTransformOperation)return{fieldPath:r.field.canonicalString(),increment:i.k};throw fail(20930,{transform:r.transform})}(0,e)))),r.precondition.isNone||(i.currentDocument=function __PRIVATE_toPrecondition(e,r){return void 0!==r.updateTime?{updateTime:__PRIVATE_toVersion(e,r.updateTime)}:void 0!==r.exists?{exists:r.exists}:fail(27497)}(e,r.precondition)),i}(i.serializer,e)))};await i.I("Commit",i.serializer.databaseId,ResourcePath.emptyPath(),s)}async function __PRIVATE_invokeBatchGetDocumentsRpc(e,r){const i=__PRIVATE_debugCast(e),s={documents:r.map((e=>__PRIVATE_toName(i.serializer,e)))},o=await i.D("BatchGetDocuments",i.serializer.databaseId,ResourcePath.emptyPath(),s,r.length),a=new Map;o.forEach((e=>{const r=function __PRIVATE_fromBatchGetDocumentsResponse(e,r){return"found"in r?function __PRIVATE_fromFound(e,r){__PRIVATE_hardAssert(!!r.found,43571),r.found.name,r.found.updateTime;const i=fromName(e,r.found.name),s=__PRIVATE_fromVersion(r.found.updateTime),o=r.found.createTime?__PRIVATE_fromVersion(r.found.createTime):SnapshotVersion.min(),a=new ObjectValue({mapValue:{fields:r.found.fields}});return MutableDocument.newFoundDocument(i,s,o,a)}(e,r):"missing"in r?function __PRIVATE_fromMissing(e,r){__PRIVATE_hardAssert(!!r.missing,3894),__PRIVATE_hardAssert(!!r.readTime,22933);const i=fromName(e,r.missing),s=__PRIVATE_fromVersion(r.readTime);return MutableDocument.newNoDocument(i,s)}(e,r):fail(7234,{result:r})}(i.serializer,e);a.set(r.key.toString(),r)}));const _=[];return r.forEach((e=>{const r=a.get(e.toString());__PRIVATE_hardAssert(!!r,55234,{key:e}),_.push(r)})),_}const Ee="ComponentProvider",ye=new Map;function __PRIVATE_getDatastore(e){if(e._terminated)throw new FirestoreError(Q,"The client has already been terminated.");if(!ye.has(e)){__PRIVATE_logDebug(Ee,"Initializing Datastore");const r=function __PRIVATE_newConnection(e){return new __PRIVATE_FetchConnection(e)}(function __PRIVATE_makeDatabaseInfo(e,r,i,s){return new DatabaseInfo(e,r,i,s.host,s.ssl,s.experimentalForceLongPolling,s.experimentalAutoDetectLongPolling,__PRIVATE_cloneLongPollingOptions(s.experimentalLongPollingOptions),s.useFetchStreams,s.isUsingEmulator)}(e._databaseId,e.app.options.appId||"",e._persistenceKey,e._freezeSettings())),i=__PRIVATE_newSerializer(e._databaseId),s=function __PRIVATE_newDatastore(e,r,i,s){return new __PRIVATE_DatastoreImpl(e,r,i,s)}(e._authCredentials,e._appCheckCredentials,r,i);ye.set(e,s)}return ye.get(e)}const Te="firestore.googleapis.com",Ae=!0;class FirestoreSettingsImpl{constructor(e){var r,i;if(void 0===e.host){if(void 0!==e.ssl)throw new FirestoreError(L,"Can't provide ssl option if host option is not set");this.host=Te,this.ssl=Ae}else this.host=e.host,this.ssl=null!==(r=e.ssl)&&void 0!==r?r:Ae;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new FirestoreError(L,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}!function __PRIVATE_validateIsNotUsedTogether(e,r,i,s){if(!0===r&&!0===s)throw new FirestoreError(L,`${e} and ${i} cannot be used together.`)}("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=__PRIVATE_cloneLongPollingOptions(null!==(i=e.experimentalLongPollingOptions)&&void 0!==i?i:{}),function __PRIVATE_validateLongPollingOptions(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new FirestoreError(L,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new FirestoreError(L,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new FirestoreError(L,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&function __PRIVATE_longPollingOptionsEqual(e,r){return e.timeoutSeconds===r.timeoutSeconds}(this.experimentalLongPollingOptions,e.experimentalLongPollingOptions)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}}class Firestore{constructor(e,r,i,s){this._authCredentials=e,this._appCheckCredentials=r,this._databaseId=i,this._app=s,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new FirestoreSettingsImpl({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new FirestoreError(Q,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new FirestoreError(Q,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new FirestoreSettingsImpl(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=function __PRIVATE_makeAuthCredentialsProvider(e){if(!e)return new __PRIVATE_EmptyAuthCredentialsProvider;switch(e.type){case"firstParty":return new __PRIVATE_FirstPartyAuthCredentialsProvider(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new FirestoreError(L,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function __PRIVATE_removeComponents(e){const r=ye.get(e);r&&(__PRIVATE_logDebug(Ee,"Removing Datastore"),ye.delete(e),r.terminate())}(this),Promise.resolve()}}function initializeFirestore(e,r,i){i||(i=X);const s=_getProvider(e,"firestore/lite");if(s.isInitialized(i))throw new FirestoreError(Q,"Firestore can only be initialized once per app.");return s.initialize({options:r,instanceIdentifier:i})}function getFirestore(e,r){const i="object"==typeof e?e:s(),o="string"==typeof e?e:r||"(default)",a=_getProvider(i,"firestore/lite").getImmediate({identifier:o});if(!a._initialized){const e=getDefaultEmulatorHostnameAndPort("firestore");e&&connectFirestoreEmulator(a,...e)}return a}function connectFirestoreEmulator(e,r,i,s={}){var o;e=__PRIVATE_cast(e,Firestore);const a=isCloudWorkstation(r),_=e._getSettings(),h=Object.assign(Object.assign({},_),{emulatorOptions:e._getEmulatorOptions()}),d=`${r}:${i}`;a&&(async function pingServer(e){return(await fetch(e,{credentials:"include"})).ok}(`https://${d}`),updateEmulatorBanner("Firestore",!0)),_.host!==Te&&_.host!==d&&__PRIVATE_logWarn("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");const f=Object.assign(Object.assign({},_),{host:d,ssl:a,emulatorOptions:s});if(!deepEqual(f,h)&&(e._setSettings(f),s.mockUserToken)){let r,i;if("string"==typeof s.mockUserToken)r=s.mockUserToken,i=User.MOCK_USER;else{r=function createMockUserToken(e,r){if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');const i=r||"demo-project",s=e.iat||0,o=e.sub||e.user_id;if(!o)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");const a=Object.assign({iss:`https://securetoken.google.com/${i}`,aud:i,iat:s,exp:s+3600,auth_time:s,sub:o,user_id:o,firebase:{sign_in_provider:"custom",identities:{}}},e);return[base64urlEncodeWithoutPadding(JSON.stringify({alg:"none",type:"JWT"})),base64urlEncodeWithoutPadding(JSON.stringify(a)),""].join(".")}(s.mockUserToken,null===(o=e._app)||void 0===o?void 0:o.options.projectId);const a=s.mockUserToken.sub||s.mockUserToken.user_id;if(!a)throw new FirestoreError(L,"mockUserToken must contain 'sub' or 'user_id' field!");i=new User(a)}e._authCredentials=new __PRIVATE_EmulatorAuthCredentialsProvider(new __PRIVATE_OAuthToken(r,i))}}function terminate(e){return e=__PRIVATE_cast(e,Firestore),o(e.app,"firestore/lite"),e._delete()}class AggregateField{constructor(e="count",r){this._internalFieldPath=r,this.type="AggregateField",this.aggregateType=e}}class AggregateQuerySnapshot{constructor(e,r,i){this._userDataWriter=r,this._data=i,this.type="AggregateQuerySnapshot",this.query=e}data(){return this._userDataWriter.convertObjectMap(this._data)}}class Query{constructor(e,r,i){this.converter=r,this._query=i,this.type="query",this.firestore=e}withConverter(e){return new Query(this.firestore,e,this._query)}}class DocumentReference{constructor(e,r,i){this.converter=r,this._key=i,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new CollectionReference(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new DocumentReference(this.firestore,e,this._key)}toJSON(){return{type:DocumentReference._jsonSchemaVersion,referencePath:this._key.toString()}}static fromJSON(e,r,i){if(__PRIVATE_validateJSON(r,DocumentReference._jsonSchema))return new DocumentReference(e,i||null,new DocumentKey(ResourcePath.fromString(r.referencePath)))}}DocumentReference._jsonSchemaVersion="firestore/documentReference/1.0",DocumentReference._jsonSchema={type:property("string",DocumentReference._jsonSchemaVersion),referencePath:property("string")};class CollectionReference extends Query{constructor(e,r,i){super(e,r,function __PRIVATE_newQueryForPath(e){return new __PRIVATE_QueryImpl(e)}(i)),this._path=i,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){const e=this._path.popLast();return e.isEmpty()?null:new DocumentReference(this.firestore,null,new DocumentKey(e))}withConverter(e){return new CollectionReference(this.firestore,e,this._path)}}function collection(e,r,...i){if(e=getModularInstance(e),__PRIVATE_validateNonEmptyArgument("collection","path",r),e instanceof Firestore){const s=ResourcePath.fromString(r,...i);return __PRIVATE_validateCollectionPath(s),new CollectionReference(e,null,s)}{if(!(e instanceof DocumentReference||e instanceof CollectionReference))throw new FirestoreError(L,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const s=e._path.child(ResourcePath.fromString(r,...i));return __PRIVATE_validateCollectionPath(s),new CollectionReference(e.firestore,null,s)}}function collectionGroup(e,r){if(e=__PRIVATE_cast(e,Firestore),__PRIVATE_validateNonEmptyArgument("collectionGroup","collection id",r),r.indexOf("/")>=0)throw new FirestoreError(L,`Invalid collection ID '${r}' passed to function collectionGroup(). Collection IDs must not contain '/'.`);return new Query(e,null,function __PRIVATE_newQueryForCollectionGroup(e){return new __PRIVATE_QueryImpl(ResourcePath.emptyPath(),e)}(r))}function doc(e,r,...i){if(e=getModularInstance(e),1===arguments.length&&(r=__PRIVATE_AutoId.newId()),__PRIVATE_validateNonEmptyArgument("doc","path",r),e instanceof Firestore){const s=ResourcePath.fromString(r,...i);return __PRIVATE_validateDocumentPath(s),new DocumentReference(e,null,new DocumentKey(s))}{if(!(e instanceof DocumentReference||e instanceof CollectionReference))throw new FirestoreError(L,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const s=e._path.child(ResourcePath.fromString(r,...i));return __PRIVATE_validateDocumentPath(s),new DocumentReference(e.firestore,e instanceof CollectionReference?e.converter:null,new DocumentKey(s))}}function refEqual(e,r){return e=getModularInstance(e),r=getModularInstance(r),(e instanceof DocumentReference||e instanceof CollectionReference)&&(r instanceof DocumentReference||r instanceof CollectionReference)&&e.firestore===r.firestore&&e.path===r.path&&e.converter===r.converter}function queryEqual(e,r){return e=getModularInstance(e),r=getModularInstance(r),e instanceof Query&&r instanceof Query&&e.firestore===r.firestore&&function __PRIVATE_queryEquals(e,r){return function __PRIVATE_targetEquals(e,r){if(e.limit!==r.limit)return!1;if(e.orderBy.length!==r.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++)if(!__PRIVATE_orderByEquals(e.orderBy[i],r.orderBy[i]))return!1;if(e.filters.length!==r.filters.length)return!1;for(let i=0;i<e.filters.length;i++)if(!__PRIVATE_filterEquals(e.filters[i],r.filters[i]))return!1;return e.collectionGroup===r.collectionGroup&&!!e.path.isEqual(r.path)&&!!__PRIVATE_boundEquals(e.startAt,r.startAt)&&__PRIVATE_boundEquals(e.endAt,r.endAt)}(__PRIVATE_queryToTarget(e),__PRIVATE_queryToTarget(r))&&e.limitType===r.limitType}(e._query,r._query)&&e.converter===r.converter}class Bytes{constructor(e){this._byteString=e}static fromBase64String(e){try{return new Bytes(ByteString.fromBase64String(e))}catch(e){throw new FirestoreError(L,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new Bytes(ByteString.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}toJSON(){return{type:Bytes._jsonSchemaVersion,bytes:this.toBase64()}}static fromJSON(e){if(__PRIVATE_validateJSON(e,Bytes._jsonSchema))return Bytes.fromBase64String(e.bytes)}}Bytes._jsonSchemaVersion="firestore/bytes/1.0",Bytes._jsonSchema={type:property("string",Bytes._jsonSchemaVersion),bytes:property("string")};class FieldPath{constructor(...e){for(let r=0;r<e.length;++r)if(0===e[r].length)throw new FirestoreError(L,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new FieldPath$1(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}function documentId(){return new FieldPath(ee)}class FieldValue{constructor(e){this._methodName=e}}class GeoPoint{constructor(e,r){if(!isFinite(e)||e<-90||e>90)throw new FirestoreError(L,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(r)||r<-180||r>180)throw new FirestoreError(L,"Longitude must be a number between -180 and 180, but was: "+r);this._lat=e,this._long=r}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}_compareTo(e){return __PRIVATE_primitiveComparator(this._lat,e._lat)||__PRIVATE_primitiveComparator(this._long,e._long)}toJSON(){return{latitude:this._lat,longitude:this._long,type:GeoPoint._jsonSchemaVersion}}static fromJSON(e){if(__PRIVATE_validateJSON(e,GeoPoint._jsonSchema))return new GeoPoint(e.latitude,e.longitude)}}GeoPoint._jsonSchemaVersion="firestore/geoPoint/1.0",GeoPoint._jsonSchema={type:property("string",GeoPoint._jsonSchemaVersion),latitude:property("number"),longitude:property("number")};class VectorValue{constructor(e){this._values=(e||[]).map((e=>e))}toArray(){return this._values.map((e=>e))}isEqual(e){return function __PRIVATE_isPrimitiveArrayEqual(e,r){if(e.length!==r.length)return!1;for(let i=0;i<e.length;++i)if(e[i]!==r[i])return!1;return!0}(this._values,e._values)}toJSON(){return{type:VectorValue._jsonSchemaVersion,vectorValues:this._values}}static fromJSON(e){if(__PRIVATE_validateJSON(e,VectorValue._jsonSchema)){if(Array.isArray(e.vectorValues)&&e.vectorValues.every((e=>"number"==typeof e)))return new VectorValue(e.vectorValues);throw new FirestoreError(L,"Expected 'vectorValues' field to be a number array")}}}VectorValue._jsonSchemaVersion="firestore/vectorValue/1.0",VectorValue._jsonSchema={type:property("string",VectorValue._jsonSchemaVersion),vectorValues:property("object")};const Ve=/^__.*__$/;class ParsedSetData{constructor(e,r,i){this.data=e,this.fieldMask=r,this.fieldTransforms=i}toMutation(e,r){return null!==this.fieldMask?new __PRIVATE_PatchMutation(e,this.data,this.fieldMask,r,this.fieldTransforms):new __PRIVATE_SetMutation(e,this.data,r,this.fieldTransforms)}}class ParsedUpdateData{constructor(e,r,i){this.data=e,this.fieldMask=r,this.fieldTransforms=i}toMutation(e,r){return new __PRIVATE_PatchMutation(e,this.data,this.fieldMask,r,this.fieldTransforms)}}function __PRIVATE_isWrite(e){switch(e){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw fail(40011,{it:e})}}class __PRIVATE_ParseContextImpl{constructor(e,r,i,s,o,a){this.settings=e,this.databaseId=r,this.serializer=i,this.ignoreUndefinedProperties=s,void 0===o&&this.st(),this.fieldTransforms=o||[],this.fieldMask=a||[]}get path(){return this.settings.path}get it(){return this.settings.it}ot(e){return new __PRIVATE_ParseContextImpl(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}ut(e){var r;const i=null===(r=this.path)||void 0===r?void 0:r.child(e),s=this.ot({path:i,_t:!1});return s.ct(e),s}lt(e){var r;const i=null===(r=this.path)||void 0===r?void 0:r.child(e),s=this.ot({path:i,_t:!1});return s.st(),s}ht(e){return this.ot({path:void 0,_t:!0})}ft(e){return __PRIVATE_createError(e,this.settings.methodName,this.settings.dt||!1,this.path,this.settings.Et)}contains(e){return void 0!==this.fieldMask.find((r=>e.isPrefixOf(r)))||void 0!==this.fieldTransforms.find((r=>e.isPrefixOf(r.field)))}st(){if(this.path)for(let e=0;e<this.path.length;e++)this.ct(this.path.get(e))}ct(e){if(0===e.length)throw this.ft("Document fields must not be empty");if(__PRIVATE_isWrite(this.it)&&Ve.test(e))throw this.ft('Document fields cannot begin and end with "__"')}}class __PRIVATE_UserDataReader{constructor(e,r,i){this.databaseId=e,this.ignoreUndefinedProperties=r,this.serializer=i||__PRIVATE_newSerializer(e)}Tt(e,r,i,s=!1){return new __PRIVATE_ParseContextImpl({it:e,methodName:r,Et:i,path:FieldPath$1.emptyPath(),_t:!1,dt:s},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function __PRIVATE_newUserDataReader(e){const r=e._freezeSettings(),i=__PRIVATE_newSerializer(e._databaseId);return new __PRIVATE_UserDataReader(e._databaseId,!!r.ignoreUndefinedProperties,i)}function __PRIVATE_parseSetData(e,r,i,s,o,a={}){const _=e.Tt(a.merge||a.mergeFields?2:0,r,i,o);__PRIVATE_validatePlainObject("Data must be an object, but it was:",_,s);const h=__PRIVATE_parseObject(s,_);let d,f;if(a.merge)d=new FieldMask(_.fieldMask),f=_.fieldTransforms;else if(a.mergeFields){const e=[];for(const s of a.mergeFields){const o=__PRIVATE_fieldPathFromArgument$1(r,s,i);if(!_.contains(o))throw new FirestoreError(L,`Field '${o}' is specified in your field mask but missing from your input data.`);__PRIVATE_fieldMaskContains(e,o)||e.push(o)}d=new FieldMask(e),f=_.fieldTransforms.filter((e=>d.covers(e.field)))}else d=null,f=_.fieldTransforms;return new ParsedSetData(new ObjectValue(h),d,f)}class __PRIVATE_DeleteFieldValueImpl extends FieldValue{_toFieldTransform(e){if(2!==e.it)throw 1===e.it?e.ft(`${this._methodName}() can only appear at the top level of your update data`):e.ft(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof __PRIVATE_DeleteFieldValueImpl}}function __PRIVATE_createSentinelChildContext(e,r,i){return new __PRIVATE_ParseContextImpl({it:3,Et:r.settings.Et,methodName:e._methodName,_t:i},r.databaseId,r.serializer,r.ignoreUndefinedProperties)}class __PRIVATE_ServerTimestampFieldValueImpl extends FieldValue{_toFieldTransform(e){return new FieldTransform(e.path,new __PRIVATE_ServerTimestampTransform)}isEqual(e){return e instanceof __PRIVATE_ServerTimestampFieldValueImpl}}class __PRIVATE_ArrayUnionFieldValueImpl extends FieldValue{constructor(e,r){super(e),this.Pt=r}_toFieldTransform(e){const r=__PRIVATE_createSentinelChildContext(this,e,!0),i=this.Pt.map((e=>__PRIVATE_parseData(e,r))),s=new __PRIVATE_ArrayUnionTransformOperation(i);return new FieldTransform(e.path,s)}isEqual(e){return e instanceof __PRIVATE_ArrayUnionFieldValueImpl&&deepEqual(this.Pt,e.Pt)}}class __PRIVATE_ArrayRemoveFieldValueImpl extends FieldValue{constructor(e,r){super(e),this.Pt=r}_toFieldTransform(e){const r=__PRIVATE_createSentinelChildContext(this,e,!0),i=this.Pt.map((e=>__PRIVATE_parseData(e,r))),s=new __PRIVATE_ArrayRemoveTransformOperation(i);return new FieldTransform(e.path,s)}isEqual(e){return e instanceof __PRIVATE_ArrayRemoveFieldValueImpl&&deepEqual(this.Pt,e.Pt)}}class __PRIVATE_NumericIncrementFieldValueImpl extends FieldValue{constructor(e,r){super(e),this.At=r}_toFieldTransform(e){const r=new __PRIVATE_NumericIncrementTransformOperation(e.serializer,toNumber(e.serializer,this.At));return new FieldTransform(e.path,r)}isEqual(e){return e instanceof __PRIVATE_NumericIncrementFieldValueImpl&&this.At===e.At}}function __PRIVATE_parseUpdateData(e,r,i,s){const o=e.Tt(1,r,i);__PRIVATE_validatePlainObject("Data must be an object, but it was:",o,s);const a=[],_=ObjectValue.empty();forEach(s,((e,s)=>{const h=__PRIVATE_fieldPathFromDotSeparatedString(r,e,i);s=getModularInstance(s);const d=o.lt(h);if(s instanceof __PRIVATE_DeleteFieldValueImpl)a.push(h);else{const e=__PRIVATE_parseData(s,d);null!=e&&(a.push(h),_.set(h,e))}}));const h=new FieldMask(a);return new ParsedUpdateData(_,h,o.fieldTransforms)}function __PRIVATE_parseUpdateVarargs(e,r,i,s,o,a){const _=e.Tt(1,r,i),h=[__PRIVATE_fieldPathFromArgument$1(r,s,i)],d=[o];if(a.length%2!=0)throw new FirestoreError(L,`Function ${r}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let e=0;e<a.length;e+=2)h.push(__PRIVATE_fieldPathFromArgument$1(r,a[e])),d.push(a[e+1]);const f=[],g=ObjectValue.empty();for(let e=h.length-1;e>=0;--e)if(!__PRIVATE_fieldMaskContains(f,h[e])){const r=h[e];let i=d[e];i=getModularInstance(i);const s=_.lt(r);if(i instanceof __PRIVATE_DeleteFieldValueImpl)f.push(r);else{const e=__PRIVATE_parseData(i,s);null!=e&&(f.push(r),g.set(r,e))}}const E=new FieldMask(f);return new ParsedUpdateData(g,E,_.fieldTransforms)}function __PRIVATE_parseQueryValue(e,r,i,s=!1){return __PRIVATE_parseData(i,e.Tt(s?4:3,r))}function __PRIVATE_parseData(e,r){if(__PRIVATE_looksLikeJsonObject(e=getModularInstance(e)))return __PRIVATE_validatePlainObject("Unsupported field value:",r,e),__PRIVATE_parseObject(e,r);if(e instanceof FieldValue)return function __PRIVATE_parseSentinelFieldValue(e,r){if(!__PRIVATE_isWrite(r.it))throw r.ft(`${e._methodName}() can only be used with update() and set()`);if(!r.path)throw r.ft(`${e._methodName}() is not currently supported inside arrays`);const i=e._toFieldTransform(r);i&&r.fieldTransforms.push(i)}(e,r),null;if(void 0===e&&r.ignoreUndefinedProperties)return null;if(r.path&&r.fieldMask.push(r.path),e instanceof Array){if(r.settings._t&&4!==r.it)throw r.ft("Nested arrays are not supported");return function __PRIVATE_parseArray(e,r){const i=[];let s=0;for(const o of e){let e=__PRIVATE_parseData(o,r.ht(s));null==e&&(e={nullValue:"NULL_VALUE"}),i.push(e),s++}return{arrayValue:{values:i}}}(e,r)}return function __PRIVATE_parseScalarValue(e,r){if(null===(e=getModularInstance(e)))return{nullValue:"NULL_VALUE"};if("number"==typeof e)return toNumber(r.serializer,e);if("boolean"==typeof e)return{booleanValue:e};if("string"==typeof e)return{stringValue:e};if(e instanceof Date){const i=Timestamp.fromDate(e);return{timestampValue:toTimestamp(r.serializer,i)}}if(e instanceof Timestamp){const i=new Timestamp(e.seconds,1e3*Math.floor(e.nanoseconds/1e3));return{timestampValue:toTimestamp(r.serializer,i)}}if(e instanceof GeoPoint)return{geoPointValue:{latitude:e.latitude,longitude:e.longitude}};if(e instanceof Bytes)return{bytesValue:__PRIVATE_toBytes(r.serializer,e._byteString)};if(e instanceof DocumentReference){const i=r.databaseId,s=e.firestore._databaseId;if(!s.isEqual(i))throw r.ft(`Document reference is for database ${s.projectId}/${s.database} but should be for database ${i.projectId}/${i.database}`);return{referenceValue:__PRIVATE_toResourceName(e.firestore._databaseId||r.databaseId,e._key.path)}}if(e instanceof VectorValue)return function __PRIVATE_parseVectorValue(e,r){return{mapValue:{fields:{[ce]:{stringValue:de},[fe]:{arrayValue:{values:e.toArray().map((e=>{if("number"!=typeof e)throw r.ft("VectorValues must only contain numeric values.");return __PRIVATE_toDouble(r.serializer,e)}))}}}}}}(e,r);throw r.ft(`Unsupported field value: ${__PRIVATE_valueDescription(e)}`)}(e,r)}function __PRIVATE_parseObject(e,r){const i={};return function isEmpty(e){for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r))return!1;return!0}(e)?r.path&&r.path.length>0&&r.fieldMask.push(r.path):forEach(e,((e,s)=>{const o=__PRIVATE_parseData(s,r.ut(e));null!=o&&(i[e]=o)})),{mapValue:{fields:i}}}function __PRIVATE_looksLikeJsonObject(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof Timestamp||e instanceof GeoPoint||e instanceof Bytes||e instanceof DocumentReference||e instanceof FieldValue||e instanceof VectorValue)}function __PRIVATE_validatePlainObject(e,r,i){if(!__PRIVATE_looksLikeJsonObject(i)||!__PRIVATE_isPlainObject(i)){const s=__PRIVATE_valueDescription(i);throw"an object"===s?r.ft(e+" a custom object"):r.ft(e+" "+s)}}function __PRIVATE_fieldPathFromArgument$1(e,r,i){if((r=getModularInstance(r))instanceof FieldPath)return r._internalPath;if("string"==typeof r)return __PRIVATE_fieldPathFromDotSeparatedString(e,r);throw __PRIVATE_createError("Field path arguments must be of type string or ",e,!1,void 0,i)}const Ie=new RegExp("[~\\*/\\[\\]]");function __PRIVATE_fieldPathFromDotSeparatedString(e,r,i){if(r.search(Ie)>=0)throw __PRIVATE_createError(`Invalid field path (${r}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,i);try{return new FieldPath(...r.split("."))._internalPath}catch(s){throw __PRIVATE_createError(`Invalid field path (${r}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,i)}}function __PRIVATE_createError(e,r,i,s,o){const a=s&&!s.isEmpty(),_=void 0!==o;let h=`Function ${r}() called with invalid data`;i&&(h+=" (via `toFirestore()`)"),h+=". ";let d="";return(a||_)&&(d+=" (found",a&&(d+=` in field ${s}`),_&&(d+=` in document ${o}`),d+=")"),new FirestoreError(L,h+e+d)}function __PRIVATE_fieldMaskContains(e,r){return e.some((e=>e.isEqual(r)))}class DocumentSnapshot{constructor(e,r,i,s,o){this._firestore=e,this._userDataWriter=r,this._key=i,this._document=s,this._converter=o}get id(){return this._key.path.lastSegment()}get ref(){return new DocumentReference(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){const e=new QueryDocumentSnapshot(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){const r=this._document.data.field(__PRIVATE_fieldPathFromArgument("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r)}}}class QueryDocumentSnapshot extends DocumentSnapshot{data(){return super.data()}}class QuerySnapshot{constructor(e,r){this._docs=r,this.query=e}get docs(){return[...this._docs]}get size(){return this.docs.length}get empty(){return 0===this.docs.length}forEach(e,r){this._docs.forEach(e,r)}}function snapshotEqual(e,r){return e=getModularInstance(e),r=getModularInstance(r),e instanceof DocumentSnapshot&&r instanceof DocumentSnapshot?e._firestore===r._firestore&&e._key.isEqual(r._key)&&(null===e._document?null===r._document:e._document.isEqual(r._document))&&e._converter===r._converter:e instanceof QuerySnapshot&&r instanceof QuerySnapshot&&queryEqual(e.query,r.query)&&__PRIVATE_arrayEquals(e.docs,r.docs,snapshotEqual)}function __PRIVATE_fieldPathFromArgument(e,r){return"string"==typeof r?__PRIVATE_fieldPathFromDotSeparatedString(e,r):r instanceof FieldPath?r._internalPath:r._delegate._internalPath}class AppliableConstraint{}class QueryConstraint extends AppliableConstraint{}function query(e,r,...i){let s=[];r instanceof AppliableConstraint&&s.push(r),s=s.concat(i),function __PRIVATE_validateQueryConstraintArray(e){const r=e.filter((e=>e instanceof QueryCompositeFilterConstraint)).length,i=e.filter((e=>e instanceof QueryFieldFilterConstraint)).length;if(r>1||r>0&&i>0)throw new FirestoreError(L,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(s);for(const r of s)e=r._apply(e);return e}class QueryFieldFilterConstraint extends QueryConstraint{constructor(e,r,i){super(),this._field=e,this._op=r,this._value=i,this.type="where"}static _create(e,r,i){return new QueryFieldFilterConstraint(e,r,i)}_apply(e){const r=this._parse(e);return __PRIVATE_validateNewFieldFilter(e._query,r),new Query(e.firestore,e.converter,__PRIVATE_queryWithAddedFilter(e._query,r))}_parse(e){const r=__PRIVATE_newUserDataReader(e.firestore),i=function __PRIVATE_newQueryFilter(e,r,i,s,o,a,_){let h;if(o.isKeyField()){if("array-contains"===a||"array-contains-any"===a)throw new FirestoreError(L,`Invalid Query. You can't perform '${a}' queries on documentId().`);if("in"===a||"not-in"===a){__PRIVATE_validateDisjunctiveFilterElements(_,a);const r=[];for(const i of _)r.push(__PRIVATE_parseDocumentIdValue(s,e,i));h={arrayValue:{values:r}}}else h=__PRIVATE_parseDocumentIdValue(s,e,_)}else"in"!==a&&"not-in"!==a&&"array-contains-any"!==a||__PRIVATE_validateDisjunctiveFilterElements(_,a),h=__PRIVATE_parseQueryValue(i,r,_,"in"===a||"not-in"===a);return FieldFilter.create(o,a,h)}(e._query,"where",r,e.firestore._databaseId,this._field,this._op,this._value);return i}}function where(e,r,i){const s=r,o=__PRIVATE_fieldPathFromArgument("where",e);return QueryFieldFilterConstraint._create(o,s,i)}class QueryCompositeFilterConstraint extends AppliableConstraint{constructor(e,r){super(),this.type=e,this._queryConstraints=r}static _create(e,r){return new QueryCompositeFilterConstraint(e,r)}_parse(e){const r=this._queryConstraints.map((r=>r._parse(e))).filter((e=>e.getFilters().length>0));return 1===r.length?r[0]:CompositeFilter.create(r,this._getOperator())}_apply(e){const r=this._parse(e);return 0===r.getFilters().length?e:(function __PRIVATE_validateNewFilter(e,r){let i=e;const s=r.getFlattenedFilters();for(const e of s)__PRIVATE_validateNewFieldFilter(i,e),i=__PRIVATE_queryWithAddedFilter(i,e)}(e._query,r),new Query(e.firestore,e.converter,__PRIVATE_queryWithAddedFilter(e._query,r)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}function or(...e){return e.forEach((e=>__PRIVATE_validateQueryFilterConstraint("or",e))),QueryCompositeFilterConstraint._create("or",e)}function and(...e){return e.forEach((e=>__PRIVATE_validateQueryFilterConstraint("and",e))),QueryCompositeFilterConstraint._create("and",e)}class QueryOrderByConstraint extends QueryConstraint{constructor(e,r){super(),this._field=e,this._direction=r,this.type="orderBy"}static _create(e,r){return new QueryOrderByConstraint(e,r)}_apply(e){const r=function __PRIVATE_newQueryOrderBy(e,r,i){if(null!==e.startAt)throw new FirestoreError(L,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new FirestoreError(L,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new OrderBy(r,i)}(e._query,this._field,this._direction);return new Query(e.firestore,e.converter,function __PRIVATE_queryWithAddedOrderBy(e,r){const i=e.explicitOrderBy.concat([r]);return new __PRIVATE_QueryImpl(e.path,e.collectionGroup,i,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)}(e._query,r))}}function orderBy(e,r="asc"){const i=r,s=__PRIVATE_fieldPathFromArgument("orderBy",e);return QueryOrderByConstraint._create(s,i)}class QueryLimitConstraint extends QueryConstraint{constructor(e,r,i){super(),this.type=e,this._limit=r,this._limitType=i}static _create(e,r,i){return new QueryLimitConstraint(e,r,i)}_apply(e){return new Query(e.firestore,e.converter,function __PRIVATE_queryWithLimit(e,r,i){return new __PRIVATE_QueryImpl(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),r,i,e.startAt,e.endAt)}(e._query,this._limit,this._limitType))}}function limit(e){return __PRIVATE_validatePositiveNumber("limit",e),QueryLimitConstraint._create("limit",e,"F")}function limitToLast(e){return __PRIVATE_validatePositiveNumber("limitToLast",e),QueryLimitConstraint._create("limitToLast",e,"L")}class QueryStartAtConstraint extends QueryConstraint{constructor(e,r,i){super(),this.type=e,this._docOrFields=r,this._inclusive=i}static _create(e,r,i){return new QueryStartAtConstraint(e,r,i)}_apply(e){const r=__PRIVATE_newQueryBoundFromDocOrFields(e,this.type,this._docOrFields,this._inclusive);return new Query(e.firestore,e.converter,function __PRIVATE_queryWithStartAt(e,r){return new __PRIVATE_QueryImpl(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,r,e.endAt)}(e._query,r))}}function startAt(...e){return QueryStartAtConstraint._create("startAt",e,!0)}function startAfter(...e){return QueryStartAtConstraint._create("startAfter",e,!1)}class QueryEndAtConstraint extends QueryConstraint{constructor(e,r,i){super(),this.type=e,this._docOrFields=r,this._inclusive=i}static _create(e,r,i){return new QueryEndAtConstraint(e,r,i)}_apply(e){const r=__PRIVATE_newQueryBoundFromDocOrFields(e,this.type,this._docOrFields,this._inclusive);return new Query(e.firestore,e.converter,function __PRIVATE_queryWithEndAt(e,r){return new __PRIVATE_QueryImpl(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,e.startAt,r)}(e._query,r))}}function endBefore(...e){return QueryEndAtConstraint._create("endBefore",e,!1)}function endAt(...e){return QueryEndAtConstraint._create("endAt",e,!0)}function __PRIVATE_newQueryBoundFromDocOrFields(e,r,i,s){if(i[0]=getModularInstance(i[0]),i[0]instanceof DocumentSnapshot)return function __PRIVATE_newQueryBoundFromDocument(e,r,i,s,o){if(!s)throw new FirestoreError(q,`Can't use a DocumentSnapshot that doesn't exist for ${i}().`);const a=[];for(const i of __PRIVATE_queryNormalizedOrderBy(e))if(i.field.isKeyField())a.push(__PRIVATE_refValue(r,s.key));else{const e=s.data.field(i.field);if(__PRIVATE_isServerTimestamp(e))throw new FirestoreError(L,'Invalid query. You are trying to start or end a query using a document for which the field "'+i.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){const e=i.field.canonicalString();throw new FirestoreError(L,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}a.push(e)}return new Bound(a,o)}(e._query,e.firestore._databaseId,r,i[0]._document,s);{const o=__PRIVATE_newUserDataReader(e.firestore);return function __PRIVATE_newQueryBoundFromFields(e,r,i,s,o,a){const _=e.explicitOrderBy;if(o.length>_.length)throw new FirestoreError(L,`Too many arguments provided to ${s}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);const h=[];for(let a=0;a<o.length;a++){const d=o[a];if(_[a].field.isKeyField()){if("string"!=typeof d)throw new FirestoreError(L,`Invalid query. Expected a string for document ID in ${s}(), but got a ${typeof d}`);if(!__PRIVATE_isCollectionGroupQuery(e)&&-1!==d.indexOf("/"))throw new FirestoreError(L,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${s}() must be a plain document ID, but '${d}' contains a slash.`);const i=e.path.child(ResourcePath.fromString(d));if(!DocumentKey.isDocumentKey(i))throw new FirestoreError(L,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${s}() must result in a valid document path, but '${i}' is not because it contains an odd number of segments.`);const o=new DocumentKey(i);h.push(__PRIVATE_refValue(r,o))}else{const e=__PRIVATE_parseQueryValue(i,s,d);h.push(e)}}return new Bound(h,a)}(e._query,e.firestore._databaseId,o,r,i,s)}}function __PRIVATE_parseDocumentIdValue(e,r,i){if("string"==typeof(i=getModularInstance(i))){if(""===i)throw new FirestoreError(L,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!__PRIVATE_isCollectionGroupQuery(r)&&-1!==i.indexOf("/"))throw new FirestoreError(L,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${i}' contains a '/' character.`);const s=r.path.child(ResourcePath.fromString(i));if(!DocumentKey.isDocumentKey(s))throw new FirestoreError(L,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${s}' is not because it has an odd number of segments (${s.length}).`);return __PRIVATE_refValue(e,new DocumentKey(s))}if(i instanceof DocumentReference)return __PRIVATE_refValue(e,i._key);throw new FirestoreError(L,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${__PRIVATE_valueDescription(i)}.`)}function __PRIVATE_validateDisjunctiveFilterElements(e,r){if(!Array.isArray(e)||0===e.length)throw new FirestoreError(L,`Invalid Query. A non-empty array is required for '${r.toString()}' filters.`)}function __PRIVATE_validateNewFieldFilter(e,r){const i=function __PRIVATE_findOpInsideFilters(e,r){for(const i of e)for(const e of i.getFlattenedFilters())if(r.indexOf(e.op)>=0)return e.op;return null}(e.filters,function __PRIVATE_conflictingOps(e){switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(r.op));if(null!==i)throw i===r.op?new FirestoreError(L,`Invalid query. You cannot use more than one '${r.op.toString()}' filter.`):new FirestoreError(L,`Invalid query. You cannot use '${r.op.toString()}' filters with '${i.toString()}' filters.`)}function __PRIVATE_validateQueryFilterConstraint(e,r){if(!(r instanceof QueryFieldFilterConstraint||r instanceof QueryCompositeFilterConstraint))throw new FirestoreError(L,`Function ${e}() requires AppliableConstraints created with a call to 'where(...)', 'or(...)', or 'and(...)'.`)}class AbstractUserDataWriter{convertValue(e,r="none"){switch(__PRIVATE_typeOrder(e)){case 0:return null;case 1:return e.booleanValue;case 2:return __PRIVATE_normalizeNumber(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,r);case 5:return e.stringValue;case 6:return this.convertBytes(__PRIVATE_normalizeByteString(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,r);case 11:return this.convertObject(e.mapValue,r);case 10:return this.convertVectorValue(e.mapValue);default:throw fail(62114,{value:e})}}convertObject(e,r){return this.convertObjectMap(e.fields,r)}convertObjectMap(e,r="none"){const i={};return forEach(e,((e,s)=>{i[e]=this.convertValue(s,r)})),i}convertVectorValue(e){var r,i,s;const o=null===(s=null===(i=null===(r=e.fields)||void 0===r?void 0:r[fe].arrayValue)||void 0===i?void 0:i.values)||void 0===s?void 0:s.map((e=>__PRIVATE_normalizeNumber(e.doubleValue)));return new VectorValue(o)}convertGeoPoint(e){return new GeoPoint(__PRIVATE_normalizeNumber(e.latitude),__PRIVATE_normalizeNumber(e.longitude))}convertArray(e,r){return(e.values||[]).map((e=>this.convertValue(e,r)))}convertServerTimestamp(e,r){switch(r){case"previous":const i=__PRIVATE_getPreviousValue(e);return null==i?null:this.convertValue(i,r);case"estimate":return this.convertTimestamp(__PRIVATE_getLocalWriteTime(e));default:return null}}convertTimestamp(e){const r=__PRIVATE_normalizeTimestamp(e);return new Timestamp(r.seconds,r.nanos)}convertDocumentKey(e,r){const i=ResourcePath.fromString(e);__PRIVATE_hardAssert(__PRIVATE_isValidResourceName(i),9688,{name:e});const s=new DatabaseId(i.get(1),i.get(3)),o=new DocumentKey(i.popFirst(5));return s.isEqual(r)||__PRIVATE_logError(`Document ${o} contains a document reference within a different database (${s.projectId}/${s.database}) which is not supported. It will be treated as a reference in the current database (${r.projectId}/${r.database}) instead.`),o}}function __PRIVATE_applyFirestoreDataConverter(e,r,i){let s;return s=e?i&&(i.merge||i.mergeFields)?e.toFirestore(r,i):e.toFirestore(r):r,s}class __PRIVATE_LiteUserDataWriter extends AbstractUserDataWriter{constructor(e){super(),this.firestore=e}convertBytes(e){return new Bytes(e)}convertReference(e){const r=this.convertDocumentKey(e,this.firestore._databaseId);return new DocumentReference(this.firestore,null,r)}}function getDoc(e){const r=__PRIVATE_getDatastore((e=__PRIVATE_cast(e,DocumentReference)).firestore),i=new __PRIVATE_LiteUserDataWriter(e.firestore);return __PRIVATE_invokeBatchGetDocumentsRpc(r,[e._key]).then((r=>{__PRIVATE_hardAssert(1===r.length,15618);const s=r[0];return new DocumentSnapshot(e.firestore,i,e._key,s.isFoundDocument()?s:null,e.converter)}))}function getDocs(e){!function __PRIVATE_validateHasExplicitOrderByForLimitToLast(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new FirestoreError(K,"limitToLast() queries require specifying at least one orderBy() clause")}((e=__PRIVATE_cast(e,Query))._query);const r=__PRIVATE_getDatastore(e.firestore),i=new __PRIVATE_LiteUserDataWriter(e.firestore);return async function __PRIVATE_invokeRunQueryRpc(e,r){const i=__PRIVATE_debugCast(e),{M:s,parent:o}=__PRIVATE_toQueryTarget(i.serializer,__PRIVATE_queryToTarget(r));return(await i.D("RunQuery",i.serializer.databaseId,o,{structuredQuery:s.structuredQuery})).filter((e=>!!e.document)).map((e=>function __PRIVATE_fromDocument(e,r,i){const s=fromName(e,r.name),o=__PRIVATE_fromVersion(r.updateTime),a=r.createTime?__PRIVATE_fromVersion(r.createTime):SnapshotVersion.min(),_=new ObjectValue({mapValue:{fields:r.fields}}),h=MutableDocument.newFoundDocument(s,o,a,_);return i&&h.setHasCommittedMutations(),i?h.setHasCommittedMutations():h}(i.serializer,e.document,void 0)))}(r,e._query).then((r=>{const s=r.map((r=>new QueryDocumentSnapshot(e.firestore,i,r.key,r,e.converter)));return"L"===e._query.limitType&&s.reverse(),new QuerySnapshot(e,s)}))}function setDoc(e,r,i){const s=__PRIVATE_applyFirestoreDataConverter((e=__PRIVATE_cast(e,DocumentReference)).converter,r,i),o=__PRIVATE_parseSetData(__PRIVATE_newUserDataReader(e.firestore),"setDoc",e._key,s,null!==e.converter,i);return __PRIVATE_invokeCommitRpc(__PRIVATE_getDatastore(e.firestore),[o.toMutation(e._key,Precondition.none())])}function updateDoc(e,r,i,...s){const o=__PRIVATE_newUserDataReader((e=__PRIVATE_cast(e,DocumentReference)).firestore);let a;return a="string"==typeof(r=getModularInstance(r))||r instanceof FieldPath?__PRIVATE_parseUpdateVarargs(o,"updateDoc",e._key,r,i,s):__PRIVATE_parseUpdateData(o,"updateDoc",e._key,r),__PRIVATE_invokeCommitRpc(__PRIVATE_getDatastore(e.firestore),[a.toMutation(e._key,Precondition.exists(!0))])}function deleteDoc(e){return __PRIVATE_invokeCommitRpc(__PRIVATE_getDatastore((e=__PRIVATE_cast(e,DocumentReference)).firestore),[new __PRIVATE_DeleteMutation(e._key,Precondition.none())])}function addDoc(e,r){const i=doc(e=__PRIVATE_cast(e,CollectionReference)),s=__PRIVATE_applyFirestoreDataConverter(e.converter,r),o=__PRIVATE_parseSetData(__PRIVATE_newUserDataReader(e.firestore),"addDoc",i._key,s,null!==i.converter,{});return __PRIVATE_invokeCommitRpc(__PRIVATE_getDatastore(e.firestore),[o.toMutation(i._key,Precondition.exists(!1))]).then((()=>i))}function getCount(e){return getAggregate(e,{count:count()})}function getAggregate(e,r){const i=__PRIVATE_cast(e.firestore,Firestore),s=__PRIVATE_getDatastore(i),o=function __PRIVATE_mapToArray(e,r){const i=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&i.push(r(e[s],s,e));return i}(r,((e,r)=>new __PRIVATE_AggregateImpl(r,e.aggregateType,e._internalFieldPath)));return async function __PRIVATE_invokeRunAggregationQueryRpc(e,r,i){var s;const o=__PRIVATE_debugCast(e),{request:a,nt:_,parent:h}=function __PRIVATE_toRunAggregationQueryRequest(e,r,i,s){const{M:o,parent:a}=__PRIVATE_toQueryTarget(e,r),_={},h=[];let d=0;return i.forEach((e=>{const r=s?e.alias:"aggregate_"+d++;_[r]=e.alias,"count"===e.aggregateType?h.push({alias:r,count:{}}):"avg"===e.aggregateType?h.push({alias:r,avg:{field:__PRIVATE_toFieldPathReference(e.fieldPath)}}):"sum"===e.aggregateType&&h.push({alias:r,sum:{field:__PRIVATE_toFieldPathReference(e.fieldPath)}})})),{request:{structuredAggregationQuery:{aggregations:h,structuredQuery:o.structuredQuery},parent:o.parent},nt:_,parent:a}}(o.serializer,function __PRIVATE_queryToAggregateTarget(e){const r=__PRIVATE_debugCast(e);return r.$||(r.$=__PRIVATE__queryToTarget(r,e.explicitOrderBy)),r.$}(r),i);o.connection.P||delete a.parent;const d=(await o.D("RunAggregationQuery",o.serializer.databaseId,h,a,1)).filter((e=>!!e.result));__PRIVATE_hardAssert(1===d.length,64727);const f=null===(s=d[0].result)||void 0===s?void 0:s.aggregateFields;return Object.keys(f).reduce(((e,r)=>(e[_[r]]=f[r],e)),{})}(s,e._query,o).then((r=>function __PRIVATE_convertToAggregateQuerySnapshot(e,r,i){const s=new __PRIVATE_LiteUserDataWriter(e);return new AggregateQuerySnapshot(r,s,i)}(i,e,r)))}function sum(e){return new AggregateField("sum",__PRIVATE_fieldPathFromArgument$1("sum",e))}function average(e){return new AggregateField("avg",__PRIVATE_fieldPathFromArgument$1("average",e))}function count(){return new AggregateField("count")}function aggregateFieldEqual(e,r){var i,s;return e instanceof AggregateField&&r instanceof AggregateField&&e.aggregateType===r.aggregateType&&(null===(i=e._internalFieldPath)||void 0===i?void 0:i.canonicalString())===(null===(s=r._internalFieldPath)||void 0===s?void 0:s.canonicalString())}function aggregateQuerySnapshotEqual(e,r){return queryEqual(e.query,r.query)&&deepEqual(e.data(),r.data())}function deleteField(){return new __PRIVATE_DeleteFieldValueImpl("deleteField")}function serverTimestamp(){return new __PRIVATE_ServerTimestampFieldValueImpl("serverTimestamp")}function arrayUnion(...e){return new __PRIVATE_ArrayUnionFieldValueImpl("arrayUnion",e)}function arrayRemove(...e){return new __PRIVATE_ArrayRemoveFieldValueImpl("arrayRemove",e)}function increment(e){return new __PRIVATE_NumericIncrementFieldValueImpl("increment",e)}function vector(e){return new VectorValue(e)}class WriteBatch{constructor(e,r){this._firestore=e,this._commitHandler=r,this._mutations=[],this._committed=!1,this._dataReader=__PRIVATE_newUserDataReader(e)}set(e,r,i){this._verifyNotCommitted();const s=__PRIVATE_validateReference(e,this._firestore),o=__PRIVATE_applyFirestoreDataConverter(s.converter,r,i),a=__PRIVATE_parseSetData(this._dataReader,"WriteBatch.set",s._key,o,null!==s.converter,i);return this._mutations.push(a.toMutation(s._key,Precondition.none())),this}update(e,r,i,...s){this._verifyNotCommitted();const o=__PRIVATE_validateReference(e,this._firestore);let a;return a="string"==typeof(r=getModularInstance(r))||r instanceof FieldPath?__PRIVATE_parseUpdateVarargs(this._dataReader,"WriteBatch.update",o._key,r,i,s):__PRIVATE_parseUpdateData(this._dataReader,"WriteBatch.update",o._key,r),this._mutations.push(a.toMutation(o._key,Precondition.exists(!0))),this}delete(e){this._verifyNotCommitted();const r=__PRIVATE_validateReference(e,this._firestore);return this._mutations=this._mutations.concat(new __PRIVATE_DeleteMutation(r._key,Precondition.none())),this}commit(){return this._verifyNotCommitted(),this._committed=!0,this._mutations.length>0?this._commitHandler(this._mutations):Promise.resolve()}_verifyNotCommitted(){if(this._committed)throw new FirestoreError(Q,"A write batch can no longer be used after commit() has been called.")}}function __PRIVATE_validateReference(e,r){if((e=getModularInstance(e)).firestore!==r)throw new FirestoreError(L,"Provided document reference is from a different Firestore instance.");return e}function writeBatch(e){const r=__PRIVATE_getDatastore(e=__PRIVATE_cast(e,Firestore));return new WriteBatch(e,(e=>__PRIVATE_invokeCommitRpc(r,e)))}class Transaction$1{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),this.mutations.length>0)throw this.lastTransactionError=new FirestoreError(L,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;const r=await __PRIVATE_invokeBatchGetDocumentsRpc(this.datastore,e);return r.forEach((e=>this.recordVersion(e))),r}set(e,r){this.write(r.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,r){try{this.write(r.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new __PRIVATE_DeleteMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;const e=this.readVersions;this.mutations.forEach((r=>{e.delete(r.key.toString())})),e.forEach(((e,r)=>{const i=DocumentKey.fromPath(r);this.mutations.push(new __PRIVATE_VerifyMutation(i,this.precondition(i)))})),await __PRIVATE_invokeCommitRpc(this.datastore,this.mutations),this.committed=!0}recordVersion(e){let r;if(e.isFoundDocument())r=e.version;else{if(!e.isNoDocument())throw fail(50498,{Rt:e.constructor.name});r=SnapshotVersion.min()}const i=this.readVersions.get(e.key.toString());if(i){if(!r.isEqual(i))throw new FirestoreError(z,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),r)}precondition(e){const r=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&r?r.isEqual(SnapshotVersion.min())?Precondition.exists(!1):Precondition.updateTime(r):Precondition.none()}preconditionForUpdate(e){const r=this.readVersions.get(e.toString());if(!this.writtenDocs.has(e.toString())&&r){if(r.isEqual(SnapshotVersion.min()))throw new FirestoreError(L,"Can't update a document that doesn't exist.");return Precondition.updateTime(r)}return Precondition.exists(!0)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}const Pe={maxAttempts:5};class __PRIVATE_TransactionRunner{constructor(e,r,i,s,o){this.asyncQueue=e,this.datastore=r,this.options=i,this.updateFunction=s,this.deferred=o,this.Vt=i.maxAttempts,this.It=new __PRIVATE_ExponentialBackoff(this.asyncQueue,"transaction_retry")}yt(){this.Vt-=1,this.gt()}gt(){this.It.Z((async()=>{const e=new Transaction$1(this.datastore),r=this.wt(e);r&&r.then((r=>{this.asyncQueue.enqueueAndForget((()=>e.commit().then((()=>{this.deferred.resolve(r)})).catch((e=>{this.vt(e)}))))})).catch((e=>{this.vt(e)}))}))}wt(e){try{const r=this.updateFunction(e);return!__PRIVATE_isNullOrUndefined(r)&&r.catch&&r.then?r:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(e){return this.deferred.reject(e),null}}vt(e){this.Vt>0&&this.Ft(e)?(this.Vt-=1,this.asyncQueue.enqueueAndForget((()=>(this.gt(),Promise.resolve())))):this.deferred.reject(e)}Ft(e){if("FirebaseError"===e.name){const r=e.code;return"aborted"===r||"failed-precondition"===r||"already-exists"===r||!function __PRIVATE_isPermanentError(e){switch(e){case b:return fail(64938);case N:case O:case M:case $:case Y:case Z:case j:return!1;case L:case q:case"already-exists":case U:case Q:case z:case W:case K:case"data-loss":return!0;default:return fail(15467,{code:e})}}(r)}return!1}}function getDocument(){return"undefined"!=typeof document?document:null}class DelayedOperation{constructor(e,r,i,s,o){this.asyncQueue=e,this.timerId=r,this.targetTimeMs=i,this.op=s,this.removalCallback=o,this.deferred=new __PRIVATE_Deferred,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch((e=>{}))}get promise(){return this.deferred.promise}static createAndSchedule(e,r,i,s,o){const a=Date.now()+i,_=new DelayedOperation(e,r,a,s,o);return _.start(i),_}start(e){this.timerHandle=setTimeout((()=>this.handleDelayElapsed()),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new FirestoreError(N,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget((()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then((e=>this.deferred.resolve(e)))):Promise.resolve()))}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}const Re="AsyncQueue";class __PRIVATE_AsyncQueueImpl{constructor(e=Promise.resolve()){this.bt=[],this.Dt=!1,this.St=[],this.Ct=null,this.Nt=!1,this.Ot=!1,this.qt=[],this.It=new __PRIVATE_ExponentialBackoff(this,"async_queue_retry"),this.Bt=()=>{const e=getDocument();e&&__PRIVATE_logDebug(Re,"Visibility state changed to "+e.visibilityState),this.It.tt()},this.$t=e;const r=getDocument();r&&"function"==typeof r.addEventListener&&r.addEventListener("visibilitychange",this.Bt)}get isShuttingDown(){return this.Dt}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.Qt(),this.kt(e)}enterRestrictedMode(e){if(!this.Dt){this.Dt=!0,this.Ot=e||!1;const r=getDocument();r&&"function"==typeof r.removeEventListener&&r.removeEventListener("visibilitychange",this.Bt)}}enqueue(e){if(this.Qt(),this.Dt)return new Promise((()=>{}));const r=new __PRIVATE_Deferred;return this.kt((()=>this.Dt&&this.Ot?Promise.resolve():(e().then(r.resolve,r.reject),r.promise))).then((()=>r.promise))}enqueueRetryable(e){this.enqueueAndForget((()=>(this.bt.push(e),this.Lt())))}async Lt(){if(0!==this.bt.length){try{await this.bt[0](),this.bt.shift(),this.It.reset()}catch(e){if(!function __PRIVATE_isIndexedDbTransactionError(e){return"IndexedDbTransactionError"===e.name}(e))throw e;__PRIVATE_logDebug(Re,"Operation failed with retryable error: "+e)}this.bt.length>0&&this.It.Z((()=>this.Lt()))}}kt(e){const r=this.$t.then((()=>(this.Nt=!0,e().catch((e=>{throw this.Ct=e,this.Nt=!1,__PRIVATE_logError("INTERNAL UNHANDLED ERROR: ",__PRIVATE_getMessageOrStack(e)),e})).then((e=>(this.Nt=!1,e))))));return this.$t=r,r}enqueueAfterDelay(e,r,i){this.Qt(),this.qt.indexOf(e)>-1&&(r=0);const s=DelayedOperation.createAndSchedule(this,e,r,i,(e=>this.xt(e)));return this.St.push(s),s}Qt(){this.Ct&&fail(47125,{Mt:__PRIVATE_getMessageOrStack(this.Ct)})}verifyOperationInProgress(){}async Ut(){let e;do{e=this.$t,await e}while(e!==this.$t)}jt(e){for(const r of this.St)if(r.timerId===e)return!0;return!1}zt(e){return this.Ut().then((()=>{this.St.sort(((e,r)=>e.targetTimeMs-r.targetTimeMs));for(const r of this.St)if(r.skipDelay(),"all"!==e&&r.timerId===e)break;return this.Ut()}))}Wt(e){this.qt.push(e)}xt(e){const r=this.St.indexOf(e);this.St.splice(r,1)}}function __PRIVATE_getMessageOrStack(e){let r=e.message||"";return e.stack&&(r=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),r}class Transaction{constructor(e,r){this._firestore=e,this._transaction=r,this._dataReader=__PRIVATE_newUserDataReader(e)}get(e){const r=__PRIVATE_validateReference(e,this._firestore),i=new __PRIVATE_LiteUserDataWriter(this._firestore);return this._transaction.lookup([r._key]).then((e=>{if(!e||1!==e.length)return fail(24041);const s=e[0];if(s.isFoundDocument())return new DocumentSnapshot(this._firestore,i,s.key,s,r.converter);if(s.isNoDocument())return new DocumentSnapshot(this._firestore,i,r._key,null,r.converter);throw fail(18433,{doc:s})}))}set(e,r,i){const s=__PRIVATE_validateReference(e,this._firestore),o=__PRIVATE_applyFirestoreDataConverter(s.converter,r,i),a=__PRIVATE_parseSetData(this._dataReader,"Transaction.set",s._key,o,null!==s.converter,i);return this._transaction.set(s._key,a),this}update(e,r,i,...s){const o=__PRIVATE_validateReference(e,this._firestore);let a;return a="string"==typeof(r=getModularInstance(r))||r instanceof FieldPath?__PRIVATE_parseUpdateVarargs(this._dataReader,"Transaction.update",o._key,r,i,s):__PRIVATE_parseUpdateData(this._dataReader,"Transaction.update",o._key,r),this._transaction.update(o._key,a),this}delete(e){const r=__PRIVATE_validateReference(e,this._firestore);return this._transaction.delete(r._key),this}}function runTransaction(e,r,i){const s=__PRIVATE_getDatastore(e=__PRIVATE_cast(e,Firestore)),o=Object.assign(Object.assign({},Pe),i);!function __PRIVATE_validateTransactionOptions(e){if(e.maxAttempts<1)throw new FirestoreError(L,"Max attempts must be at least 1")}(o);const a=new __PRIVATE_Deferred;return new __PRIVATE_TransactionRunner(function __PRIVATE_newAsyncQueue(){return new __PRIVATE_AsyncQueueImpl}(),s,o,(i=>r(new Transaction(e,i))),a).yt(),a.promise}!function __PRIVATE_registerFirestore(){!function __PRIVATE_setSDKVersion(e){w=e}(`${a}_lite`),e(new Component("firestore/lite",((e,{instanceIdentifier:r,options:i})=>{const s=e.getProvider("app").getImmediate(),o=new Firestore(new __PRIVATE_LiteAuthCredentialsProvider(e.getProvider("auth-internal")),new __PRIVATE_LiteAppCheckTokenProvider(s,e.getProvider("app-check-internal")),function __PRIVATE_databaseIdFromApp(e,r){if(!Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))throw new FirestoreError(L,'"projectId" not provided in firebase.initializeApp.');return new DatabaseId(e.options.projectId,r)}(s,r),s);return i&&o._setSettings(i),o}),"PUBLIC").setMultipleInstances(!0)),r("firestore-lite",R,""),r("firestore-lite",R,"esm2017")}();export{AggregateField,AggregateQuerySnapshot,Bytes,CollectionReference,DocumentReference,DocumentSnapshot,FieldPath,FieldValue,Firestore,FirestoreError,GeoPoint,Query,QueryCompositeFilterConstraint,QueryConstraint,QueryDocumentSnapshot,QueryEndAtConstraint,QueryFieldFilterConstraint,QueryLimitConstraint,QueryOrderByConstraint,QuerySnapshot,QueryStartAtConstraint,Timestamp,Transaction,VectorValue,WriteBatch,addDoc,aggregateFieldEqual,aggregateQuerySnapshotEqual,and,arrayRemove,arrayUnion,average,collection,collectionGroup,connectFirestoreEmulator,count,deleteDoc,deleteField,doc,documentId,endAt,endBefore,getAggregate,getCount,getDoc,getDocs,getFirestore,increment,initializeFirestore,limit,limitToLast,or,orderBy,query,queryEqual,refEqual,runTransaction,serverTimestamp,setDoc,setLogLevel,snapshotEqual,startAfter,startAt,sum,terminate,updateDoc,vector,where,writeBatch};

//# sourceMappingURL=firebase-firestore-lite.js.map
