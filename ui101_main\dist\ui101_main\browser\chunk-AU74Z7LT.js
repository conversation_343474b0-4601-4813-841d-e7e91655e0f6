import {
  AuthService,
  BaseIconButton,
  Button,
  ButtonModule,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  IconDirective,
  IconModule,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Tooltip,
  TooltipModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-VEYUXI7N.js";
import {
  Auth,
  ChangeDetectorRef,
  CommonModule,
  Component,
  ContentChild,
  Directive,
  ElementRef,
  HostBinding,
  Injectable,
  Input,
  NgClass,
  NgIf,
  NgModule,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
  NgTemplateOutlet,
  Renderer2,
  Router,
  TemplateRef,
  ViewChild,
  inject,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵInheritDefinitionFeature,
  ɵɵadvance,
  ɵɵariaProperty,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵviewQuery
} from "./chunk-V3EQCA3M.js";

// src/app/services/firebase-test.service.ts
var FirebaseTestService = class _FirebaseTestService {
  auth = inject(Auth);
  testFirebaseConnection() {
    console.log("=== Firebase Connection Test ===");
    console.log("Auth instance:", this.auth);
    console.log("Auth app:", this.auth.app);
    console.log("Auth config:", this.auth.config);
    console.log("Auth current user:", this.auth.currentUser);
    try {
      console.log("Firebase app name:", this.auth.app.name);
      console.log("Firebase app options:", this.auth.app.options);
      console.log("\u2705 Firebase appears to be properly initialized");
    } catch (error) {
      console.error("\u274C Firebase initialization error:", error);
    }
  }
  static \u0275fac = function FirebaseTestService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FirebaseTestService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _FirebaseTestService, factory: _FirebaseTestService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FirebaseTestService, [{
    type: Injectable,
    args: [{ providedIn: "root" }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-input.mjs
var _c0 = ["wrapper"];
var _c1 = ["*", [["", "cdsTextArea", ""], ["", "ibmTextArea", ""], ["textarea"]]];
var _c2 = ["*", "[cdsTextArea],[ibmTextArea],textarea"];
var _c3 = (a0) => ({
  "cds--label--disabled": a0
});
var _c4 = (a0) => ({
  "cds--text-area__wrapper--warn": a0
});
var _c5 = (a0) => ({
  "cds--form__helper-text--disabled": a0
});
function TextareaLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "span", 4)(2, "div", 5);
    \u0275\u0275elementContainerEnd();
  }
}
function TextareaLabelComponent_ng_container_1_3_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_3_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function TextareaLabelComponent_ng_container_1_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0);
  }
}
function TextareaLabelComponent_ng_container_1__svg_svg_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 13);
  }
}
function TextareaLabelComponent_ng_container_1__svg_svg_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 14);
  }
}
function TextareaLabelComponent_ng_container_1_10_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_10_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.textAreaTemplate);
  }
}
function TextareaLabelComponent_ng_container_1_ng_template_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0, 1);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_Template, 1, 1, null, 3);
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(3, "svg", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_Template, 1, 1, null, 3);
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(3, "svg", 14);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "hr", 15);
    \u0275\u0275template(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_Template, 4, 2, "div", 16)(3, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_Template, 4, 2, "div", 16);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.helperText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(3, _c5, ctx_r0.disabled));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275template(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_Template, 3, 5, "div", 18)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_Template, 3, 2, "div", 16)(3, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_Template, 3, 2, "div", 16);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextareaLabelComponent_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 6)(2, "label", 7);
    \u0275\u0275template(3, TextareaLabelComponent_ng_container_1_3_Template, 1, 1, null, 8)(4, TextareaLabelComponent_ng_container_1_ng_template_4_Template, 1, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 9, 1);
    \u0275\u0275template(8, TextareaLabelComponent_ng_container_1__svg_svg_8_Template, 1, 0, "svg", 10)(9, TextareaLabelComponent_ng_container_1__svg_svg_9_Template, 1, 0, "svg", 11)(10, TextareaLabelComponent_ng_container_1_10_Template, 1, 1, null, 8)(11, TextareaLabelComponent_ng_container_1_ng_template_11_Template, 1, 0, "ng-template", null, 2, \u0275\u0275templateRefExtractor)(13, TextareaLabelComponent_ng_container_1_ng_container_13_Template, 4, 2, "ng-container", 3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, TextareaLabelComponent_ng_container_1_ng_container_14_Template, 4, 3, "ng-container", 3);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = \u0275\u0275reference(5);
    const textAreaContent_r3 = \u0275\u0275reference(12);
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("for", ctx_r0.labelInputID)("ngClass", \u0275\u0275pureFunction1(13, _c3, ctx_r0.disabled));
    \u0275\u0275attribute("aria-label", ctx_r0.ariaLabel);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(15, _c4, ctx_r0.warn));
    \u0275\u0275attribute("data-invalid", ctx_r0.invalid ? true : null);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.fluid && ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.fluid && !ctx_r0.invalid && ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.textAreaTemplate)("ngIfElse", textAreaContent_r3);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r0.fluid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.fluid);
  }
}
var _c6 = ["*", [["", "cdsText", ""], ["", "ibmText", ""], ["input", "type", "text"], ["div"]]];
var _c7 = ["*", "[cdsText],[ibmText],input[type=text],div"];
var _c8 = (a0) => ({
  "cds--text-input__field-wrapper--warning": a0
});
function TextInputLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "span", 6)(2, "div", 7);
    \u0275\u0275elementContainerEnd();
  }
}
function TextInputLabelComponent_label_1_1_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_label_1_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_label_1_1_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function TextInputLabelComponent_label_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0);
  }
}
function TextInputLabelComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "label", 8);
    \u0275\u0275template(1, TextInputLabelComponent_label_1_1_Template, 1, 1, null, 9)(2, TextInputLabelComponent_label_1_ng_template_2_Template, 1, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = \u0275\u0275reference(3);
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("for", ctx_r0.labelInputID)("ngClass", \u0275\u0275pureFunction1(5, _c3, ctx_r0.disabled));
    \u0275\u0275attribute("aria-label", ctx_r0.ariaLabel);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
  }
}
function TextInputLabelComponent_div_2__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 15);
  }
}
function TextInputLabelComponent_div_2__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 16);
  }
}
function TextInputLabelComponent_div_2_5_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_5_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.textInputTemplate);
  }
}
function TextInputLabelComponent_div_2_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0, 1);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_8_div_2_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_8_div_3_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextInputLabelComponent_div_2_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "hr", 17);
    \u0275\u0275template(2, TextInputLabelComponent_div_2_ng_container_8_div_2_Template, 3, 2, "div", 18)(3, TextInputLabelComponent_div_2_ng_container_8_div_3_Template, 3, 2, "div", 18);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.helperText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_ng_container_9_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_9_div_1_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_1_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(3, _c5, ctx_r0.disabled));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_ng_container_9_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_9_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_2_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TextInputLabelComponent_div_2_ng_container_9_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_9_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_3_2_Template, 1, 1, null, 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275template(1, TextInputLabelComponent_div_2_ng_container_9_div_1_Template, 3, 5, "div", 20)(2, TextInputLabelComponent_div_2_ng_container_9_div_2_Template, 3, 2, "div", 18)(3, TextInputLabelComponent_div_2_ng_container_9_div_3_Template, 3, 2, "div", 18);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextInputLabelComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11)(1, "div", 12, 1);
    \u0275\u0275template(3, TextInputLabelComponent_div_2__svg_svg_3_Template, 1, 0, "svg", 13)(4, TextInputLabelComponent_div_2__svg_svg_4_Template, 1, 0, "svg", 14)(5, TextInputLabelComponent_div_2_5_Template, 1, 1, null, 9)(6, TextInputLabelComponent_div_2_ng_template_6_Template, 1, 0, "ng-template", null, 2, \u0275\u0275templateRefExtractor)(8, TextInputLabelComponent_div_2_ng_container_8_Template, 4, 2, "ng-container", 3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, TextInputLabelComponent_div_2_ng_container_9_Template, 4, 3, "ng-container", 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const textInputContent_r3 = \u0275\u0275reference(7);
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(8, _c8, ctx_r0.warn));
    \u0275\u0275attribute("data-invalid", ctx_r0.invalid ? true : null);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.invalid && !ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.textInputTemplate)("ngIfElse", textInputContent_r3);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r0.fluid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.fluid);
  }
}
var _c9 = ["*", [["", "cdsPassword", ""], ["", "ibmPassword", ""]]];
var _c10 = ["*", "[cdsPassword], [ibmPassword]"];
function PasswordInputLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "span", 5)(2, "div", 6);
    \u0275\u0275elementContainerEnd();
  }
}
function PasswordInputLabelComponent_label_1_1_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_label_1_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_label_1_1_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function PasswordInputLabelComponent_label_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0);
  }
}
function PasswordInputLabelComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "label", 7);
    \u0275\u0275template(1, PasswordInputLabelComponent_label_1_1_Template, 1, 1, null, 8)(2, PasswordInputLabelComponent_label_1_ng_template_2_Template, 1, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = \u0275\u0275reference(3);
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("for", ctx_r0.labelInputID)("ngClass", \u0275\u0275pureFunction1(5, _c3, ctx_r0.disabled));
    \u0275\u0275attribute("aria-label", ctx_r0.ariaLabel);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
  }
}
function PasswordInputLabelComponent_div_2__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 15);
  }
}
function PasswordInputLabelComponent_div_2__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 16);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 22);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 23);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "cds-tooltip", 17)(1, "div", 18)(2, "button", 19);
    \u0275\u0275listener("click", function PasswordInputLabelComponent_div_2_cds_tooltip_6_Template_button_click_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.handleTogglePasswordVisibility($event));
    });
    \u0275\u0275template(3, PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_3_Template, 1, 0, "svg", 20)(4, PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_4_Template, 1, 0, "svg", 21);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("description", ctx_r0.passwordIsVisible ? ctx_r0.hidePasswordLabel : ctx_r0.showPasswordLabel)("disabled", ctx_r0.disabled)("caret", ctx_r0.caret)("dropShadow", ctx_r0.dropShadow)("highContrast", ctx_r0.highContrast)("isOpen", ctx_r0.isOpen)("align", ctx_r0.align)("autoAlign", ctx_r0.autoAlign)("enterDelayMs", ctx_r0.enterDelayMs)("leaveDelayMs", ctx_r0.leaveDelayMs);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r0.disabled);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.passwordIsVisible);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.passwordIsVisible);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_7_div_2_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_Template, 1, 1, null, 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_7_div_3_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_Template, 1, 1, null, 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "hr", 24);
    \u0275\u0275template(2, PasswordInputLabelComponent_div_2_ng_container_7_div_2_Template, 3, 2, "div", 25)(3, PasswordInputLabelComponent_div_2_ng_container_7_div_3_Template, 3, 2, "div", 25);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.helperText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 28);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_8_div_1_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_Template, 1, 1, null, 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(3, _c5, ctx_r0.disabled));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_Template, 1, 1, null, 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_Template, 1, 1, null, 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275template(1, PasswordInputLabelComponent_div_2_ng_container_8_div_1_Template, 3, 5, "div", 27)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_2_Template, 3, 2, "div", 25)(3, PasswordInputLabelComponent_div_2_ng_container_8_div_3_Template, 3, 2, "div", 25);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.skeleton && ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function PasswordInputLabelComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11, 1);
    \u0275\u0275template(3, PasswordInputLabelComponent_div_2__svg_svg_3_Template, 1, 0, "svg", 12)(4, PasswordInputLabelComponent_div_2__svg_svg_4_Template, 1, 0, "svg", 13);
    \u0275\u0275projection(5, 1);
    \u0275\u0275template(6, PasswordInputLabelComponent_div_2_cds_tooltip_6_Template, 5, 13, "cds-tooltip", 14)(7, PasswordInputLabelComponent_div_2_ng_container_7_Template, 4, 2, "ng-container", 2);
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, PasswordInputLabelComponent_div_2_ng_container_8_Template, 4, 3, "ng-container", 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(7, _c8, ctx_r0.warn));
    \u0275\u0275attribute("data-invalid", ctx_r0.invalid ? true : null);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.skeleton);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.fluid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.fluid);
  }
}
var _c11 = [[["input"], ["textarea"], ["div"]], "*"];
var _c12 = ["input,textarea,div", "*"];
var _c13 = (a0, a1) => ({
  "cds--label--disabled": a0,
  "cds--skeleton": a1
});
function Label_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0);
  }
}
function Label_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275projection(0, 1);
  }
}
function Label_ng_container_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "cds-textarea-label", 7);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    const inputContentTemplate_r2 = \u0275\u0275reference(1);
    const labelContentTemplate_r3 = \u0275\u0275reference(3);
    \u0275\u0275advance();
    \u0275\u0275property("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    \u0275\u0275ariaProperty("ariaLabel", ctx_r0.ariaLabel);
    \u0275\u0275property("labelTemplate", labelContentTemplate_r3)("textAreaTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "cds-text-label", 8);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    const inputContentTemplate_r2 = \u0275\u0275reference(1);
    const labelContentTemplate_r3 = \u0275\u0275reference(3);
    \u0275\u0275advance();
    \u0275\u0275property("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    \u0275\u0275ariaProperty("ariaLabel", ctx_r0.ariaLabel);
    \u0275\u0275property("labelTemplate", labelContentTemplate_r3)("textInputTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275element(1, "cds-password-label", 9);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    const inputContentTemplate_r2 = \u0275\u0275reference(1);
    const labelContentTemplate_r3 = \u0275\u0275reference(3);
    \u0275\u0275advance();
    \u0275\u0275property("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    \u0275\u0275ariaProperty("ariaLabel", ctx_r0.ariaLabel);
    \u0275\u0275property("labelTemplate", labelContentTemplate_r3)("passwordInputTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_8_ng_template_1_Template(rf, ctx) {
}
function Label_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275template(1, Label_ng_container_8_ng_template_1_Template, 0, 0, "ng-template", 10);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    \u0275\u0275nextContext();
    const default_r4 = \u0275\u0275reference(10);
    \u0275\u0275advance();
    \u0275\u0275property("ngTemplateOutlet", default_r4);
  }
}
function Label_ng_template_9_ng_template_1_Template(rf, ctx) {
}
function Label_ng_template_9__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 17);
  }
}
function Label_ng_template_9__svg_svg_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275element(0, "svg", 18);
  }
}
function Label_ng_template_9_ng_template_6_Template(rf, ctx) {
}
function Label_ng_template_9_div_7_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.helperText);
  }
}
function Label_ng_template_9_div_7_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_7_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, Label_ng_template_9_div_7_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function Label_ng_template_9_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, Label_ng_template_9_div_7_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_7_2_Template, 1, 1, null, 20);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(3, _c5, ctx_r0.disabled));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function Label_ng_template_9_div_8_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.invalidText);
  }
}
function Label_ng_template_9_div_8_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_8_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, Label_ng_template_9_div_8_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function Label_ng_template_9_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275template(1, Label_ng_template_9_div_8_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_8_2_Template, 1, 1, null, 20);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function Label_ng_template_9_div_9_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.warnText);
  }
}
function Label_ng_template_9_div_9_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_9_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, Label_ng_template_9_div_9_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function Label_ng_template_9_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275template(1, Label_ng_template_9_div_9_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_9_2_Template, 1, 1, null, 20);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function Label_ng_template_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "label", 11);
    \u0275\u0275template(1, Label_ng_template_9_ng_template_1_Template, 0, 0, "ng-template", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "div", 12, 3);
    \u0275\u0275template(4, Label_ng_template_9__svg_svg_4_Template, 1, 0, "svg", 13)(5, Label_ng_template_9__svg_svg_5_Template, 1, 0, "svg", 14)(6, Label_ng_template_9_ng_template_6_Template, 0, 0, "ng-template", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275template(7, Label_ng_template_9_div_7_Template, 3, 5, "div", 15)(8, Label_ng_template_9_div_8_Template, 3, 2, "div", 16)(9, Label_ng_template_9_div_9_Template, 3, 2, "div", 16);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    const inputContentTemplate_r2 = \u0275\u0275reference(1);
    const labelContentTemplate_r3 = \u0275\u0275reference(3);
    \u0275\u0275property("for", ctx_r0.labelInputID)("ngClass", \u0275\u0275pureFunction2(12, _c13, ctx_r0.disabled, ctx_r0.skeleton));
    \u0275\u0275attribute("aria-label", ctx_r0.ariaLabel);
    \u0275\u0275advance();
    \u0275\u0275property("ngTemplateOutlet", labelContentTemplate_r3);
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(15, _c8, ctx_r0.warn));
    \u0275\u0275attribute("data-invalid", ctx_r0.invalid ? true : null);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngTemplateOutlet", inputContentTemplate_r2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.skeleton && ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
var TextInput = class {
  constructor() {
    this.theme = "dark";
    this.size = "md";
    this.inputClass = true;
    this.invalid = false;
    this.warn = false;
    this.skeleton = false;
  }
  /**
   * @todo - remove `cds--text-input--${size}` classes in v12
   */
  get isSizeSm() {
    return this.size === "sm";
  }
  get isSizeMd() {
    return this.size === "md";
  }
  get isSizelg() {
    return this.size === "lg";
  }
  // Size
  get sizeSm() {
    return this.size === "sm";
  }
  get sizeMd() {
    return this.size === "md";
  }
  get sizelg() {
    return this.size === "lg";
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttribute() {
    return this.invalid ? true : void 0;
  }
};
TextInput.\u0275fac = function TextInput_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextInput)();
};
TextInput.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: TextInput,
  selectors: [["", "cdsText", ""], ["", "ibmText", ""]],
  hostVars: 23,
  hostBindings: function TextInput_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275attribute("data-invalid", ctx.getInvalidAttribute);
      \u0275\u0275classProp("cds--text-input", ctx.inputClass)("cds--text-input--sm", ctx.isSizeSm)("cds--text-input--md", ctx.isSizeMd)("cds--text-input--lg", ctx.isSizelg)("cds--layout--size-sm", ctx.sizeSm)("cds--layout--size-md", ctx.sizeMd)("cds--layout--size-lg", ctx.sizelg)("cds--text-input--invalid", ctx.invalid)("cds--text-input--warning", ctx.warn)("cds--skeleton", ctx.skeleton)("cds--text-input--light", ctx.isLightTheme);
    }
  },
  inputs: {
    theme: "theme",
    size: "size",
    invalid: "invalid",
    warn: "warn",
    skeleton: "skeleton"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextInput, [{
    type: Directive,
    args: [{
      selector: "[cdsText], [ibmText]"
    }]
  }], null, {
    theme: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    inputClass: [{
      type: HostBinding,
      args: ["class.cds--text-input"]
    }],
    isSizeSm: [{
      type: HostBinding,
      args: ["class.cds--text-input--sm"]
    }],
    isSizeMd: [{
      type: HostBinding,
      args: ["class.cds--text-input--md"]
    }],
    isSizelg: [{
      type: HostBinding,
      args: ["class.cds--text-input--lg"]
    }],
    sizeSm: [{
      type: HostBinding,
      args: ["class.cds--layout--size-sm"]
    }],
    sizeMd: [{
      type: HostBinding,
      args: ["class.cds--layout--size-md"]
    }],
    sizelg: [{
      type: HostBinding,
      args: ["class.cds--layout--size-lg"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-input--invalid"]
    }, {
      type: Input
    }],
    warn: [{
      type: HostBinding,
      args: ["class.cds--text-input--warning"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-input--light"]
    }],
    getInvalidAttribute: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var TextArea = class {
  constructor() {
    this.theme = "dark";
    this.baseClass = true;
    this.invalid = false;
    this.skeleton = false;
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttr() {
    return this.invalid ? true : void 0;
  }
};
TextArea.\u0275fac = function TextArea_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextArea)();
};
TextArea.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: TextArea,
  selectors: [["", "cdsTextArea", ""], ["", "ibmTextArea", ""]],
  hostVars: 9,
  hostBindings: function TextArea_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275attribute("data-invalid", ctx.getInvalidAttr);
      \u0275\u0275classProp("cds--text-area", ctx.baseClass)("cds--text-area--invalid", ctx.invalid)("cds--skeleton", ctx.skeleton)("cds--text-area--light", ctx.isLightTheme);
    }
  },
  inputs: {
    theme: "theme",
    invalid: "invalid",
    skeleton: "skeleton"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextArea, [{
    type: Directive,
    args: [{
      selector: "[cdsTextArea], [ibmTextArea]"
    }]
  }], null, {
    theme: [{
      type: Input
    }],
    baseClass: [{
      type: HostBinding,
      args: ["class.cds--text-area"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-area--invalid"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-area--light"]
    }],
    getInvalidAttr: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var PasswordInput = class {
  constructor(elementRef, renderer) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.passwordInputClass = true;
    this.inputClass = true;
    this.invalid = false;
    this.warn = false;
    this.skeleton = false;
    this.theme = "dark";
    this.size = "md";
    this._type = "password";
  }
  set type(type) {
    if (type) {
      this._type = type;
      if (this.elementRef) {
        this.renderer.setAttribute(this.elementRef.nativeElement, "type", this._type);
      }
    }
  }
  /**
   * @todo - remove `cds--text-input--${size}` classes in v12
   */
  get isSizeSm() {
    return this.size === "sm";
  }
  get isSizeMd() {
    return this.size === "md";
  }
  get isSizelg() {
    return this.size === "lg";
  }
  // Size
  get sizeSm() {
    return this.size === "sm";
  }
  get sizeMd() {
    return this.size === "md";
  }
  get sizelg() {
    return this.size === "lg";
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttribute() {
    return this.invalid ? true : void 0;
  }
  ngAfterViewInit() {
    this.renderer.setAttribute(this.elementRef.nativeElement, "type", this._type);
  }
};
PasswordInput.\u0275fac = function PasswordInput_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PasswordInput)(\u0275\u0275directiveInject(ElementRef), \u0275\u0275directiveInject(Renderer2));
};
PasswordInput.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: PasswordInput,
  selectors: [["", "cdsPassword", ""], ["", "ibmPassword", ""]],
  hostVars: 25,
  hostBindings: function PasswordInput_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275attribute("data-invalid", ctx.getInvalidAttribute);
      \u0275\u0275classProp("cds--password-input", ctx.passwordInputClass)("cds--text-input--sm", ctx.isSizeSm)("cds--text-input--md", ctx.isSizeMd)("cds--text-input--lg", ctx.isSizelg)("cds--layout--size-sm", ctx.sizeSm)("cds--layout--size-md", ctx.sizeMd)("cds--layout--size-lg", ctx.sizelg)("cds--text-input--light", ctx.isLightTheme)("cds--text-input", ctx.inputClass)("cds--text-input--invalid", ctx.invalid)("cds--text-input--warning", ctx.warn)("cds--skeleton", ctx.skeleton);
    }
  },
  inputs: {
    type: "type",
    invalid: "invalid",
    warn: "warn",
    skeleton: "skeleton",
    theme: "theme",
    size: "size"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PasswordInput, [{
    type: Directive,
    args: [{
      selector: "[cdsPassword], [ibmPassword]"
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: Renderer2
    }];
  }, {
    type: [{
      type: Input
    }],
    passwordInputClass: [{
      type: HostBinding,
      args: ["class.cds--password-input"]
    }],
    isSizeSm: [{
      type: HostBinding,
      args: ["class.cds--text-input--sm"]
    }],
    isSizeMd: [{
      type: HostBinding,
      args: ["class.cds--text-input--md"]
    }],
    isSizelg: [{
      type: HostBinding,
      args: ["class.cds--text-input--lg"]
    }],
    sizeSm: [{
      type: HostBinding,
      args: ["class.cds--layout--size-sm"]
    }],
    sizeMd: [{
      type: HostBinding,
      args: ["class.cds--layout--size-md"]
    }],
    sizelg: [{
      type: HostBinding,
      args: ["class.cds--layout--size-lg"]
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-input--light"]
    }],
    inputClass: [{
      type: HostBinding,
      args: ["class.cds--text-input"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-input--invalid"]
    }, {
      type: Input
    }],
    warn: [{
      type: HostBinding,
      args: ["class.cds--text-input--warning"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    theme: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    getInvalidAttribute: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var TextareaLabelComponent = class _TextareaLabelComponent {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "ibm-textarea-" + _TextareaLabelComponent.labelCounter;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.fluid = false;
    this.labelClass = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("textarea")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("textarea");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TextareaLabelComponent.labelCounter = 0;
TextareaLabelComponent.\u0275fac = function TextareaLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextareaLabelComponent)(\u0275\u0275directiveInject(ChangeDetectorRef));
};
TextareaLabelComponent.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: TextareaLabelComponent,
  selectors: [["cds-textarea-label"], ["ibm-textarea-label"]],
  contentQueries: function TextareaLabelComponent_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      \u0275\u0275contentQuery(dirIndex, TextArea, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.textArea = _t.first);
    }
  },
  viewQuery: function TextareaLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 8,
  hostBindings: function TextareaLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--form-item", ctx.labelClass)("cds--text-area__wrapper--readonly", ctx.isReadonly)("cds--text-area--fluid", ctx.fluidClass)("cds--text-area--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    textAreaTemplate: "textAreaTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    fluid: "fluid"
  },
  standalone: false,
  ngContentSelectors: _c2,
  decls: 2,
  vars: 2,
  consts: [["labelContent", ""], ["wrapper", ""], ["textAreaContent", ""], [4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-area", "cds--skeleton"], [1, "cds--text-area__label-wrapper"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [1, "cds--text-area__wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-area__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-area__invalid-icon cds--text-area__invalid-icon--warning", 4, "ngIf"], [3, "ngTemplateOutlet"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-area__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-area__invalid-icon", "cds--text-area__invalid-icon--warning"], [1, "cds--text-area__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function TextareaLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c1);
      \u0275\u0275template(0, TextareaLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 3)(1, TextareaLabelComponent_ng_container_1_Template, 15, 17, "ng-container", 3);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.skeleton);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextareaLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-textarea-label, ibm-textarea-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-area cds--skeleton"></div>
		</ng-container>
		<ng-container *ngIf="!skeleton">
			<div class="cds--text-area__label-wrapper">
				<label
					[for]="labelInputID"
					[attr.aria-label]="ariaLabel"
					class="cds--label"
					[ngClass]="{
						'cds--label--disabled': disabled
					}">
					<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
					<ng-template #labelContent>
						<ng-content></ng-content>
					</ng-template>
				</label>
			</div>
			<div
				class="cds--text-area__wrapper"
				[ngClass]="{
					'cds--text-area__wrapper--warn': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="!fluid && invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-area__invalid-icon">
				</svg>
				<svg
					*ngIf="!fluid && !invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-area__invalid-icon cds--text-area__invalid-icon--warning">
				</svg>
				<ng-template *ngIf="textAreaTemplate; else textAreaContent" [ngTemplateOutlet]="textAreaTemplate"></ng-template>
				<ng-template #textAreaContent>
					<ng-content select="[cdsTextArea],[ibmTextArea],textarea"></ng-content>
				</ng-template>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-area__divider" />
					<div *ngIf="invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
						<svg
							cdsIcon="warning--filled"
							size="16"
							class="cds--text-area__invalid-icon">
						</svg>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
						<svg
							cdsIcon="warning--alt--filled"
							size="16"
							class="cds--text-area__invalid-icon cds--text-area__invalid-icon--warning">
						</svg>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{'cds--form__helper-text--disabled': disabled}">
					<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>
				<div *ngIf="invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>
				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</ng-container>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    textAreaTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: false
      }]
    }],
    textArea: [{
      type: ContentChild,
      args: [TextArea, {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-area__wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-area--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-area--fluid__skeleton"]
    }]
  });
})();
var TextInputLabelComponent = class _TextInputLabelComponent {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "ibm-text-input-" + _TextInputLabelComponent.labelCounter++;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.fluid = false;
    this.labelClass = true;
    this.textInputWrapper = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("input")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  ngAfterContentInit() {
    this.changeDetectorRef.detectChanges();
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TextInputLabelComponent.labelCounter = 0;
TextInputLabelComponent.\u0275fac = function TextInputLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextInputLabelComponent)(\u0275\u0275directiveInject(ChangeDetectorRef));
};
TextInputLabelComponent.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: TextInputLabelComponent,
  selectors: [["cds-text-label"], ["ibm-text-label"]],
  viewQuery: function TextInputLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 10,
  hostBindings: function TextInputLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--form-item", ctx.labelClass)("cds--text-input-wrapper", ctx.textInputWrapper)("cds--text-input-wrapper--readonly", ctx.isReadonly)("cds--text-input--fluid", ctx.fluidClass)("cds--text-input--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    textInputTemplate: "textInputTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    fluid: "fluid"
  },
  standalone: false,
  ngContentSelectors: _c7,
  decls: 3,
  vars: 3,
  consts: [["labelContent", ""], ["wrapper", ""], ["textInputContent", ""], [4, "ngIf"], ["class", "cds--label", 3, "for", "ngClass", 4, "ngIf"], ["class", "cds--text-input__field-outer-wrapper", 4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-input", "cds--skeleton"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"], [1, "cds--text-input__field-outer-wrapper"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--text-input__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function TextInputLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c6);
      \u0275\u0275template(0, TextInputLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 3)(1, TextInputLabelComponent_label_1_Template, 4, 7, "label", 4)(2, TextInputLabelComponent_div_2_Template, 10, 10, "div", 5);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.skeleton);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.skeleton);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextInputLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-text-label, ibm-text-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-input cds--skeleton"></div>
		</ng-container>
		<label
			*ngIf="!skeleton"
			[for]="labelInputID"
			[attr.aria-label]="ariaLabel"
			class="cds--label"
			[ngClass]="{
				'cds--label--disabled': disabled
			}">
			<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
			<ng-template #labelContent>
				<ng-content></ng-content>
			</ng-template>
		</label>
		<div *ngIf="!skeleton" class="cds--text-input__field-outer-wrapper">
			<div
				class="cds--text-input__field-wrapper"
				[ngClass]="{
					'cds--text-input__field-wrapper--warning': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="invalid && !warn"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-template *ngIf="textInputTemplate; else textInputContent" [ngTemplateOutlet]="textInputTemplate"></ng-template>
				<ng-template #textInputContent>
					<ng-content select="[cdsText],[ibmText],input[type=text],div"></ng-content>
				</ng-template>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-input__divider" />
					<div *ngIf="invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{'cds--form__helper-text--disabled': disabled}">
					<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>
				<div *ngIf="invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>
				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</div>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    textInputTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    textInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid__skeleton"]
    }]
  });
})();
var PasswordInputLabelComponent = class _PasswordInputLabelComponent extends BaseIconButton {
  /**
   * Constructor for PasswordInputLabelComponent.
   * @param changeDetectorRef - Reference to ChangeDetectorRef.
   */
  constructor(changeDetectorRef) {
    super();
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "cds-password-input-" + _PasswordInputLabelComponent.labelCounter++;
    this.inputType = "password";
    this.passwordIsVisible = false;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.hidePasswordLabel = "Hide password";
    this.showPasswordLabel = "Show password";
    this.fluid = false;
    this.labelClass = true;
    this.passwordInputWrapper = true;
    this.textInputWrapper = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("input")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Lifecycle hook called after the view has been initialized to set the ID of the input element
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
    }
  }
  /**
   * Function to check if a value is a TemplateRef.
   * @param value - Value to check.
   * @returns Whether the value is a TemplateRef.
   */
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
  /**
   * Handler for toggling password visibility.
   */
  handleTogglePasswordVisibility() {
    this.inputType = this.inputType === "password" ? "text" : "password";
    this.textInput.type = this.inputType;
    this.passwordIsVisible = this.inputType === "text";
  }
};
PasswordInputLabelComponent.labelCounter = 0;
PasswordInputLabelComponent.\u0275fac = function PasswordInputLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PasswordInputLabelComponent)(\u0275\u0275directiveInject(ChangeDetectorRef));
};
PasswordInputLabelComponent.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: PasswordInputLabelComponent,
  selectors: [["cds-password-label"], ["ibm-password-label"]],
  contentQueries: function PasswordInputLabelComponent_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      \u0275\u0275contentQuery(dirIndex, PasswordInput, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.textInput = _t.first);
    }
  },
  viewQuery: function PasswordInputLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 7);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 12,
  hostBindings: function PasswordInputLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--form-item", ctx.labelClass)("cds--password-input-wrapper", ctx.passwordInputWrapper)("cds--text-input-wrapper", ctx.textInputWrapper)("cds--text-input-wrapper--readonly", ctx.isReadonly)("cds--text-input--fluid", ctx.fluidClass)("cds--text-input--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    passwordInputTemplate: "passwordInputTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    hidePasswordLabel: "hidePasswordLabel",
    showPasswordLabel: "showPasswordLabel",
    fluid: "fluid"
  },
  standalone: false,
  features: [\u0275\u0275InheritDefinitionFeature],
  ngContentSelectors: _c10,
  decls: 3,
  vars: 3,
  consts: [["labelContent", ""], ["wrapper", ""], [4, "ngIf"], ["class", "cds--label", 3, "for", "ngClass", 4, "ngIf"], ["class", "cds--text-input__field-outer-wrapper", 4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-input", "cds--skeleton"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"], [1, "cds--text-input__field-outer-wrapper"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["class", "cds--toggle-password-tooltip", 3, "description", "disabled", "caret", "dropShadow", "highContrast", "isOpen", "align", "autoAlign", "enterDelayMs", "leaveDelayMs", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--toggle-password-tooltip", 3, "description", "disabled", "caret", "dropShadow", "highContrast", "isOpen", "align", "autoAlign", "enterDelayMs", "leaveDelayMs"], [1, "cds--tooltip-trigger__wrapper"], ["type", "button", 1, "cds--text-input--password__visibility__toggle", "cds--btn", "cds--tooltip__trigger", "cds--tooltip--a11y", 3, "click", "disabled"], ["cdsIcon", "view--off", "class", "cds--icon-visibility-off", "size", "16", 4, "ngIf"], ["cdsIcon", "view", "class", "cds--icon-visibility-on", "size", "16", 4, "ngIf"], ["cdsIcon", "view--off", "size", "16", 1, "cds--icon-visibility-off"], ["cdsIcon", "view", "size", "16", 1, "cds--icon-visibility-on"], [1, "cds--text-input__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function PasswordInputLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c9);
      \u0275\u0275template(0, PasswordInputLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 2)(1, PasswordInputLabelComponent_label_1_Template, 4, 7, "label", 3)(2, PasswordInputLabelComponent_div_2_Template, 9, 9, "div", 4);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.skeleton);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.skeleton);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective, Tooltip],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PasswordInputLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-password-label, ibm-password-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-input cds--skeleton"></div>
		</ng-container>
		<label
			*ngIf="!skeleton"
			[for]="labelInputID"
			[attr.aria-label]="ariaLabel"
			class="cds--label"
			[ngClass]="{
				'cds--label--disabled': disabled
			}">
			<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
			<ng-template #labelContent>
				<ng-content></ng-content>
			</ng-template>
		</label>

		<div *ngIf="!skeleton" class="cds--text-input__field-outer-wrapper">
			<div
			class="cds--text-input__field-wrapper"
			[ngClass]="{
				'cds--text-input__field-wrapper--warning': warn
			}"
			[attr.data-invalid]="invalid ? true : null"
			#wrapper>
				<svg
					*ngIf="!warn && invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-content select="[cdsPassword], [ibmPassword]"></ng-content>
				<cds-tooltip
					*ngIf="!skeleton"
					[description]="passwordIsVisible ? hidePasswordLabel : showPasswordLabel"
					[disabled]="disabled"
					[caret]="caret"
					[dropShadow]="dropShadow"
					[highContrast]="highContrast"
					[isOpen]="isOpen"
					[align]="align"
					[autoAlign]="autoAlign"
					[enterDelayMs]="enterDelayMs"
					[leaveDelayMs]="leaveDelayMs"
					class="cds--toggle-password-tooltip">
						<div class="cds--tooltip-trigger__wrapper">
							<button
								class="cds--text-input--password__visibility__toggle cds--btn cds--tooltip__trigger cds--tooltip--a11y"
								[disabled]="disabled"
								type="button"
								(click)="handleTogglePasswordVisibility($event)">
								<svg *ngIf="passwordIsVisible" cdsIcon="view--off" class="cds--icon-visibility-off" size="16"></svg>
								<svg *ngIf="!passwordIsVisible" cdsIcon="view" class="cds--icon-visibility-on" size="16"></svg>
							</button>
						</div>
				</cds-tooltip>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-input__divider" />
					<div *ngIf="!warn && invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{ invalidText }}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{ warnText }}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="!skeleton && helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{ 'cds--form__helper-text--disabled': disabled }">
					<ng-container *ngIf="!isTemplate(helperText)">{{ helperText }}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>

				<div *ngIf="!warn && invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{ invalidText }}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>

				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{ warnText }}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</div>
    `
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    textInput: [{
      type: ContentChild,
      args: [PasswordInput]
    }],
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    passwordInputTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    hidePasswordLabel: [{
      type: Input
    }],
    showPasswordLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: true
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    passwordInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--password-input-wrapper"]
    }],
    textInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid__skeleton"]
    }]
  });
})();
var Label = class _Label {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = `cds-label-${_Label.labelCounter++}`;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
  }
  get labelClass() {
    return this.type === void 0;
  }
  /**
   * Update wrapper class if a textarea is hosted.
   */
  ngAfterContentInit() {
    if (this.textArea) {
      this.type = "TextArea";
    } else if (this.textInput) {
      this.type = "TextInput";
    } else if (this.passwordInput) {
      this.type = "PasswordInput";
    }
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input,textarea");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
Label.labelCounter = 0;
Label.\u0275fac = function Label_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Label)(\u0275\u0275directiveInject(ChangeDetectorRef));
};
Label.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: Label,
  selectors: [["cds-label"], ["ibm-label"]],
  contentQueries: function Label_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      \u0275\u0275contentQuery(dirIndex, TextArea, 5);
      \u0275\u0275contentQuery(dirIndex, TextInput, 5);
      \u0275\u0275contentQuery(dirIndex, PasswordInput, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.textArea = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.textInput = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.passwordInput = _t.first);
    }
  },
  viewQuery: function Label_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 2,
  hostBindings: function Label_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--form-item", ctx.labelClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel"
  },
  standalone: false,
  ngContentSelectors: _c12,
  decls: 11,
  vars: 4,
  consts: [["inputContentTemplate", ""], ["labelContentTemplate", ""], ["default", ""], ["wrapper", ""], [3, "ngSwitch"], [4, "ngSwitchCase"], [4, "ngSwitchDefault"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "textAreaTemplate"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "textInputTemplate"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "passwordInputTemplate"], [3, "ngTemplateOutlet"], [1, "cds--label", 3, "for", "ngClass"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], ["class", "cds--form-requirement", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--form__helper-text", 3, "ngClass"], [4, "ngIf"], [1, "cds--form-requirement"]],
  template: function Label_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c11);
      \u0275\u0275template(0, Label_ng_template_0_Template, 1, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor)(2, Label_ng_template_2_Template, 1, 0, "ng-template", null, 1, \u0275\u0275templateRefExtractor);
      \u0275\u0275elementContainerStart(4, 4);
      \u0275\u0275template(5, Label_ng_container_5_Template, 2, 11, "ng-container", 5)(6, Label_ng_container_6_Template, 2, 11, "ng-container", 5)(7, Label_ng_container_7_Template, 2, 11, "ng-container", 5)(8, Label_ng_container_8_Template, 2, 1, "ng-container", 6);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275template(9, Label_ng_template_9_Template, 10, 17, "ng-template", null, 2, \u0275\u0275templateRefExtractor);
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275property("ngSwitch", ctx.type);
      \u0275\u0275advance();
      \u0275\u0275property("ngSwitchCase", "TextArea");
      \u0275\u0275advance();
      \u0275\u0275property("ngSwitchCase", "TextInput");
      \u0275\u0275advance();
      \u0275\u0275property("ngSwitchCase", "PasswordInput");
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, NgSwitch, NgSwitchCase, NgSwitchDefault, IconDirective, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Label, [{
    type: Component,
    args: [{
      selector: "cds-label, ibm-label",
      template: `
		<ng-template #inputContentTemplate>
			<ng-content select="input,textarea,div"></ng-content>
		</ng-template>

		<ng-template #labelContentTemplate>
			<ng-content></ng-content>
		</ng-template>

		<ng-container [ngSwitch]="type">
			<ng-container *ngSwitchCase="'TextArea'">
				<cds-textarea-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[textAreaTemplate]="inputContentTemplate">
				</cds-textarea-label>
			</ng-container>
			<ng-container *ngSwitchCase="'TextInput'">
				<cds-text-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[textInputTemplate]="inputContentTemplate">
				</cds-text-label>
			</ng-container>
			<ng-container *ngSwitchCase="'PasswordInput'">
				<cds-password-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[passwordInputTemplate]="inputContentTemplate">
				</cds-password-label>
			</ng-container>
			<ng-container *ngSwitchDefault>
				<ng-template [ngTemplateOutlet]="default"></ng-template>
			</ng-container>
		</ng-container>

		<ng-template #default>
			<label
				[for]="labelInputID"
				[attr.aria-label]="ariaLabel"
				class="cds--label"
				[ngClass]="{
					'cds--label--disabled': disabled,
					'cds--skeleton': skeleton
				}">
				<ng-template [ngTemplateOutlet]="labelContentTemplate"></ng-template>
			</label>
			<div
				class="cds--text-input__field-wrapper"
				[ngClass]="{
					'cds--text-input__field-wrapper--warning': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-template [ngTemplateOutlet]="inputContentTemplate"></ng-template>
			</div>
			<div
				*ngIf="!skeleton && helperText && !invalid && !warn"
				class="cds--form__helper-text"
				[ngClass]="{'cds--form__helper-text--disabled': disabled}">
				<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
				<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
			</div>
			<div *ngIf="invalid" class="cds--form-requirement">
				<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
				<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
			</div>
			<div *ngIf="!invalid && warn" class="cds--form-requirement">
				<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
				<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
			</div>
		</ng-template>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper"]
    }],
    textArea: [{
      type: ContentChild,
      args: [TextArea]
    }],
    textInput: [{
      type: ContentChild,
      args: [TextInput, {
        static: false
      }]
    }],
    passwordInput: [{
      type: ContentChild,
      args: [PasswordInput, {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }]
  });
})();
var InputModule = class {
};
InputModule.\u0275fac = function InputModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || InputModule)();
};
InputModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: InputModule,
  declarations: [Label, TextInput, TextArea, PasswordInput, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
  imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule],
  exports: [Label, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent, TextInput, TextArea, PasswordInput]
});
InputModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InputModule, [{
    type: NgModule,
    args: [{
      declarations: [Label, TextInput, TextArea, PasswordInput, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
      exports: [Label, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent, TextInput, TextArea, PasswordInput],
      imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule]
    }]
  }], null, null);
})();

// src/app/login/login.component.ts
function LoginComponent_Conditional_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.error(), " ");
  }
}
var LoginComponent = class _LoginComponent {
  formBuilder = inject(FormBuilder);
  auth = inject(AuthService);
  router = inject(Router);
  firebaseTest = inject(FirebaseTestService);
  loginForm;
  isSubmitting = signal(false, ...ngDevMode ? [{ debugName: "isSubmitting" }] : []);
  error = signal("", ...ngDevMode ? [{ debugName: "error" }] : []);
  constructor() {
    this.loginForm = this.formBuilder.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required]]
    });
  }
  ngOnInit() {
    this.firebaseTest.testFirebaseConnection();
  }
  async onSubmit() {
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }
    this.isSubmitting.set(true);
    this.error.set("");
    try {
      const { email, password } = this.loginForm.value;
      console.log("Form values:", { email, password: password ? "[REDACTED]" : "empty" });
      await this.auth.signIn(email, password);
      console.log("Sign in successful, navigating to projects...");
      setTimeout(async () => {
        await this.router.navigate(["/projects"]);
      }, 100);
    } catch (e) {
      console.error("Login component error:", e);
      let errorMessage = "Sign-in failed";
      if (e?.code) {
        switch (e.code) {
          case "auth/user-not-found":
            errorMessage = "No account found with this email address";
            break;
          case "auth/wrong-password":
            errorMessage = "Incorrect password";
            break;
          case "auth/invalid-email":
            errorMessage = "Invalid email address";
            break;
          case "auth/user-disabled":
            errorMessage = "This account has been disabled";
            break;
          case "auth/too-many-requests":
            errorMessage = "Too many failed attempts. Please try again later";
            break;
          case "auth/network-request-failed":
            errorMessage = "Network error. Please check your connection";
            break;
          default:
            errorMessage = `Authentication error: ${e.code}`;
        }
      } else if (e?.message) {
        errorMessage = e.message;
      }
      this.error.set(errorMessage);
    } finally {
      this.isSubmitting.set(false);
    }
  }
  getEmailErrorMessage() {
    const emailControl = this.loginForm.get("email");
    if (emailControl?.hasError("required")) {
      return "Email is required";
    }
    if (emailControl?.hasError("email")) {
      return "Please enter a valid email address";
    }
    return "";
  }
  getPasswordErrorMessage() {
    const passwordControl = this.loginForm.get("password");
    if (passwordControl?.hasError("required")) {
      return "Password is required";
    }
    return "";
  }
  isEmailInvalid() {
    const emailControl = this.loginForm.get("email");
    return !!(emailControl?.invalid && (emailControl?.dirty || emailControl?.touched));
  }
  isPasswordInvalid() {
    const passwordControl = this.loginForm.get("password");
    return !!(passwordControl?.invalid && (passwordControl?.dirty || passwordControl?.touched));
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 17, vars: 10, consts: [[1, "login-container"], [1, "login-form-wrapper"], [1, "login-title"], [1, "login-form", 3, "ngSubmit", "formGroup"], [1, "form-field"], [3, "invalid", "invalidText"], ["cdsText", "", "type", "email", "formControlName", "email", "placeholder", "Enter your email", "autocomplete", "email", 3, "invalid"], ["cdsPassword", "", "formControlName", "password", "placeholder", "Enter your password", "autocomplete", "current-password", 3, "invalid"], [1, "error-message"], [1, "form-actions"], ["cdsButton", "primary", "type", "submit", 1, "login-button", 3, "disabled"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1", 2);
      \u0275\u0275text(3, "Sign In");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "form", 3);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_4_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(5, "div", 4)(6, "cds-text-label", 5);
      \u0275\u0275text(7, " Email ");
      \u0275\u0275element(8, "input", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "div", 4)(10, "cds-password-label", 5);
      \u0275\u0275text(11, " Password ");
      \u0275\u0275element(12, "input", 7);
      \u0275\u0275elementEnd()();
      \u0275\u0275conditionalCreate(13, LoginComponent_Conditional_13_Template, 2, 1, "div", 8);
      \u0275\u0275elementStart(14, "div", 9)(15, "button", 10);
      \u0275\u0275text(16);
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("invalid", ctx.isEmailInvalid())("invalidText", ctx.getEmailErrorMessage());
      \u0275\u0275advance(2);
      \u0275\u0275property("invalid", ctx.isEmailInvalid());
      \u0275\u0275advance(2);
      \u0275\u0275property("invalid", ctx.isPasswordInvalid())("invalidText", ctx.getPasswordErrorMessage());
      \u0275\u0275advance(2);
      \u0275\u0275property("invalid", ctx.isPasswordInvalid());
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.error() ? 13 : -1);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.isSubmitting());
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isSubmitting() ? "Signing In..." : "Sign In", " ");
    }
  }, dependencies: [
    CommonModule,
    ReactiveFormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    FormGroupDirective,
    FormControlName,
    InputModule,
    TextInputLabelComponent,
    PasswordInputLabelComponent,
    TextInput,
    PasswordInput,
    ButtonModule,
    Button
  ], styles: ["\n\n.login-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 1rem;\n  background:\n    linear-gradient(\n      135deg,\n      var(--cds-background) 0%,\n      var(--cds-layer-01) 100%);\n}\n.login-form-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  padding: 2rem;\n  background-color: var(--cds-layer-01);\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid var(--cds-border-subtle-01);\n}\n@media (min-width: 42rem) {\n  .login-form-wrapper[_ngcontent-%COMP%] {\n    padding: 2.5rem;\n    max-width: 420px;\n  }\n}\n.login-title[_ngcontent-%COMP%] {\n  font-size: var(--cds-heading-04-font-size, 1.75rem);\n  font-weight: var(--cds-heading-04-font-weight, 400);\n  line-height: var(--cds-heading-04-line-height, 1.28572);\n  letter-spacing: var(--cds-heading-04-letter-spacing, 0);\n  color: var(--cds-text-primary);\n  margin-bottom: 2rem;\n  text-align: center;\n  font-weight: 600;\n}\n.login-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.form-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.form-actions[_ngcontent-%COMP%] {\n  margin-top: 1.5rem;\n}\n.error-message[_ngcontent-%COMP%] {\n  font-size: var(--cds-body-01-font-size, 0.875rem);\n  font-weight: var(--cds-body-01-font-weight, 400);\n  line-height: var(--cds-body-01-line-height, 1.42857);\n  letter-spacing: var(--cds-body-01-letter-spacing, 0.16px);\n  color: var(--cds-text-error);\n  background-color: var(--cds-support-error-inverse);\n  border: 1px solid var(--cds-support-error);\n  border-radius: 4px;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n.login-button[_ngcontent-%COMP%] {\n  width: 100%;\n  justify-content: center;\n  height: 48px;\n  font-weight: 600;\n}\n.login-button[_ngcontent-%COMP%]:disabled {\n  cursor: not-allowed;\n}\n/*# sourceMappingURL=login.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{ selector: "app-login", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      InputModule,
      ButtonModule
    ], template: `<div class="login-container">
  <div class="login-form-wrapper">
    <h1 class="login-title">Sign In</h1>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-field">
        <cds-text-label
          [invalid]="isEmailInvalid()"
          [invalidText]="getEmailErrorMessage()">
          Email
          <input
            cdsText
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            [invalid]="isEmailInvalid()"
            autocomplete="email">
        </cds-text-label>
      </div>

      <div class="form-field">
        <cds-password-label
          [invalid]="isPasswordInvalid()"
          [invalidText]="getPasswordErrorMessage()">
          Password
          <input
            cdsPassword
            formControlName="password"
            placeholder="Enter your password"
            [invalid]="isPasswordInvalid()"
            autocomplete="current-password">
        </cds-password-label>
      </div>

      @if (error()) {
        <div class="error-message">
          {{ error() }}
        </div>
      }

      <div class="form-actions">
        <button
          cdsButton="primary"
          type="submit"
          [disabled]="isSubmitting()"
          class="login-button">
          {{ isSubmitting() ? 'Signing In...' : 'Sign In' }}
        </button>
      </div>
    </form>
  </div>
</div>
`, styles: ["/* src/app/login/login.component.scss */\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 1rem;\n  background:\n    linear-gradient(\n      135deg,\n      var(--cds-background) 0%,\n      var(--cds-layer-01) 100%);\n}\n.login-form-wrapper {\n  width: 100%;\n  max-width: 400px;\n  padding: 2rem;\n  background-color: var(--cds-layer-01);\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid var(--cds-border-subtle-01);\n}\n@media (min-width: 42rem) {\n  .login-form-wrapper {\n    padding: 2.5rem;\n    max-width: 420px;\n  }\n}\n.login-title {\n  font-size: var(--cds-heading-04-font-size, 1.75rem);\n  font-weight: var(--cds-heading-04-font-weight, 400);\n  line-height: var(--cds-heading-04-line-height, 1.28572);\n  letter-spacing: var(--cds-heading-04-letter-spacing, 0);\n  color: var(--cds-text-primary);\n  margin-bottom: 2rem;\n  text-align: center;\n  font-weight: 600;\n}\n.login-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.form-field {\n  width: 100%;\n}\n.form-actions {\n  margin-top: 1.5rem;\n}\n.error-message {\n  font-size: var(--cds-body-01-font-size, 0.875rem);\n  font-weight: var(--cds-body-01-font-weight, 400);\n  line-height: var(--cds-body-01-line-height, 1.42857);\n  letter-spacing: var(--cds-body-01-letter-spacing, 0.16px);\n  color: var(--cds-text-error);\n  background-color: var(--cds-support-error-inverse);\n  border: 1px solid var(--cds-support-error);\n  border-radius: 4px;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n.login-button {\n  width: 100%;\n  justify-content: center;\n  height: 48px;\n  font-weight: 600;\n}\n.login-button:disabled {\n  cursor: not-allowed;\n}\n/*# sourceMappingURL=login.component.css.map */\n"] }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/login/login.component.ts", lineNumber: 24 });
})();
export {
  LoginComponent
};
//# sourceMappingURL=chunk-AU74Z7LT.js.map
