{"version": 3, "sources": ["../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-loading.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, Input, HostBinding, NgModule } from '@angular/core';\nimport * as i1 from 'carbon-components-angular/i18n';\nimport { I18nModule } from 'carbon-components-angular/i18n';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { LoadingModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-loading--basic)\n */\nconst _c0 = (a0, a1, a2) => ({\n  \"cds--loading--small\": a0,\n  \"cds--loading--stop\": a1,\n  \"cds--loading-overlay--stop\": a2\n});\nclass Loading {\n  constructor(i18n) {\n    this.i18n = i18n;\n    /**\n     * Accessible title for the loading circle.\n     * Defaults to the `LOADING.TITLE` value from the i18n service.\n     */\n    this.title = this.i18n.get().LOADING.TITLE;\n    /**\n     * set to `false` to stop the loading animation\n     */\n    this.isActive = true;\n    /**\n     * Specify the size of the button\n     */\n    this.size = \"normal\";\n    /**\n     * Set to `true` to make loader with an overlay.\n     */\n    this.overlay = false;\n  }\n}\nLoading.ɵfac = function Loading_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || Loading)(i0.ɵɵdirectiveInject(i1.I18n));\n};\nLoading.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Loading,\n  selectors: [[\"cds-loading\"], [\"ibm-loading\"]],\n  hostVars: 2,\n  hostBindings: function Loading_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--loading-overlay\", ctx.overlay);\n    }\n  },\n  inputs: {\n    title: \"title\",\n    isActive: \"isActive\",\n    size: \"size\",\n    overlay: \"overlay\"\n  },\n  standalone: false,\n  decls: 5,\n  vars: 6,\n  consts: [[1, \"cds--loading\", 3, \"ngClass\"], [\"viewBox\", \"0 0 100 100\", 1, \"cds--loading__svg\"], [\"cx\", \"50%\", \"cy\", \"50%\", \"r\", \"44\", 1, \"cds--loading__stroke\"]],\n  template: function Loading_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(1, \"svg\", 1)(2, \"title\");\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(4, \"circle\", 2);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c0, ctx.size === \"sm\", !ctx.isActive && !ctx.overlay, !ctx.isActive && ctx.overlay));\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.title);\n    }\n  },\n  dependencies: [i2.NgClass],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Loading, [{\n    type: Component,\n    args: [{\n      selector: \"cds-loading, ibm-loading\",\n      template: `\n\t\t<div\n\t\t\t[ngClass]=\"{\n\t\t\t\t'cds--loading--small': size === 'sm',\n\t\t\t\t'cds--loading--stop': !isActive && !overlay,\n\t\t\t\t'cds--loading-overlay--stop': !isActive && overlay\n\t\t\t}\"\n\t\t\tclass=\"cds--loading\">\n\t\t\t<svg class=\"cds--loading__svg\" viewBox=\"0 0 100 100\">\n\t\t\t\t<title>{{title}}</title>\n\t\t\t\t<circle class=\"cds--loading__stroke\" cx=\"50%\" cy=\"50%\" r=\"44\" />\n\t\t\t</svg>\n\t\t</div>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i1.I18n\n    }];\n  }, {\n    title: [{\n      type: Input\n    }],\n    isActive: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    overlay: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"class.cds--loading-overlay\"]\n    }]\n  });\n})();\nclass LoadingModule {}\nLoadingModule.ɵfac = function LoadingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || LoadingModule)();\n};\nLoadingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LoadingModule,\n  declarations: [Loading],\n  imports: [CommonModule, I18nModule],\n  exports: [Loading]\n});\nLoadingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, I18nModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoadingModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [Loading],\n      exports: [Loading],\n      imports: [CommonModule, I18nModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Loading, LoadingModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,8BAA8B;AAChC;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,MAAM;AAChB,SAAK,OAAO;AAKZ,SAAK,QAAQ,KAAK,KAAK,IAAI,EAAE,QAAQ;AAIrC,SAAK,WAAW;AAIhB,SAAK,OAAO;AAIZ,SAAK,UAAU;AAAA,EACjB;AACF;AACA,QAAQ,OAAO,SAAS,gBAAgB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,SAAY,kBAAqB,IAAI,CAAC;AACzE;AACA,QAAQ,OAAyB,kBAAkB;AAAA,EACjD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,GAAG,CAAC,aAAa,CAAC;AAAA,EAC5C,UAAU;AAAA,EACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,wBAAwB,IAAI,OAAO;AAAA,IACpD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,SAAS,GAAG,CAAC,WAAW,eAAe,GAAG,mBAAmB,GAAG,CAAC,MAAM,OAAO,MAAM,OAAO,KAAK,MAAM,GAAG,sBAAsB,CAAC;AAAA,EAChK,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,eAAe;AAClB,MAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO;AACzC,MAAG,OAAO,CAAC;AACX,MAAG,aAAa;AAChB,MAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,MAAG,aAAa,EAAE;AAAA,IACpB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,IAAI,SAAS,MAAM,CAAC,IAAI,YAAY,CAAC,IAAI,SAAS,CAAC,IAAI,YAAY,IAAI,OAAO,CAAC;AACnI,MAAG,UAAU,CAAC;AACd,MAAG,kBAAkB,IAAI,KAAK;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,OAAO;AAAA,EACzB,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAoB;AAAC;AACrB,cAAc,OAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAe;AAClD;AACA,cAAc,OAAyB,iBAAiB;AAAA,EACtD,MAAM;AAAA,EACN,cAAc,CAAC,OAAO;AAAA,EACtB,SAAS,CAAC,cAAc,UAAU;AAAA,EAClC,SAAS,CAAC,OAAO;AACnB,CAAC;AACD,cAAc,OAAyB,iBAAiB;AAAA,EACtD,SAAS,CAAC,cAAc,UAAU;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,OAAO;AAAA,MACtB,SAAS,CAAC,OAAO;AAAA,MACjB,SAAS,CAAC,cAAc,UAAU;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}