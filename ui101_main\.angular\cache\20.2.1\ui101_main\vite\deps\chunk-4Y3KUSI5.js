import {
  Injectable,
  Ng<PERSON><PERSON>ule,
  Ng<PERSON><PERSON>,
  Optional,
  Pi<PERSON>,
  SkipSelf,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵinject
} from "./chunk-6YWT35PS.js";
import {
  fromEvent,
  iif,
  isObservable
} from "./chunk-OSECCFIU.js";
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscription,
  from,
  map
} from "./chunk-PHHPLELC.js";

// node_modules/@carbon/utils-position/index.js
var _a;
var PLACEMENTS;
(function(PLACEMENTS2) {
  PLACEMENTS2["LEFT"] = "left";
  PLACEMENTS2["RIGHT"] = "right";
  PLACEMENTS2["TOP"] = "top";
  PLACEMENTS2["BOTTOM"] = "bottom";
})(PLACEMENTS || (PLACEMENTS = {}));
var defaultPositions = (_a = {}, _a[PLACEMENTS.LEFT] = function(referenceOffset, target, referenceRect) {
  return {
    top: referenceOffset.top - Math.round(target.offsetHeight / 2) + Math.round(referenceRect.height / 2),
    left: Math.round(referenceOffset.left - target.offsetWidth)
  };
}, _a[PLACEMENTS.RIGHT] = function(referenceOffset, target, referenceRect) {
  return {
    top: referenceOffset.top - Math.round(target.offsetHeight / 2) + Math.round(referenceRect.height / 2),
    left: Math.round(referenceOffset.left + referenceRect.width)
  };
}, _a[PLACEMENTS.TOP] = function(referenceOffset, target, referenceRect) {
  return {
    top: Math.round(referenceOffset.top - target.offsetHeight),
    left: referenceOffset.left - Math.round(target.offsetWidth / 2) + Math.round(referenceRect.width / 2)
  };
}, _a[PLACEMENTS.BOTTOM] = function(referenceOffset, target, referenceRect) {
  return {
    top: Math.round(referenceOffset.top + referenceRect.height),
    left: referenceOffset.left - Math.round(target.offsetWidth / 2) + Math.round(referenceRect.width / 2)
  };
}, _a);
var windowRef = typeof window !== "undefined" ? window : {
  innerHeight: 0,
  scrollY: 0,
  innerWidth: 0,
  scrollX: 0
};
var Position = (
  /** @class */
  (function() {
    function Position2(positions) {
      if (positions === void 0) {
        positions = {};
      }
      this.positions = defaultPositions;
      this.positions = Object.assign({}, defaultPositions, positions);
    }
    Position2.prototype.getRelativeOffset = function(target) {
      var offsets = {
        left: target.offsetLeft,
        top: target.offsetTop
      };
      while (target.offsetParent && getComputedStyle(target.offsetParent).position === "static") {
        offsets.left += target.offsetLeft;
        offsets.top += target.offsetTop;
        target = target.offsetParent;
      }
      return offsets;
    };
    Position2.prototype.getAbsoluteOffset = function(target) {
      var currentNode = target;
      var margins = {
        top: 0,
        left: 0
      };
      while (currentNode.offsetParent) {
        var computed = getComputedStyle(currentNode.offsetParent);
        if (computed.position === "static" && computed.marginLeft && computed.marginTop) {
          if (parseInt(computed.marginTop, 10)) {
            margins.top += parseInt(computed.marginTop, 10);
          }
          if (parseInt(computed.marginLeft, 10)) {
            margins.left += parseInt(computed.marginLeft, 10);
          }
        }
        currentNode = currentNode.offsetParent;
      }
      var targetRect = target.getBoundingClientRect();
      var relativeRect = document.body.getBoundingClientRect();
      return {
        top: targetRect.top - relativeRect.top + margins.top,
        left: targetRect.left - relativeRect.left + margins.left
      };
    };
    Position2.prototype.findRelative = function(reference, target, placement) {
      var referenceOffset = this.getRelativeOffset(reference);
      var referenceRect = reference.getBoundingClientRect();
      return this.calculatePosition(referenceOffset, referenceRect, target, placement);
    };
    Position2.prototype.findAbsolute = function(reference, target, placement) {
      var referenceOffset = this.getAbsoluteOffset(reference);
      var referenceRect = reference.getBoundingClientRect();
      return this.calculatePosition(referenceOffset, referenceRect, target, placement);
    };
    Position2.prototype.findPosition = function(reference, target, placement, offsetFunction) {
      if (offsetFunction === void 0) {
        offsetFunction = this.getAbsoluteOffset.bind(this);
      }
      var referenceOffset = offsetFunction(reference);
      var referenceRect = reference.getBoundingClientRect();
      return this.calculatePosition(referenceOffset, referenceRect, target, placement);
    };
    Position2.prototype.findPositionAt = function(offset, target, placement) {
      return this.calculatePosition(offset, { top: 0, left: 0, height: 0, width: 0 }, target, placement);
    };
    Position2.prototype.getPlacementBox = function(target, position2) {
      var targetBottom = target.offsetHeight + position2.top;
      var targetRight = target.offsetWidth + position2.left;
      return {
        top: position2.top,
        bottom: targetBottom,
        left: position2.left,
        right: targetRight
      };
    };
    Position2.prototype.addOffset = function(position2, top, left) {
      if (top === void 0) {
        top = 0;
      }
      if (left === void 0) {
        left = 0;
      }
      return Object.assign({}, position2, {
        top: position2.top + top,
        left: position2.left + left
      });
    };
    Position2.prototype.setElement = function(element, position2) {
      element.style.top = position2.top + "px";
      element.style.left = position2.left + "px";
    };
    Position2.prototype.findBestPlacement = function(reference, target, placements, containerFunction, positionFunction) {
      var _this = this;
      if (containerFunction === void 0) {
        containerFunction = this.defaultContainerFunction.bind(this);
      }
      if (positionFunction === void 0) {
        positionFunction = this.findPosition.bind(this);
      }
      var weightedPlacements = placements.map(function(placement) {
        var pos = positionFunction(reference, target, placement);
        var box = _this.getPlacementBox(target, pos);
        var hiddenHeight = 0;
        var hiddenWidth = 0;
        var container = containerFunction();
        if (box.top < container.top) {
          hiddenHeight = container.top - box.top;
        } else if (box.bottom > container.height) {
          hiddenHeight = box.bottom - container.height;
        }
        if (box.left < container.left) {
          hiddenWidth = container.left - box.left;
        } else if (box.right > container.width) {
          hiddenWidth = box.right - container.width;
        }
        if (hiddenHeight && !hiddenWidth) {
          hiddenWidth = 1;
        } else if (hiddenWidth && !hiddenHeight) {
          hiddenHeight = 1;
        }
        var area = target.offsetHeight * target.offsetWidth;
        var hiddenArea = hiddenHeight * hiddenWidth;
        var visibleArea = area - hiddenArea;
        var visiblePercent = visibleArea / area;
        return {
          placement,
          weight: visiblePercent
        };
      });
      weightedPlacements.sort(function(a, b) {
        return b.weight - a.weight;
      });
      return weightedPlacements[0].placement;
    };
    Position2.prototype.findBestPlacementAt = function(offset, target, placements, containerFunction) {
      var _this = this;
      if (containerFunction === void 0) {
        containerFunction = this.defaultContainerFunction.bind(this);
      }
      var positionAt = function(_, target2, placement) {
        return _this.findPositionAt(offset, target2, placement);
      };
      return this.findBestPlacement(null, target, placements, containerFunction, positionAt);
    };
    Position2.prototype.defaultContainerFunction = function() {
      return {
        // we go with window here, because that's going to be the simple/common case
        top: 0,
        left: 0,
        height: windowRef.innerHeight,
        width: windowRef.innerWidth
      };
    };
    Position2.prototype.calculatePosition = function(referenceOffset, referenceRect, target, placement) {
      if (this.positions[placement]) {
        return this.positions[placement](referenceOffset, target, referenceRect);
      }
      console.error("No function found for placement, defaulting to 0,0");
      return { left: 0, top: 0 };
    };
    return Position2;
  })()
);
var position = new Position();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-utils.mjs
var AnimationFrameServiceSingleton = class {
  constructor(ngZone) {
    this.ngZone = ngZone;
    this.frameSource = new Subject();
    this.tick = this.frameSource.asObservable();
    this.ngZone.runOutsideAngular(() => {
      this.animationFrameId = requestAnimationFrame(this.doTick.bind(this));
    });
  }
  ngOnDestroy() {
    cancelAnimationFrame(this.animationFrameId);
  }
  doTick(frame) {
    this.frameSource.next(frame);
    this.ngZone.runOutsideAngular(() => {
      requestAnimationFrame(this.doTick.bind(this));
    });
  }
};
AnimationFrameServiceSingleton.ɵfac = function AnimationFrameServiceSingleton_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || AnimationFrameServiceSingleton)(ɵɵinject(NgZone));
};
AnimationFrameServiceSingleton.ɵprov = ɵɵdefineInjectable({
  token: AnimationFrameServiceSingleton,
  factory: AnimationFrameServiceSingleton.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AnimationFrameServiceSingleton, [{
    type: Injectable
  }], function() {
    return [{
      type: NgZone
    }];
  }, null);
})();
var AnimationFrameService = class {
  constructor(singleton) {
    this.singleton = singleton;
    this.tick = from(this.singleton.tick);
  }
};
AnimationFrameService.ɵfac = function AnimationFrameService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || AnimationFrameService)(ɵɵinject(AnimationFrameServiceSingleton));
};
AnimationFrameService.ɵprov = ɵɵdefineInjectable({
  token: AnimationFrameService,
  factory: AnimationFrameService.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AnimationFrameService, [{
    type: Injectable
  }], function() {
    return [{
      type: AnimationFrameServiceSingleton
    }];
  }, null);
})();
var merge2 = (target, ...objects) => {
  for (const object of objects) {
    for (const key in object) {
      if (object.hasOwnProperty(key)) {
        if (object[key] instanceof Object) {
          if (!target[key]) {
            target[key] = {};
          }
          target[key] = merge2(target[key], object[key]);
        } else {
          target[key] = object[key];
        }
      }
    }
  }
  return target;
};
var isScrollableElement = (element) => {
  const computedStyle = getComputedStyle(element);
  return computedStyle.overflow === "auto" || computedStyle.overflow === "scroll" || computedStyle["overflow-y"] === "auto" || computedStyle["overflow-y"] === "scroll" || computedStyle["overflow-x"] === "auto" || computedStyle["overflow-x"] === "scroll";
};
var isVisibleInContainer = (element, container) => {
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  if (container.tagName === "BODY" || container.tagName === "HTML") {
    const isAboveViewport = elementRect.top < 0 && elementRect.top + element.clientHeight < 0;
    const isLeftOfViewport = elementRect.left < 0;
    const isBelowViewport = elementRect.bottom - element.clientHeight > (window.innerHeight || document.documentElement.clientHeight);
    const isRightOfViewport = elementRect.right > (window.innerWidth || document.documentElement.clientWidth);
    const isVisibleInViewport = !(isAboveViewport || isBelowViewport || isLeftOfViewport || isRightOfViewport);
    return isVisibleInViewport;
  }
  return (
    // This also accounts for partial visibility. It will still return true if the element is partially visible inside the container.
    elementRect.bottom - element.clientHeight <= containerRect.bottom + (container.offsetHeight - container.clientHeight) / 2 && elementRect.top >= -element.clientHeight
  );
};
var getScrollableParents = (node) => {
  const elements = [document.body];
  while (node.parentElement && node !== document.body) {
    if (isScrollableElement(node)) {
      elements.push(node);
    }
    node = node.parentElement;
  }
  return elements;
};
var ElementService = class {
  constructor(singleton) {
    this.singleton = singleton;
    this.tick = from(this.singleton.tick);
  }
  visibility(target, parentElement = target) {
    const scrollableParents = getScrollableParents(parentElement);
    return this.tick.pipe(map(() => {
      for (const parent of scrollableParents) {
        if (!isVisibleInContainer(target, parent)) {
          return {
            visible: false
          };
        }
      }
      return {
        visible: true
      };
    }));
  }
};
ElementService.ɵfac = function ElementService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ElementService)(ɵɵinject(AnimationFrameServiceSingleton));
};
ElementService.ɵprov = ɵɵdefineInjectable({
  token: ElementService,
  factory: ElementService.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ElementService, [{
    type: Injectable
  }], function() {
    return [{
      type: AnimationFrameServiceSingleton
    }];
  }, null);
})();
var getEventObservable = (targetElement, eventType) => {
  switch (eventType) {
    case "scroll":
    case "resize":
    case "touchstart":
    case "touchmove":
    case "touchend":
      return fromEvent(targetElement, eventType, {
        passive: true
      });
    default:
      return fromEvent(targetElement, eventType);
  }
};
var DocumentService = class {
  constructor() {
    this.globalEvents = /* @__PURE__ */ new Map();
    this.documentRef = document;
    this.subscriptions = new Subscription();
  }
  handleEvent(eventType, callback) {
    if (!this.globalEvents.has(eventType)) {
      if (this.documentRef) {
        this.globalEvents.set(eventType, getEventObservable(this.documentRef, eventType));
      } else {
        this.globalEvents.set(eventType, new Observable());
      }
    }
    const observable = this.globalEvents.get(eventType);
    this.subscriptions.add(observable.subscribe(callback));
  }
  handleClick(callback) {
    this.handleEvent("click", callback);
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.globalEvents = null;
  }
};
DocumentService.ɵfac = function DocumentService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || DocumentService)();
};
DocumentService.ɵprov = ɵɵdefineInjectable({
  token: DocumentService,
  factory: DocumentService.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DocumentService, [{
    type: Injectable
  }], null, null);
})();
var EventService = class {
  constructor(documentService) {
    this.documentService = documentService;
    this.subscriptions = new Subscription();
    this.targets = /* @__PURE__ */ new WeakMap();
  }
  on(targetElement, eventType, callback) {
    if (!this.targets.has(targetElement)) {
      this.targets.set(targetElement, /* @__PURE__ */ new Map());
    }
    const eventMap = this.targets.get(targetElement);
    if (!eventMap.has(eventType)) {
      eventMap.set(eventType, getEventObservable(targetElement, eventType));
    }
    const subscription = eventMap.get(eventType).subscribe(callback);
    this.subscriptions.add(subscription);
  }
  onDocument(eventType, callback) {
    this.documentService.handleEvent(eventType, callback);
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
};
EventService.ɵfac = function EventService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || EventService)(ɵɵinject(DocumentService));
};
EventService.ɵprov = ɵɵdefineInjectable({
  token: EventService,
  factory: EventService.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EventService, [{
    type: Injectable
  }], function() {
    return [{
      type: DocumentService
    }];
  }, null);
})();
function DOCUMENT_SERVICE_PROVIDER_FACTORY(parentService) {
  return parentService || new DocumentService();
}
var DOCUMENT_SERVICE_PROVIDER = {
  provide: DocumentService,
  deps: [[new Optional(), new SkipSelf(), DocumentService]],
  useFactory: DOCUMENT_SERVICE_PROVIDER_FACTORY
};
function ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER_FACTORY(parentService, ngZone) {
  return parentService || new AnimationFrameServiceSingleton(ngZone);
}
var ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER = {
  provide: AnimationFrameServiceSingleton,
  deps: [[new Optional(), new SkipSelf(), AnimationFrameServiceSingleton], NgZone],
  useFactory: ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER_FACTORY
};
var UtilsModule = class {
};
UtilsModule.ɵfac = function UtilsModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || UtilsModule)();
};
UtilsModule.ɵmod = ɵɵdefineNgModule({
  type: UtilsModule
});
UtilsModule.ɵinj = ɵɵdefineInjector({
  providers: [DOCUMENT_SERVICE_PROVIDER, ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER, AnimationFrameServiceSingleton, DocumentService, AnimationFrameService, ElementService, EventService]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UtilsModule, [{
    type: NgModule,
    args: [{
      providers: [DOCUMENT_SERVICE_PROVIDER, ANIMATION_FRAME_SERVICE_SINGLETON_PROVIDER, AnimationFrameServiceSingleton, DocumentService, AnimationFrameService, ElementService, EventService]
    }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-i18n.mjs
var EN = {
  "BREADCRUMB": {
    "LABEL": "Breadcrumb",
    "OVERFLOW_MENU_DESCRIPTION": "Options"
  },
  "CODE_SNIPPET": {
    "CODE_SNIPPET_TEXT": "Code Snippet Text",
    "SHOW_MORE": "Show more",
    "SHOW_LESS": "Show less",
    "SHOW_MORE_ICON": "Show more icon",
    "COPY_CODE": "Copy code",
    "COPIED": "Copied!"
  },
  "COMBOBOX": {
    "PLACEHOLDER": "Filter...",
    "CLEAR_SELECTIONS": "Clear all selected items",
    "CLEAR_SELECTED": "Clear selected item",
    "A11Y": {
      "OPEN_MENU": "Open menu",
      "CLOSE_MENU": "Close menu",
      "CLEAR_SELECTIONS": "Clear all selected items",
      "CLEAR_SELECTED": "Clear Selection"
    }
  },
  "DROPDOWN": {
    "OPEN": "Open menu",
    "SELECTED": "Selected",
    "CLEAR": "Clear all selected items",
    "FILTER": {
      "SELECTED_ONLY": "Show selected only",
      "SEARCH": "Search",
      "NO_RESULTS": "No search results",
      "RESET_SEARCH": "Reset search"
    }
  },
  "DROPDOWN_LIST": {
    "LABEL": "Listbox"
  },
  "FILE_UPLOADER": {
    "CHECKMARK": "Checkmark",
    "OPEN": "Add file",
    "REMOVE_BUTTON": "Close button"
  },
  "LOADING": {
    "TITLE": "Loading"
  },
  "MODAL": {
    "CLOSE": "Close"
  },
  "NOTIFICATION": {
    "CLOSE_BUTTON": "Close alert notification"
  },
  "NUMBER": {
    "INCREMENT": "Increment value",
    "DECREMENT": "Decrement value"
  },
  "OVERFLOW_MENU": {
    "OVERFLOW": "Overflow",
    "ICON_DESCRIPTION": "Options"
  },
  "SEARCH": {
    "LABEL": "Search",
    "PLACEHOLDER": "Search",
    "CLEAR_BUTTON": "Clear search input"
  },
  "PAGINATION": {
    "ITEMS_PER_PAGE": "Items per page:",
    "OPEN_LIST_OF_OPTIONS": "Open list of options",
    "BACKWARD": "Backward",
    "FORWARD": "Forward",
    "TOTAL_ITEMS_UNKNOWN": "{{start}}-{{end}} items",
    "TOTAL_ITEMS": "{{start}}-{{end}} of {{total}} items",
    "TOTAL_ITEM": "{{start}}-{{end}} of {{total}} item",
    "PAGE": "page",
    "OF_LAST_PAGES": "of {{last}} pages",
    "OF_LAST_PAGE": "of {{last}} page",
    "NEXT": "Next",
    "PREVIOUS": "Previous",
    "SELECT_ARIA": "Select page number"
  },
  "PROGRESS_INDICATOR": {
    "CURRENT": "Current",
    "INCOMPLETE": "Incomplete",
    "COMPLETE": "Complete",
    "INVALID": "Invalid"
  },
  "TABLE": {
    "FILTER": "Filter",
    "END_OF_DATA": "You've reached the end of your content",
    "SCROLL_TOP": "Scroll to top",
    "CHECKBOX_HEADER": "Select all rows",
    "CHECKBOX_ROW": "Select {{value}}",
    "EXPAND_BUTTON": "Expand row",
    "EXPAND_ALL_BUTTON": "Expand all rows",
    "SORT_DESCENDING": "Sort rows by this header in descending order",
    "SORT_ASCENDING": "Sort rows by this header in ascending order",
    "ROW": "row"
  },
  "TABLE_TOOLBAR": {
    "ACTION_BAR": "Table action bar",
    "BATCH_TEXT": "",
    "BATCH_TEXT_SINGLE": "1 item selected",
    "BATCH_TEXT_MULTIPLE": "{{count}} items selected",
    "CANCEL": "Cancel"
  },
  "TABS": {
    "BUTTON_ARIA_LEFT": "Go to the previous tab",
    "BUTTON_ARIA_RIGHT": "Go to the next tab",
    "HEADER_ARIA_LABEL": "List of tabs"
  },
  "TILES": {
    "TILE": "tile",
    "EXPAND": "Expand",
    "COLLAPSE": "Collapse"
  },
  "TOGGLE": {
    "OFF": "Off",
    "ON": "On"
  },
  "UI_SHELL": {
    "SKIP_TO": "Skip to content",
    "HEADER": {
      "OPEN_MENU": "Open menu",
      "CLOSE_MENU": "Close menu"
    },
    "SIDE_NAV": {
      "TOGGLE_OPEN": "Open",
      "TOGGLE_CLOSE": "Close"
    }
  }
};
var replace = (subject, variables) => subject.pipe(map((str) => {
  const keys = Object.keys(variables);
  for (const key of keys) {
    const value = variables[key];
    str = str.replace(new RegExp(`{{\\s*${key}\\s*}}`, "g"), value);
  }
  return str;
}));
var Overridable = class {
  constructor(path, i18n) {
    this.path = path;
    this.i18n = i18n;
    this.baseTranslation = this.i18n.get(this.path);
    this.isOverridden = false;
    const value = this.i18n.getValueFromPath(this.path);
    this.$override = new BehaviorSubject(value);
    this._value = value;
  }
  /**
   * The raw value of the translation. Defaults to the string value, but will return the value passed to `override`
   *
   * @readonly
   */
  get value() {
    return this._value;
  }
  set value(v) {
    this.override(v);
  }
  /**
   * The translation subject. Returns either a stream of overridden values, or our base translation values.
   *
   * @readonly
   */
  get subject() {
    return iif(() => this.isOverridden, this.$override, this.baseTranslation);
  }
  /**
   * Takes a string or an `Observable` that emits strings.
   * Overrides the value provided by the `I18n` service.
   */
  override(value) {
    this.isOverridden = true;
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
    this._value = value;
    if (isObservable(value)) {
      this.subscription = value.subscribe((v) => {
        this.$override.next(v);
      });
    } else {
      this.$override.next(value);
    }
  }
};
var I18n = class {
  constructor() {
    this.translationStrings = EN;
    this.translations = /* @__PURE__ */ new Map();
    this.locale = new BehaviorSubject("en");
  }
  /**
   * Sets the locale and optionally the translation strings. Locale is used by components that
   * are already locale aware (datepicker for example) while the translation strings are used
   * for components that are not.
   *
   * Locales set here will override locales/languages set in components
   * @param language an ISO 639-1 language code - https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
   * @param strings an object of strings, optional
   */
  setLocale(language, strings) {
    this.locale.next(language);
    if (strings) {
      this.set(strings);
    }
  }
  /**
   * Returns the current locale
   */
  getLocale() {
    return this.locale.value;
  }
  /**
   * Returns an observable that resolves to the current locale, and will update when changed
   */
  getLocaleObservable() {
    return this.locale.asObservable();
  }
  /**
   * Set/update the translations from an object. Also notifies all participating components of the update.
   *
   * @param strings an object of strings, should follow the same format as src/i18n/en.json
   */
  set(strings) {
    this.translationStrings = merge2({}, EN, strings);
    const translations = Array.from(this.translations);
    for (const [path, subject] of translations) {
      subject.next(this.getValueFromPath(path));
    }
  }
  /**
   * When a path is specified returns an observable that will resolve to the translation string value.
   *
   * Returns the full translations object if path is not specified.
   *
   * @param path optional, looks like `"NOTIFICATION.CLOSE_BUTTON"`
   */
  get(path) {
    if (!path) {
      return this.translationStrings;
    }
    return this.getSubject(path);
  }
  /**
   * Returns all descendents of some path fragment as an object.
   *
   * @param partialPath a path fragment, for example `"NOTIFICATION"`
   */
  getMultiple(partialPath) {
    const values = this.getValueFromPath(partialPath);
    const subjects = {};
    for (const key of Object.keys(values)) {
      if (values[key] === Object(values[key])) {
        subjects[key] = this.getMultiple(`${partialPath}.${key}`);
      } else {
        subjects[key] = this.getSubject(`${partialPath}.${key}`);
      }
    }
    return subjects;
  }
  /**
   * Returns an instance of `Overridable` that can be used to optionally override the value provided by `I18n`
   * @param path looks like `"NOTIFICATION.CLOSE_BUTTON"`
   */
  getOverridable(path) {
    return new Overridable(path, this);
  }
  /**
   * Takes the `Observable` returned from `i18n.get` and an object of variables to replace.
   *
   * The keys specify the variable name in the string.
   *
   * Example:
   * ```
   * service.set({ "TEST": "{{foo}} {{bar}}" });
   *
   * service.replace(service.get("TEST"), { foo: "test", bar: "asdf" })
   * ```
   *
   * Produces: `"test asdf"`
   *
   * @param subject the translation to replace variables on
   * @param variables object of variables to replace
   */
  replace(subject, variables) {
    return replace(subject, variables);
  }
  /**
   * Trys to resolve a value from the provided path.
   *
   * @param path looks like `"NOTIFICATION.CLOSE_BUTTON"`
   */
  getValueFromPath(path) {
    let value = this.translationStrings;
    for (const segment of path.split(".")) {
      if (value[segment] !== void 0 && value[segment] !== null) {
        value = value[segment];
      } else {
        throw new Error(`no key ${segment} at ${path}`);
      }
    }
    return value;
  }
  /**
   * Helper method that returns an observable from the internal cache based on the path
   *
   * @param path looks like `"NOTIFICATION.CLOSE_BUTTON"`
   */
  getSubject(path) {
    try {
      const value = this.getValueFromPath(path);
      if (this.translations.has(path)) {
        return this.translations.get(path);
      }
      const translation = new BehaviorSubject(value);
      this.translations.set(path, translation);
      return translation;
    } catch (error) {
      console.error(error);
    }
  }
};
I18n.ɵfac = function I18n_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || I18n)();
};
I18n.ɵprov = ɵɵdefineInjectable({
  token: I18n,
  factory: I18n.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(I18n, [{
    type: Injectable
  }], null, null);
})();
var ReplacePipe = class {
  transform(value, variables) {
    return replace(value, variables);
  }
};
ReplacePipe.ɵfac = function ReplacePipe_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ReplacePipe)();
};
ReplacePipe.ɵpipe = ɵɵdefinePipe({
  name: "i18nReplace",
  type: ReplacePipe,
  pure: true,
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplacePipe, [{
    type: Pipe,
    args: [{
      name: "i18nReplace"
    }]
  }], null, null);
})();
function I18N_SERVICE_PROVIDER_FACTORY(parentService) {
  return parentService || new I18n();
}
var I18N_SERVICE_PROVIDER = {
  provide: I18n,
  deps: [[new Optional(), new SkipSelf(), I18n]],
  useFactory: I18N_SERVICE_PROVIDER_FACTORY
};
var I18nModule = class {
};
I18nModule.ɵfac = function I18nModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || I18nModule)();
};
I18nModule.ɵmod = ɵɵdefineNgModule({
  type: I18nModule,
  declarations: [ReplacePipe],
  exports: [ReplacePipe]
});
I18nModule.ɵinj = ɵɵdefineInjector({
  providers: [I18n, I18N_SERVICE_PROVIDER]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(I18nModule, [{
    type: NgModule,
    args: [{
      declarations: [ReplacePipe],
      exports: [ReplacePipe],
      providers: [I18n, I18N_SERVICE_PROVIDER]
    }]
  }], null, null);
})();

export {
  merge2 as merge,
  I18n,
  I18nModule
};
//# sourceMappingURL=chunk-4Y3KUSI5.js.map
