import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Project } from '../services/projects.service';

// Carbon Design System imports
import { ButtonModule } from 'carbon-components-angular/button';
import { TilesModule } from 'carbon-components-angular/tiles';

@Component({
  selector: 'app-project-card',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TilesModule
  ],
  template: `
    <cds-tile class="project-card">
      <div class="project-card-content">
        <div class="project-info">
          <h3 class="project-title">{{ project.title }}</h3>
          <span class="project-type">{{ project.type }}</span>
        </div>
        <div class="project-actions">
          <button
            cdsButton="primary"
            size="sm"
            (click)="onEnter()">
            Enter
          </button>
        </div>
      </div>
    </cds-tile>
  `,
  styles: [`
    .project-card {
      margin-bottom: 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .project-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .project-card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem;
    }
    
    .project-info {
      flex: 1;
    }
    
    .project-title {
      margin: 0 0 0.25rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #161616;
    }
    
    .project-type {
      font-size: 0.875rem;
      color: #6f6f6f;
      text-transform: uppercase;
      font-weight: 500;
    }
    
    .project-actions {
      margin-left: 1rem;
    }
  `]
})
export class ProjectCardComponent {
  @Input({ required: true }) project!: Project;
  @Output() enter = new EventEmitter<Project>();

  onEnter() {
    this.enter.emit(this.project);
  }
}
