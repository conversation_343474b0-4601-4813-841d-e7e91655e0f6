{"version": 3, "sources": ["src/app/projects/projects.page.ts"], "sourcesContent": ["\n    .projects-container {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n      min-height: 100vh;\n    }\n    \n    .projects-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n      padding-bottom: 1rem;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    \n    .projects-header h1 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: 400;\n      color: #161616;\n    }\n    \n    .header-actions {\n      display: flex;\n      gap: 1rem;\n    }\n    \n    .projects-content {\n      padding: 1rem 0;\n    }\n    \n    .loading-state {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 4rem 2rem;\n      text-align: center;\n    }\n    \n    .loading-state p {\n      margin-top: 1rem;\n      color: #6f6f6f;\n      font-size: 1.125rem;\n    }\n    \n    .empty-state {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      min-height: 400px;\n      padding: 2rem;\n    }\n    \n    .empty-state-content {\n      text-align: center;\n      max-width: 400px;\n    }\n    \n    .empty-state-content h2 {\n      margin: 0 0 1rem 0;\n      font-size: 1.5rem;\n      font-weight: 400;\n      color: #161616;\n    }\n    \n    .empty-state-content p {\n      margin: 0 0 2rem 0;\n      color: #6f6f6f;\n      font-size: 1rem;\n      line-height: 1.5;\n    }\n    \n    .projects-list {\n      display: grid;\n      gap: 1rem;\n      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n    }\n    \n    @media (max-width: 768px) {\n      .projects-container {\n        padding: 1rem;\n      }\n      \n      .projects-header {\n        flex-direction: column;\n        gap: 1rem;\n        align-items: stretch;\n      }\n      \n      .header-actions {\n        justify-content: center;\n      }\n      \n      .projects-list {\n        grid-template-columns: 1fr;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAGF,CATA,gBASA;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,KAAA;AACA,cAAA;;AAGF,CATA,cASA;AACE,cAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,aAAA;;AAGF,CALA,oBAKA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAZA,oBAYA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAjFF;AAkFI,aAAA;;AAGF,GA9EF;AA+EI,oBAAA;AACA,SAAA;AACA,iBAAA;;AAGF,GApEF;AAqEI,qBAAA;;AAGF,GArBF;AAsBI,2BAAA;;;", "names": []}