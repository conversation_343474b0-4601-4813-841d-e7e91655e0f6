import {
  I18n,
  I18nModule
} from "./chunk-4Y3KUSI5.js";
import {
  CommonModule,
  NgClass
} from "./chunk-Y7V6UAPQ.js";
import {
  Component,
  HostBinding,
  Input,
  NgModule,
  setClassMetadata,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnamespaceSVG,
  ɵɵproperty,
  ɵɵpureFunction3,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-6YWT35PS.js";
import "./chunk-OSECCFIU.js";
import "./chunk-IONO6HLE.js";
import "./chunk-PHHPLELC.js";

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-loading.mjs
var _c0 = (a0, a1, a2) => ({
  "cds--loading--small": a0,
  "cds--loading--stop": a1,
  "cds--loading-overlay--stop": a2
});
var Loading = class {
  constructor(i18n) {
    this.i18n = i18n;
    this.title = this.i18n.get().LOADING.TITLE;
    this.isActive = true;
    this.size = "normal";
    this.overlay = false;
  }
};
Loading.ɵfac = function Loading_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Loading)(ɵɵdirectiveInject(I18n));
};
Loading.ɵcmp = ɵɵdefineComponent({
  type: Loading,
  selectors: [["cds-loading"], ["ibm-loading"]],
  hostVars: 2,
  hostBindings: function Loading_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--loading-overlay", ctx.overlay);
    }
  },
  inputs: {
    title: "title",
    isActive: "isActive",
    size: "size",
    overlay: "overlay"
  },
  standalone: false,
  decls: 5,
  vars: 6,
  consts: [[1, "cds--loading", 3, "ngClass"], ["viewBox", "0 0 100 100", 1, "cds--loading__svg"], ["cx", "50%", "cy", "50%", "r", "44", 1, "cds--loading__stroke"]],
  template: function Loading_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵelementStart(0, "div", 0);
      ɵɵnamespaceSVG();
      ɵɵelementStart(1, "svg", 1)(2, "title");
      ɵɵtext(3);
      ɵɵelementEnd();
      ɵɵelement(4, "circle", 2);
      ɵɵelementEnd()();
    }
    if (rf & 2) {
      ɵɵproperty("ngClass", ɵɵpureFunction3(2, _c0, ctx.size === "sm", !ctx.isActive && !ctx.overlay, !ctx.isActive && ctx.overlay));
      ɵɵadvance(3);
      ɵɵtextInterpolate(ctx.title);
    }
  },
  dependencies: [NgClass],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Loading, [{
    type: Component,
    args: [{
      selector: "cds-loading, ibm-loading",
      template: `
		<div
			[ngClass]="{
				'cds--loading--small': size === 'sm',
				'cds--loading--stop': !isActive && !overlay,
				'cds--loading-overlay--stop': !isActive && overlay
			}"
			class="cds--loading">
			<svg class="cds--loading__svg" viewBox="0 0 100 100">
				<title>{{title}}</title>
				<circle class="cds--loading__stroke" cx="50%" cy="50%" r="44" />
			</svg>
		</div>
	`
    }]
  }], function() {
    return [{
      type: I18n
    }];
  }, {
    title: [{
      type: Input
    }],
    isActive: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    overlay: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["class.cds--loading-overlay"]
    }]
  });
})();
var LoadingModule = class {
};
LoadingModule.ɵfac = function LoadingModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LoadingModule)();
};
LoadingModule.ɵmod = ɵɵdefineNgModule({
  type: LoadingModule,
  declarations: [Loading],
  imports: [CommonModule, I18nModule],
  exports: [Loading]
});
LoadingModule.ɵinj = ɵɵdefineInjector({
  imports: [CommonModule, I18nModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoadingModule, [{
    type: NgModule,
    args: [{
      declarations: [Loading],
      exports: [Loading],
      imports: [CommonModule, I18nModule]
    }]
  }], null, null);
})();
export {
  Loading,
  LoadingModule
};
//# sourceMappingURL=carbon-components-angular_loading.js.map
